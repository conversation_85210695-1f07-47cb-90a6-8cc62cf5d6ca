import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart';

import '/constants/envs.dart' show envApiUrlAstroxOp;
import '/models/business.dart';
import '/provider/api.dart' hide apiServiceProvider;

final class AstroxApi {
  AstroxApi(this.ref);

  final Ref ref;

  late final http = ref.read(httpProvider).clone(options: BaseOptions(baseUrl: envApiUrlAstroxOp));

  Future<List<Network>> getNetworks({
    CancelToken? cancelToken,
  }) async {
    final res = await http
        .get(
          '/multichain/networkConfig',
          cancelToken: cancelToken,
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => Network.fromJson(v.asJson()));
    return rep.list;
  }

  Future<AstroxPaged<Token>> getTokens({
    required String blockChain,
    required int pageNum,
    required int pageSize,
  }) async {
    final res = await http
        .post(
          '/api/queryTokenList',
          data: {
            'blockChain': blockChain,
            'pageNum': pageNum,
            'pageSize': pageSize,
          },
        )
        .retry();
    final rep = Rep.fromJson(
      res.data,
      (v) => AstroxPaged.fromJson(v.asJson(), (v) => Token.fromJson(v.asJson())),
    );
    return rep.data;
  }
}

abstract final class AstroxApiErrorCode {}
