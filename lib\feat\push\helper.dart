
// Push相关功能的简单使用示例

/*
使用方法：

1. 在应用启动时，推送服务会自动初始化
2. 监听推送状态：
   final pushState = ref.watch(pushNotificationProvider);
   
3. 检查权限：
   ref.read(pushNotificationProvider.notifier).checkPermission();
   
4. 打开设置：
   ref.read(pushNotificationProvider.notifier).openAppSettings();
   
5. 手动刷新Token：
   ref.read(pushNotificationProvider.notifier).refreshToken();

6. 获取FCM Token：
   final tokenAsync = ref.watch(fcmTokenProvider);
   
7. 获取服务器端Token：
   final serverTokenAsync = ref.watch(fcmTokenByServerProvider);

注意：
- 推送服务会在应用启动时自动初始化
- Token变化时会自动同步到服务器
- 权限变化需要手动检查
*/

import 'dart:io';
import 'package:flutter/foundation.dart';

/// 推送服务工具类
class PushHelper {
  /// 获取当前平台名称
  static String getCurrentPlatformName() {
    if (kIsWeb) {
      return 'Web';
    } else if (Platform.isIOS) {
      return 'iOS';
    } else if (Platform.isAndroid) {
      return 'Android';
    } else {
      return 'Unknown';
    }
  }

  /// 获取当前平台的FCM字段名
  static String getFCMFieldName() {
    if (kIsWeb) {
      return 'fcmWeb';
    } else if (Platform.isIOS) {
      return 'fcmIos';
    } else if (Platform.isAndroid) {
      return 'fcmAndroid';
    } else {
      return 'fcmAndroid'; // 默认
    }
  }

  /// 格式化Token显示（只显示前后几位）
  static String formatTokenForDisplay(String? token, {int prefixLength = 8, int suffixLength = 8}) {
    if (token == null || token.isEmpty) {
      return '未获取';
    }
    
    if (token.length <= prefixLength + suffixLength + 3) {
      return token;
    }
    
    return '${token.substring(0, prefixLength)}...${token.substring(token.length - suffixLength)}';
  }

  /// 检查Token是否有效
  static bool isValidToken(String? token) {
    return token != null && token.isNotEmpty && token.length > 20;
  }

  /// 生成调试信息
  static Map<String, dynamic> generateDebugInfo({
    String? currentToken,
    String? lastSentToken,
    bool? hasPermission,
    bool? isInitialized,
  }) {
    return {
      'platform': getCurrentPlatformName(),
      'fcmField': getFCMFieldName(),
      'currentToken': formatTokenForDisplay(currentToken),
      'lastSentToken': formatTokenForDisplay(lastSentToken),
      'hasPermission': hasPermission ?? false,
      'isInitialized': isInitialized ?? false,
      'tokenValid': isValidToken(currentToken),
      'needsUpdate': currentToken != lastSentToken,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
