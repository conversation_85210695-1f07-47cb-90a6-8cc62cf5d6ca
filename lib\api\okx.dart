import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_misc/me_misc.dart';

import '/provider/api.dart';

part 'okx.freezed.dart';

part 'okx.g.dart';

final class OkxApi {
  OkxApi(this.ref);

  final Ref ref;

  late final http = ref.read(httpProvider).clone(options: BaseOptions(baseUrl: 'https://www.okx.com'));

  Future<List<OkxTicker>> tickerPrice(String instId) async {
    final res = await http
        .get(
          '/api/v5/market/ticker',
          queryParameters: {'instId': instId},
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => OkxTicker.fromJson(v.asJson()));
    return rep.list;
  }

  Future<List<OkxTicker>> tickersPrice(String? instType, String instId) async {
    final res = await http
        .get(
          '/api/v5/market/tickers',
          queryParameters: {
            'instType': ?instType,
            'instId': instId,
          },
        )
        .retry();
    final rep = ListRep.fromJson(res.data, (v) => OkxTicker.fromJson(v.asJson()));
    return rep.list;
  }
}

@freezed
sealed class OkxTicker with _$OkxTicker {
  const factory OkxTicker({
    @JsonKey(name: 'instId') required String instId,
    @JsonKey(name: 'last') required double last,
    @JsonKey(name: 'askPx') required double askPx,
    @JsonKey(name: 'askSz') required double askSz,
    @JsonKey(name: 'bidPx') required double bidPx,
    @JsonKey(name: 'bidSz') required double bidSz,
  }) = _OkxTicker;

  factory OkxTicker.fromJson(Map<String, Object?> json) => _$OkxTickerFromJson(json);
}
