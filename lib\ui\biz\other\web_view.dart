import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '/feat/bridge/module/card3.dart' show defaultCard3Bridge;
import '/feat/link/helper.dart' show AppLinkHelper;

@FFRoute(name: '/webview')
class WebViewPage extends ConsumerStatefulWidget {
  const WebViewPage({
    super.key,
    required this.url,
    this.title,
  });

  final String url;
  final String? title;

  @override
  ConsumerState<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends ConsumerState<WebViewPage> with WidgetsBindingObserver {
  InAppWebViewController? _webViewController;
  double progress = 0;
  bool _isLoading = true;
  String? _errorMessage;

  // 添加超时定时器
  Timer? _loadingTimeout;

  // 添加URL历史记录，防止循环加载
  final Set<String> _loadedUrls = <String>{};

  // 添加应用生命周期状态追踪
  bool _isAppInBackground = false;
  DateTime? _backgroundTime;
  String? _currentUrl;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadedUrls.add(widget.url); // 记录初始URL
    _currentUrl = widget.url;

    // 设置30秒超时
    _loadingTimeout = Timer(const Duration(seconds: 30), () {
      if (_isLoading) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Page load timeout (30s)';
        });
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _loadingTimeout?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        if (!_isAppInBackground) {
          // 避免重复设置
          _isAppInBackground = true;
          _backgroundTime = DateTime.now();
        }

      case AppLifecycleState.resumed:
        if (_isAppInBackground && _backgroundTime != null) {
          final backgroundDuration = DateTime.now().difference(_backgroundTime!);
          // 缩短时间阈值到5秒，让恢复更及时
          if (backgroundDuration.inSeconds >= 60) {
            _checkWebViewStateAfterResume();
          }

          // 重置状态
          _isAppInBackground = false;
          _backgroundTime = null;
        } else {
          // 确保状态正确重置
          _isAppInBackground = false;
          _backgroundTime = null;
        }
      default:
        break;
    }
  }

  // 检查WebView状态并在必要时重新加载
  Future<void> _checkWebViewStateAfterResume() async {
    if (!mounted || _webViewController == null) {
      return;
    }
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      if (!mounted || _webViewController == null) {
        return;
      }
      await _reloadWebView();
    } catch (e) {
      safeSetState(() {
        _isLoading = false;
        _errorMessage = 'WebView recovery failed: $e';
      });
      rethrow;
    }
  }

  // 重新加载WebView
  Future<void> _reloadWebView() async {
    if (!mounted || _webViewController == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 添加5秒超时
      await _performReload().timeout(const Duration(seconds: 5));
    } catch (e) {
      safeSetState(() {
        _isLoading = false;
        _errorMessage = 'Failed to reload page: $e';
      });
      rethrow;
    }
  }

  // 执行实际的重新加载操作
  Future<void> _performReload() async {
    final controller = _webViewController;
    if (controller == null) {
      return;
    }
    if (_currentUrl != null) {
      await controller.loadUrl(
        urlRequest: URLRequest(
          url: WebUri(_currentUrl!),
          headers: {
            'User-Agent': 'card3/1.0',
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
          },
        ),
      );
    } else {
      await controller.reload();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black, // 设置背景色为黑色，便于发现边距问题
      appBar: AppBar(
        backgroundColor: ColorName.backgroundColorDark,
        title: Text(
          widget.title ?? '',
          style: const TextStyle(color: Colors.white),
        ),
        leading: const AppBackButton(),
        actions: [
          // 添加手动刷新按钮
          IconButton(
            icon: const Icon(
              Icons.refresh,
              color: Colors.white,
            ),
            onPressed: () async {
              await _reloadWebView();
            },
            tooltip: 'Refresh page',
          ),
        ],
        elevation: 0, // 移除阴影
      ),
      body: _errorMessage != null
          ? _buildErrorWidget()
          : SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Stack(
                children: [
                  // 确保WebView完全填充容器
                  Positioned.fill(
                    child: InAppWebView(
                      initialSettings: InAppWebViewSettings(
                        allowsInlineMediaPlayback: true,
                        isInspectable: !Release.sealed,
                        javaScriptEnabled: true,
                        // 允许跨域访问
                        allowUniversalAccessFromFileURLs: true,
                        applicationNameForUserAgent: 'card3/1.0',
                        // 允许文件访问
                        allowFileAccessFromFileURLs: true,
                        // 启用缓存以提高性能和减少重新加载
                        cacheEnabled: true,
                        // 支持多窗口
                        supportMultipleWindows: true,
                        // 添加超时设置
                        resourceCustomSchemes: ['card3'],
                        // 禁用自动播放
                        mediaPlaybackRequiresUserGesture: false,
                        // 禁用缩放
                        builtInZoomControls: false,
                        displayZoomControls: false,
                        // 设置视口
                        useWideViewPort: true,
                        loadWithOverviewMode: true,
                        // 剪贴板相关设置
                        javaScriptCanOpenWindowsAutomatically: true,
                        domStorageEnabled: true,
                        databaseEnabled: true,
                        // 允许剪贴板访问
                        allowsLinkPreview: true,
                        // iOS特定设置
                        allowsBackForwardNavigationGestures: true,
                        // Android特定设置
                        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                        // 防止WebView被系统回收的设置
                        incognito: false,
                        // 启用硬件加速
                        hardwareAcceleration: true,
                        // 启用剪贴板API
                        // userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1 card3/1.0',
                      ),
                      initialUrlRequest: URLRequest(
                        url: WebUri(widget.url),
                        headers: {
                          'User-Agent': 'card3/1.0',
                        },
                      ),
                      // 添加页面开始加载回调
                      onLoadStart: (controller, url) {
                        _currentUrl = url?.toString() ?? _currentUrl;
                        setState(() {
                          _isLoading = true;
                          _errorMessage = null;
                        });
                      },
                      // 添加导航决策处理，拦截自定义URL scheme
                      shouldOverrideUrlLoading: (controller, navigationAction) async {
                        final uri = navigationAction.request.url;
                        if (uri == null) {
                          return NavigationActionPolicy.ALLOW;
                        }

                        // 处理card3://协议
                        if (uri.scheme == 'card3') {
                          // 解析card3链接的路径
                          final path = uri.path.replaceFirst('/', '');

                          if (path == 'webview') {
                            // 对于webview类型的链接，直接在当前WebView中加载目标URL，避免循环
                            final targetUrl = uri.queryParameters['url'];
                            if (targetUrl != null) {
                              // 检查是否已经加载过这个URL，防止循环
                              if (_loadedUrls.contains(targetUrl)) {
                                return NavigationActionPolicy.CANCEL;
                              }
                              _loadedUrls.add(targetUrl); // 记录新URL

                              // 直接在当前WebView中加载目标URL
                              await controller.loadUrl(urlRequest: URLRequest(url: WebUri(targetUrl)));
                              return NavigationActionPolicy.CANCEL;
                            }
                          } else {
                            // 对于其他类型的card3链接（如active_card），使用AppLinkHelper处理
                            if (AppLinkHelper.handleUri(uri)) {
                              // 如果成功处理，阻止WebView加载
                              return NavigationActionPolicy.CANCEL;
                            }
                          }
                        }

                        // 对于其他协议，允许正常加载
                        return NavigationActionPolicy.ALLOW;
                      },
                      // 添加加载错误处理
                      onReceivedError: (controller, request, error) {
                        _loadingTimeout?.cancel();
                        setState(() {
                          _isLoading = false;
                          _errorMessage = 'Failed to load page: ${error.description} (Code: ${error.type})';
                        });
                      },
                      // 添加HTTP错误处理
                      onReceivedHttpError: (controller, request, errorResponse) {
                        // 只有在严重错误时才显示错误页面
                        if (errorResponse.statusCode != null && errorResponse.statusCode! >= 500) {
                          _loadingTimeout?.cancel();
                          setState(() {
                            _isLoading = false;
                            _errorMessage = 'HTTP Error: ${errorResponse.statusCode} - ${errorResponse.reasonPhrase}';
                          });
                        }
                      },
                      onLoadStop: (controller, url) async {
                        _loadingTimeout?.cancel();
                        setState(() {
                          _isLoading = false;
                        });

                        // 页面加载完成后再注入，确保DOM已完全加载
                        final token = BoxService.getToken() ?? '';
                        await controller.evaluateJavascript(
                          source:
                              '''
                          // 设置token到全局变量
                          window.card3Token = `${Uri.encodeComponent(token)}`;
                          
                          // 添加全局函数供网页调用
                          window.getCardToken = function() {
                            return window.card3Token;
                          };
                          
                          
                          // 触发自定义事件通知网页
                          document.dispatchEvent(new CustomEvent('tokenReady', {
                            detail: { token: window.card3Token }
                          }));
                          
                        ''',
                        );
                      },
                      onProgressChanged: (controller, progress) {
                        setState(() {
                          this.progress = progress / 100;
                        });
                      },
                      // 添加渲染进程不响应处理
                      onRenderProcessUnresponsive: (controller, url) async {
                        // 自动重新加载
                        await _reloadWebView();
                        return null;
                      },
                      // 添加渲染进程响应恢复处理
                      onRenderProcessResponsive: (controller, url) async {
                        return null;
                      },
                      // 添加控制器准备回调
                      onWebViewCreated: (controller) {
                        _webViewController = controller;

                        // 注入bridge
                        defaultCard3Bridge.inject(context, ref, controller);
                      },
                    ),
                  ),
                  // 显示加载进度条
                  if (_isLoading || progress < 1.0)
                    Positioned(
                      top: 0,
                      left: 0,
                      right: 0,
                      child: LinearProgressIndicator(
                        value: progress,
                        backgroundColor: Colors.grey[300],
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),

                  // 调试信息显示 - 仅在Debug模式下显示
                  if (kDebugMode)
                    Positioned(
                      bottom: 10,
                      left: 10,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              'Debug Info:',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Background: $_isAppInBackground',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                            if (_backgroundTime != null)
                              Text(
                                'Background Time: ${DateTime.now().difference(_backgroundTime!).inSeconds}s',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                              ),
                            Text(
                              'Loading: $_isLoading',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load page',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: const TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _errorMessage = null;
                      _isLoading = true;
                    });
                    // 重新加载页面
                    _webViewController?.reload();
                  },
                  child: const Text('Retry'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
