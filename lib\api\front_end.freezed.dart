// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'front_end.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FrontEndGitHubContributionCollection {
  @JsonKey(name: 'contributionCalendar')
  FrontEndGitHubContributionCalendar get calendar;

  /// Create a copy of FrontEndGitHubContributionCollection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FrontEndGitHubContributionCollectionCopyWith<
    FrontEndGitHubContributionCollection
  >
  get copyWith =>
      _$FrontEndGitHubContributionCollectionCopyWithImpl<
        FrontEndGitHubContributionCollection
      >(this as FrontEndGitHubContributionCollection, _$identity);

  /// Serializes this FrontEndGitHubContributionCollection to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FrontEndGitHubContributionCollection &&
            (identical(other.calendar, calendar) ||
                other.calendar == calendar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, calendar);

  @override
  String toString() {
    return 'FrontEndGitHubContributionCollection(calendar: $calendar)';
  }
}

/// @nodoc
abstract mixin class $FrontEndGitHubContributionCollectionCopyWith<$Res> {
  factory $FrontEndGitHubContributionCollectionCopyWith(
    FrontEndGitHubContributionCollection value,
    $Res Function(FrontEndGitHubContributionCollection) _then,
  ) = _$FrontEndGitHubContributionCollectionCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'contributionCalendar')
    FrontEndGitHubContributionCalendar calendar,
  });

  $FrontEndGitHubContributionCalendarCopyWith<$Res> get calendar;
}

/// @nodoc
class _$FrontEndGitHubContributionCollectionCopyWithImpl<$Res>
    implements $FrontEndGitHubContributionCollectionCopyWith<$Res> {
  _$FrontEndGitHubContributionCollectionCopyWithImpl(this._self, this._then);

  final FrontEndGitHubContributionCollection _self;
  final $Res Function(FrontEndGitHubContributionCollection) _then;

  /// Create a copy of FrontEndGitHubContributionCollection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? calendar = null}) {
    return _then(
      _self.copyWith(
        calendar: null == calendar
            ? _self.calendar
            : calendar // ignore: cast_nullable_to_non_nullable
                  as FrontEndGitHubContributionCalendar,
      ),
    );
  }

  /// Create a copy of FrontEndGitHubContributionCollection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FrontEndGitHubContributionCalendarCopyWith<$Res> get calendar {
    return $FrontEndGitHubContributionCalendarCopyWith<$Res>(_self.calendar, (
      value,
    ) {
      return _then(_self.copyWith(calendar: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _FrontEndGitHubContributionCollection
    implements FrontEndGitHubContributionCollection {
  const _FrontEndGitHubContributionCollection({
    @JsonKey(name: 'contributionCalendar') required this.calendar,
  });
  factory _FrontEndGitHubContributionCollection.fromJson(
    Map<String, dynamic> json,
  ) => _$FrontEndGitHubContributionCollectionFromJson(json);

  @override
  @JsonKey(name: 'contributionCalendar')
  final FrontEndGitHubContributionCalendar calendar;

  /// Create a copy of FrontEndGitHubContributionCollection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FrontEndGitHubContributionCollectionCopyWith<
    _FrontEndGitHubContributionCollection
  >
  get copyWith =>
      __$FrontEndGitHubContributionCollectionCopyWithImpl<
        _FrontEndGitHubContributionCollection
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FrontEndGitHubContributionCollectionToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FrontEndGitHubContributionCollection &&
            (identical(other.calendar, calendar) ||
                other.calendar == calendar));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, calendar);

  @override
  String toString() {
    return 'FrontEndGitHubContributionCollection(calendar: $calendar)';
  }
}

/// @nodoc
abstract mixin class _$FrontEndGitHubContributionCollectionCopyWith<$Res>
    implements $FrontEndGitHubContributionCollectionCopyWith<$Res> {
  factory _$FrontEndGitHubContributionCollectionCopyWith(
    _FrontEndGitHubContributionCollection value,
    $Res Function(_FrontEndGitHubContributionCollection) _then,
  ) = __$FrontEndGitHubContributionCollectionCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'contributionCalendar')
    FrontEndGitHubContributionCalendar calendar,
  });

  @override
  $FrontEndGitHubContributionCalendarCopyWith<$Res> get calendar;
}

/// @nodoc
class __$FrontEndGitHubContributionCollectionCopyWithImpl<$Res>
    implements _$FrontEndGitHubContributionCollectionCopyWith<$Res> {
  __$FrontEndGitHubContributionCollectionCopyWithImpl(this._self, this._then);

  final _FrontEndGitHubContributionCollection _self;
  final $Res Function(_FrontEndGitHubContributionCollection) _then;

  /// Create a copy of FrontEndGitHubContributionCollection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? calendar = null}) {
    return _then(
      _FrontEndGitHubContributionCollection(
        calendar: null == calendar
            ? _self.calendar
            : calendar // ignore: cast_nullable_to_non_nullable
                  as FrontEndGitHubContributionCalendar,
      ),
    );
  }

  /// Create a copy of FrontEndGitHubContributionCollection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FrontEndGitHubContributionCalendarCopyWith<$Res> get calendar {
    return $FrontEndGitHubContributionCalendarCopyWith<$Res>(_self.calendar, (
      value,
    ) {
      return _then(_self.copyWith(calendar: value));
    });
  }
}

/// @nodoc
mixin _$FrontEndGitHubContributionCalendar {
  @JsonKey(name: 'totalContributions')
  int get total;
  @JsonKey(name: 'weeks')
  List<FrontEndGitHubContributionDays> get weeks;

  /// Create a copy of FrontEndGitHubContributionCalendar
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FrontEndGitHubContributionCalendarCopyWith<
    FrontEndGitHubContributionCalendar
  >
  get copyWith =>
      _$FrontEndGitHubContributionCalendarCopyWithImpl<
        FrontEndGitHubContributionCalendar
      >(this as FrontEndGitHubContributionCalendar, _$identity);

  /// Serializes this FrontEndGitHubContributionCalendar to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FrontEndGitHubContributionCalendar &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other.weeks, weeks));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    total,
    const DeepCollectionEquality().hash(weeks),
  );

  @override
  String toString() {
    return 'FrontEndGitHubContributionCalendar(total: $total, weeks: $weeks)';
  }
}

/// @nodoc
abstract mixin class $FrontEndGitHubContributionCalendarCopyWith<$Res> {
  factory $FrontEndGitHubContributionCalendarCopyWith(
    FrontEndGitHubContributionCalendar value,
    $Res Function(FrontEndGitHubContributionCalendar) _then,
  ) = _$FrontEndGitHubContributionCalendarCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'totalContributions') int total,
    @JsonKey(name: 'weeks') List<FrontEndGitHubContributionDays> weeks,
  });
}

/// @nodoc
class _$FrontEndGitHubContributionCalendarCopyWithImpl<$Res>
    implements $FrontEndGitHubContributionCalendarCopyWith<$Res> {
  _$FrontEndGitHubContributionCalendarCopyWithImpl(this._self, this._then);

  final FrontEndGitHubContributionCalendar _self;
  final $Res Function(FrontEndGitHubContributionCalendar) _then;

  /// Create a copy of FrontEndGitHubContributionCalendar
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? total = null, Object? weeks = null}) {
    return _then(
      _self.copyWith(
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        weeks: null == weeks
            ? _self.weeks
            : weeks // ignore: cast_nullable_to_non_nullable
                  as List<FrontEndGitHubContributionDays>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _FrontEndGitHubContributionCalendar
    implements FrontEndGitHubContributionCalendar {
  const _FrontEndGitHubContributionCalendar({
    @JsonKey(name: 'totalContributions') required this.total,
    @JsonKey(name: 'weeks')
    required final List<FrontEndGitHubContributionDays> weeks,
  }) : _weeks = weeks;
  factory _FrontEndGitHubContributionCalendar.fromJson(
    Map<String, dynamic> json,
  ) => _$FrontEndGitHubContributionCalendarFromJson(json);

  @override
  @JsonKey(name: 'totalContributions')
  final int total;
  final List<FrontEndGitHubContributionDays> _weeks;
  @override
  @JsonKey(name: 'weeks')
  List<FrontEndGitHubContributionDays> get weeks {
    if (_weeks is EqualUnmodifiableListView) return _weeks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weeks);
  }

  /// Create a copy of FrontEndGitHubContributionCalendar
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FrontEndGitHubContributionCalendarCopyWith<
    _FrontEndGitHubContributionCalendar
  >
  get copyWith =>
      __$FrontEndGitHubContributionCalendarCopyWithImpl<
        _FrontEndGitHubContributionCalendar
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FrontEndGitHubContributionCalendarToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FrontEndGitHubContributionCalendar &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._weeks, _weeks));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    total,
    const DeepCollectionEquality().hash(_weeks),
  );

  @override
  String toString() {
    return 'FrontEndGitHubContributionCalendar(total: $total, weeks: $weeks)';
  }
}

/// @nodoc
abstract mixin class _$FrontEndGitHubContributionCalendarCopyWith<$Res>
    implements $FrontEndGitHubContributionCalendarCopyWith<$Res> {
  factory _$FrontEndGitHubContributionCalendarCopyWith(
    _FrontEndGitHubContributionCalendar value,
    $Res Function(_FrontEndGitHubContributionCalendar) _then,
  ) = __$FrontEndGitHubContributionCalendarCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'totalContributions') int total,
    @JsonKey(name: 'weeks') List<FrontEndGitHubContributionDays> weeks,
  });
}

/// @nodoc
class __$FrontEndGitHubContributionCalendarCopyWithImpl<$Res>
    implements _$FrontEndGitHubContributionCalendarCopyWith<$Res> {
  __$FrontEndGitHubContributionCalendarCopyWithImpl(this._self, this._then);

  final _FrontEndGitHubContributionCalendar _self;
  final $Res Function(_FrontEndGitHubContributionCalendar) _then;

  /// Create a copy of FrontEndGitHubContributionCalendar
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? total = null, Object? weeks = null}) {
    return _then(
      _FrontEndGitHubContributionCalendar(
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        weeks: null == weeks
            ? _self._weeks
            : weeks // ignore: cast_nullable_to_non_nullable
                  as List<FrontEndGitHubContributionDays>,
      ),
    );
  }
}

/// @nodoc
mixin _$FrontEndGitHubContributionDays {
  @JsonKey(name: 'contributionDays')
  List<FrontEndGitHubContributionDay> get days;

  /// Create a copy of FrontEndGitHubContributionDays
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FrontEndGitHubContributionDaysCopyWith<FrontEndGitHubContributionDays>
  get copyWith =>
      _$FrontEndGitHubContributionDaysCopyWithImpl<
        FrontEndGitHubContributionDays
      >(this as FrontEndGitHubContributionDays, _$identity);

  /// Serializes this FrontEndGitHubContributionDays to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FrontEndGitHubContributionDays &&
            const DeepCollectionEquality().equals(other.days, days));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(days));

  @override
  String toString() {
    return 'FrontEndGitHubContributionDays(days: $days)';
  }
}

/// @nodoc
abstract mixin class $FrontEndGitHubContributionDaysCopyWith<$Res> {
  factory $FrontEndGitHubContributionDaysCopyWith(
    FrontEndGitHubContributionDays value,
    $Res Function(FrontEndGitHubContributionDays) _then,
  ) = _$FrontEndGitHubContributionDaysCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'contributionDays') List<FrontEndGitHubContributionDay> days,
  });
}

/// @nodoc
class _$FrontEndGitHubContributionDaysCopyWithImpl<$Res>
    implements $FrontEndGitHubContributionDaysCopyWith<$Res> {
  _$FrontEndGitHubContributionDaysCopyWithImpl(this._self, this._then);

  final FrontEndGitHubContributionDays _self;
  final $Res Function(FrontEndGitHubContributionDays) _then;

  /// Create a copy of FrontEndGitHubContributionDays
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? days = null}) {
    return _then(
      _self.copyWith(
        days: null == days
            ? _self.days
            : days // ignore: cast_nullable_to_non_nullable
                  as List<FrontEndGitHubContributionDay>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _FrontEndGitHubContributionDays
    implements FrontEndGitHubContributionDays {
  const _FrontEndGitHubContributionDays({
    @JsonKey(name: 'contributionDays')
    required final List<FrontEndGitHubContributionDay> days,
  }) : _days = days;
  factory _FrontEndGitHubContributionDays.fromJson(Map<String, dynamic> json) =>
      _$FrontEndGitHubContributionDaysFromJson(json);

  final List<FrontEndGitHubContributionDay> _days;
  @override
  @JsonKey(name: 'contributionDays')
  List<FrontEndGitHubContributionDay> get days {
    if (_days is EqualUnmodifiableListView) return _days;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_days);
  }

  /// Create a copy of FrontEndGitHubContributionDays
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FrontEndGitHubContributionDaysCopyWith<_FrontEndGitHubContributionDays>
  get copyWith =>
      __$FrontEndGitHubContributionDaysCopyWithImpl<
        _FrontEndGitHubContributionDays
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FrontEndGitHubContributionDaysToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FrontEndGitHubContributionDays &&
            const DeepCollectionEquality().equals(other._days, _days));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_days));

  @override
  String toString() {
    return 'FrontEndGitHubContributionDays(days: $days)';
  }
}

/// @nodoc
abstract mixin class _$FrontEndGitHubContributionDaysCopyWith<$Res>
    implements $FrontEndGitHubContributionDaysCopyWith<$Res> {
  factory _$FrontEndGitHubContributionDaysCopyWith(
    _FrontEndGitHubContributionDays value,
    $Res Function(_FrontEndGitHubContributionDays) _then,
  ) = __$FrontEndGitHubContributionDaysCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'contributionDays') List<FrontEndGitHubContributionDay> days,
  });
}

/// @nodoc
class __$FrontEndGitHubContributionDaysCopyWithImpl<$Res>
    implements _$FrontEndGitHubContributionDaysCopyWith<$Res> {
  __$FrontEndGitHubContributionDaysCopyWithImpl(this._self, this._then);

  final _FrontEndGitHubContributionDays _self;
  final $Res Function(_FrontEndGitHubContributionDays) _then;

  /// Create a copy of FrontEndGitHubContributionDays
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? days = null}) {
    return _then(
      _FrontEndGitHubContributionDays(
        days: null == days
            ? _self._days
            : days // ignore: cast_nullable_to_non_nullable
                  as List<FrontEndGitHubContributionDay>,
      ),
    );
  }
}

/// @nodoc
mixin _$FrontEndGitHubContributionDay {
  @JsonKey(name: 'contributionCount')
  int get contributionCount;
  @JsonKey(name: 'date')
  String get rawDate;

  /// Create a copy of FrontEndGitHubContributionDay
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FrontEndGitHubContributionDayCopyWith<FrontEndGitHubContributionDay>
  get copyWith =>
      _$FrontEndGitHubContributionDayCopyWithImpl<
        FrontEndGitHubContributionDay
      >(this as FrontEndGitHubContributionDay, _$identity);

  /// Serializes this FrontEndGitHubContributionDay to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FrontEndGitHubContributionDay &&
            (identical(other.contributionCount, contributionCount) ||
                other.contributionCount == contributionCount) &&
            (identical(other.rawDate, rawDate) || other.rawDate == rawDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contributionCount, rawDate);

  @override
  String toString() {
    return 'FrontEndGitHubContributionDay(contributionCount: $contributionCount, rawDate: $rawDate)';
  }
}

/// @nodoc
abstract mixin class $FrontEndGitHubContributionDayCopyWith<$Res> {
  factory $FrontEndGitHubContributionDayCopyWith(
    FrontEndGitHubContributionDay value,
    $Res Function(FrontEndGitHubContributionDay) _then,
  ) = _$FrontEndGitHubContributionDayCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'contributionCount') int contributionCount,
    @JsonKey(name: 'date') String rawDate,
  });
}

/// @nodoc
class _$FrontEndGitHubContributionDayCopyWithImpl<$Res>
    implements $FrontEndGitHubContributionDayCopyWith<$Res> {
  _$FrontEndGitHubContributionDayCopyWithImpl(this._self, this._then);

  final FrontEndGitHubContributionDay _self;
  final $Res Function(FrontEndGitHubContributionDay) _then;

  /// Create a copy of FrontEndGitHubContributionDay
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? contributionCount = null, Object? rawDate = null}) {
    return _then(
      _self.copyWith(
        contributionCount: null == contributionCount
            ? _self.contributionCount
            : contributionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        rawDate: null == rawDate
            ? _self.rawDate
            : rawDate // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _FrontEndGitHubContributionDay extends FrontEndGitHubContributionDay {
  const _FrontEndGitHubContributionDay({
    @JsonKey(name: 'contributionCount') required this.contributionCount,
    @JsonKey(name: 'date') required this.rawDate,
  }) : super._();
  factory _FrontEndGitHubContributionDay.fromJson(Map<String, dynamic> json) =>
      _$FrontEndGitHubContributionDayFromJson(json);

  @override
  @JsonKey(name: 'contributionCount')
  final int contributionCount;
  @override
  @JsonKey(name: 'date')
  final String rawDate;

  /// Create a copy of FrontEndGitHubContributionDay
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FrontEndGitHubContributionDayCopyWith<_FrontEndGitHubContributionDay>
  get copyWith =>
      __$FrontEndGitHubContributionDayCopyWithImpl<
        _FrontEndGitHubContributionDay
      >(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FrontEndGitHubContributionDayToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FrontEndGitHubContributionDay &&
            (identical(other.contributionCount, contributionCount) ||
                other.contributionCount == contributionCount) &&
            (identical(other.rawDate, rawDate) || other.rawDate == rawDate));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, contributionCount, rawDate);

  @override
  String toString() {
    return 'FrontEndGitHubContributionDay(contributionCount: $contributionCount, rawDate: $rawDate)';
  }
}

/// @nodoc
abstract mixin class _$FrontEndGitHubContributionDayCopyWith<$Res>
    implements $FrontEndGitHubContributionDayCopyWith<$Res> {
  factory _$FrontEndGitHubContributionDayCopyWith(
    _FrontEndGitHubContributionDay value,
    $Res Function(_FrontEndGitHubContributionDay) _then,
  ) = __$FrontEndGitHubContributionDayCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'contributionCount') int contributionCount,
    @JsonKey(name: 'date') String rawDate,
  });
}

/// @nodoc
class __$FrontEndGitHubContributionDayCopyWithImpl<$Res>
    implements _$FrontEndGitHubContributionDayCopyWith<$Res> {
  __$FrontEndGitHubContributionDayCopyWithImpl(this._self, this._then);

  final _FrontEndGitHubContributionDay _self;
  final $Res Function(_FrontEndGitHubContributionDay) _then;

  /// Create a copy of FrontEndGitHubContributionDay
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? contributionCount = null, Object? rawDate = null}) {
    return _then(
      _FrontEndGitHubContributionDay(
        contributionCount: null == contributionCount
            ? _self.contributionCount
            : contributionCount // ignore: cast_nullable_to_non_nullable
                  as int,
        rawDate: null == rawDate
            ? _self.rawDate
            : rawDate // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}
