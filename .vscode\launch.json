{
  // VSCode Launch Configurations - equivalent to IntelliJ IDEA run configurations
  // Use Ctrl+Shift+P -> "Debug: Select and Start Debugging" to run these
  // Pre-launch tasks will automatically stop the launch if they fail
  "version": "0.2.0",
  "configurations": [
    // Flutter Debug Configurations
    {
      "name": "DEV",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "preLaunchTask": "config-dev"
    },
    {
      "name": "PROD",
      "request": "launch",
      "type": "dart",
      "flutterMode": "debug",
      "preLaunchTask": "config-prod"
    }
  ]
}