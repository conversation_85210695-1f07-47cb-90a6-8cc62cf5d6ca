import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/user.dart' show ProfileMode;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/business.dart';
import '/provider/user.dart' show fetchUserInfoProvider;

/// 模式设置页面
@FFRoute(name: '/setting/mode')
class ModeSettingPage extends ConsumerStatefulWidget {
  const ModeSettingPage({super.key});

  @override
  ConsumerState<ModeSettingPage> createState() => _ModeSettingPageState();
}

class _ModeSettingPageState extends ConsumerState<ModeSettingPage> {
  ProfileMode _selectedMode = ProfileMode.ETHCC; // 使用枚举类型
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    // 获取用户当前的模式设置
    final config = ref.read(configProvider);
    if (ref.read(fetchUserInfoProvider()).valueOrNull case final user?) {
      safeSetState(() {
        _selectedMode = user.profileMode == ProfileMode.EMPTY ? config.defaultMode : user.profileMode;
      });
    }
  }

  Future<void> _handleModeChange() async {
    setState(() {
      _isSubmitting = true;
    });

    try {
      // 将枚举转换为字符串传递给API
      final modeString = _selectedMode == ProfileMode.DEFAULT ? 'DEFAULT' : 'ETHCC';

      await ref
          .read(apiServiceProvider)
          .updateUserInfo(
            profileMode: modeString,
          );

      // 刷新用户信息
      ref.invalidate(fetchUserInfoProvider);

      if (mounted) {
        Card3ToastUtil.showToast(
          message: ToastMessages.modeChangedSuccessfully,
        );

        // 导航回首页
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(
          message: ToastMessages.failedToChangeMode,
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      appBar: AppBar(
        backgroundColor: const Color(0xFFf1f1f1),
        elevation: 0,
        leading: const AppBackButton(),
        title: const Text(
          'Mode Setting',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            // 模式选择区域
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 40),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Choose your profile mode',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Common Mode 选项
                    _buildModeOption(
                      value: ProfileMode.DEFAULT,
                      title: 'Common Mode',
                      description: 'Standard profile with basic features',
                      isSelected: _selectedMode == ProfileMode.DEFAULT,
                      onTap: () {
                        setState(() {
                          _selectedMode = ProfileMode.DEFAULT;
                        });
                      },
                    ),

                    const SizedBox(height: 20),

                    // ETHCC Mode 选项
                    _buildModeOption(
                      value: ProfileMode.ETHCC,
                      title: 'ETHCC Mode',
                      description: 'Enhanced profile for ETHCC events',
                      isSelected: _selectedMode == ProfileMode.ETHCC,
                      onTap: () {
                        setState(() {
                          _selectedMode = ProfileMode.ETHCC;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),

            // 确认按钮
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _handleModeChange,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isSubmitting ? Colors.grey[300] : ColorName.themeColorDark,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2.0,
                        ),
                      )
                    : const Text(
                        'Confirm',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModeOption({
    required ProfileMode value,
    required String title,
    required String description,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? ColorName.themeColorDark : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Radio 图标
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? ColorName.themeColorDark : Colors.grey[400]!,
                  width: 2,
                ),
                color: isSelected ? ColorName.themeColorDark : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      size: 16,
                      color: Colors.white,
                    )
                  : null,
            ),

            const SizedBox(width: 16),

            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? ColorName.themeColorDark : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
