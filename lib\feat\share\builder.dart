import '/constants/envs.dart' show envUrlCard3;

export 'package:share_plus/share_plus.dart';

final class ShareBuilder {
  ShareBuilder();

  late String _method;
  final _queryParameters = <String, String>{};
  final _paths = <String>[];

  ShareBuilder setMethod(String value) {
    assert(!value.startsWith('/'));
    _method = value;
    return this;
  }

  ShareBuilder addQueryParameter(String key, String value) {
    _queryParameters[key] = value;
    return this;
  }

  ShareBuilder addPath(String value) {
    assert(!value.startsWith('/'));
    _paths.add(value);
    return this;
  }

  ShareBuilder addPaths(List<String> value) {
    _paths.addAll(value);
    return this;
  }

  Uri build() {
    final uri = Uri.parse(envUrlCard3).replace(
      pathSegments: ['app', _method, ..._paths],
      queryParameters: _queryParameters.isNotEmpty ? _queryParameters : null,
    );
    return uri;
  }
}
