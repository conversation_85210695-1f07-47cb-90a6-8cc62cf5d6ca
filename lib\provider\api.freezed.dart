// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Rep<T> implements DiagnosticableTreeMixin {
  @JsonKey(name: 'code')
  Object get code;
  @JsonKey(name: 'message', readValue: apiFromJsonMessage)
  String get message;
  @JsonKey(name: 'data')
  T get data;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Object get succeedCode;

  /// Create a copy of Rep
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RepCopyWith<T, Rep<T>> get copyWith =>
      _$RepCopyWithImpl<T, Rep<T>>(this as Rep<T>, _$identity);

  /// Serializes this Rep to a JSON map.
  Map<String, dynamic> toJson(Object? Function(T) toJsonT);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'Rep<$T>'))
      ..add(DiagnosticsProperty('code', code))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('data', data))
      ..add(DiagnosticsProperty('succeedCode', succeedCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Rep<T> &&
            const DeepCollectionEquality().equals(other.code, code) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.data, data) &&
            const DeepCollectionEquality().equals(
              other.succeedCode,
              succeedCode,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(code),
    message,
    const DeepCollectionEquality().hash(data),
    const DeepCollectionEquality().hash(succeedCode),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Rep<$T>(code: $code, message: $message, data: $data, succeedCode: $succeedCode)';
  }
}

/// @nodoc
abstract mixin class $RepCopyWith<T, $Res> {
  factory $RepCopyWith(Rep<T> value, $Res Function(Rep<T>) _then) =
      _$RepCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'code') Object code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) String message,
    @JsonKey(name: 'data') T data,
    @JsonKey(includeFromJson: false, includeToJson: false) Object succeedCode,
  });
}

/// @nodoc
class _$RepCopyWithImpl<T, $Res> implements $RepCopyWith<T, $Res> {
  _$RepCopyWithImpl(this._self, this._then);

  final Rep<T> _self;
  final $Res Function(Rep<T>) _then;

  /// Create a copy of Rep
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? data = freezed,
    Object? succeedCode = null,
  }) {
    return _then(
      _self.copyWith(
        code: null == code ? _self.code : code,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        data: freezed == data
            ? _self.data
            : data // ignore: cast_nullable_to_non_nullable
                  as T,
        succeedCode: null == succeedCode ? _self.succeedCode : succeedCode,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable(genericArgumentFactories: true)
class _Rep<T> extends Rep<T> with DiagnosticableTreeMixin {
  const _Rep({
    @JsonKey(name: 'code') required this.code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) this.message = '',
    @JsonKey(name: 'data') required this.data,
    @JsonKey(includeFromJson: false, includeToJson: false)
    this.succeedCode = Rep.defaultSucceedCode,
  }) : super._();
  factory _Rep.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$RepFromJson(json, fromJsonT);

  @override
  @JsonKey(name: 'code')
  final Object code;
  @override
  @JsonKey(name: 'message', readValue: apiFromJsonMessage)
  final String message;
  @override
  @JsonKey(name: 'data')
  final T data;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Object succeedCode;

  /// Create a copy of Rep
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RepCopyWith<T, _Rep<T>> get copyWith =>
      __$RepCopyWithImpl<T, _Rep<T>>(this, _$identity);

  @override
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$RepToJson<T>(this, toJsonT);
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'Rep<$T>'))
      ..add(DiagnosticsProperty('code', code))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('data', data))
      ..add(DiagnosticsProperty('succeedCode', succeedCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Rep<T> &&
            const DeepCollectionEquality().equals(other.code, code) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.data, data) &&
            const DeepCollectionEquality().equals(
              other.succeedCode,
              succeedCode,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(code),
    message,
    const DeepCollectionEquality().hash(data),
    const DeepCollectionEquality().hash(succeedCode),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Rep<$T>(code: $code, message: $message, data: $data, succeedCode: $succeedCode)';
  }
}

/// @nodoc
abstract mixin class _$RepCopyWith<T, $Res> implements $RepCopyWith<T, $Res> {
  factory _$RepCopyWith(_Rep<T> value, $Res Function(_Rep<T>) _then) =
      __$RepCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'code') Object code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) String message,
    @JsonKey(name: 'data') T data,
    @JsonKey(includeFromJson: false, includeToJson: false) Object succeedCode,
  });
}

/// @nodoc
class __$RepCopyWithImpl<T, $Res> implements _$RepCopyWith<T, $Res> {
  __$RepCopyWithImpl(this._self, this._then);

  final _Rep<T> _self;
  final $Res Function(_Rep<T>) _then;

  /// Create a copy of Rep
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? data = freezed,
    Object? succeedCode = null,
  }) {
    return _then(
      _Rep<T>(
        code: null == code ? _self.code : code,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        data: freezed == data
            ? _self.data
            : data // ignore: cast_nullable_to_non_nullable
                  as T,
        succeedCode: null == succeedCode ? _self.succeedCode : succeedCode,
      ),
    );
  }
}

/// @nodoc
mixin _$ListRep<T> implements DiagnosticableTreeMixin {
  @JsonKey(name: 'code')
  Object get code;
  @JsonKey(name: 'message', readValue: apiFromJsonMessage)
  String get message;
  @JsonKey(name: 'data')
  List<T> get list;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Object get succeedCode;

  /// Create a copy of ListRep
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ListRepCopyWith<T, ListRep<T>> get copyWith =>
      _$ListRepCopyWithImpl<T, ListRep<T>>(this as ListRep<T>, _$identity);

  /// Serializes this ListRep to a JSON map.
  Map<String, dynamic> toJson(Object? Function(T) toJsonT);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'ListRep<$T>'))
      ..add(DiagnosticsProperty('code', code))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('list', list))
      ..add(DiagnosticsProperty('succeedCode', succeedCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ListRep<T> &&
            const DeepCollectionEquality().equals(other.code, code) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.list, list) &&
            const DeepCollectionEquality().equals(
              other.succeedCode,
              succeedCode,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(code),
    message,
    const DeepCollectionEquality().hash(list),
    const DeepCollectionEquality().hash(succeedCode),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ListRep<$T>(code: $code, message: $message, list: $list, succeedCode: $succeedCode)';
  }
}

/// @nodoc
abstract mixin class $ListRepCopyWith<T, $Res> {
  factory $ListRepCopyWith(ListRep<T> value, $Res Function(ListRep<T>) _then) =
      _$ListRepCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'code') Object code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) String message,
    @JsonKey(name: 'data') List<T> list,
    @JsonKey(includeFromJson: false, includeToJson: false) Object succeedCode,
  });
}

/// @nodoc
class _$ListRepCopyWithImpl<T, $Res> implements $ListRepCopyWith<T, $Res> {
  _$ListRepCopyWithImpl(this._self, this._then);

  final ListRep<T> _self;
  final $Res Function(ListRep<T>) _then;

  /// Create a copy of ListRep
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? list = null,
    Object? succeedCode = null,
  }) {
    return _then(
      _self.copyWith(
        code: null == code ? _self.code : code,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        list: null == list
            ? _self.list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
        succeedCode: null == succeedCode ? _self.succeedCode : succeedCode,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable(genericArgumentFactories: true)
class _ListRep<T> extends ListRep<T> with DiagnosticableTreeMixin {
  const _ListRep({
    @JsonKey(name: 'code') required this.code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) this.message = '',
    @JsonKey(name: 'data') final List<T> list = const [],
    @JsonKey(includeFromJson: false, includeToJson: false)
    this.succeedCode = Rep.defaultSucceedCode,
  }) : _list = list,
       super._();
  factory _ListRep.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$ListRepFromJson(json, fromJsonT);

  @override
  @JsonKey(name: 'code')
  final Object code;
  @override
  @JsonKey(name: 'message', readValue: apiFromJsonMessage)
  final String message;
  final List<T> _list;
  @override
  @JsonKey(name: 'data')
  List<T> get list {
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_list);
  }

  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Object succeedCode;

  /// Create a copy of ListRep
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ListRepCopyWith<T, _ListRep<T>> get copyWith =>
      __$ListRepCopyWithImpl<T, _ListRep<T>>(this, _$identity);

  @override
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$ListRepToJson<T>(this, toJsonT);
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'ListRep<$T>'))
      ..add(DiagnosticsProperty('code', code))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('list', list))
      ..add(DiagnosticsProperty('succeedCode', succeedCode));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ListRep<T> &&
            const DeepCollectionEquality().equals(other.code, code) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._list, _list) &&
            const DeepCollectionEquality().equals(
              other.succeedCode,
              succeedCode,
            ));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(code),
    message,
    const DeepCollectionEquality().hash(_list),
    const DeepCollectionEquality().hash(succeedCode),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ListRep<$T>(code: $code, message: $message, list: $list, succeedCode: $succeedCode)';
  }
}

/// @nodoc
abstract mixin class _$ListRepCopyWith<T, $Res>
    implements $ListRepCopyWith<T, $Res> {
  factory _$ListRepCopyWith(
    _ListRep<T> value,
    $Res Function(_ListRep<T>) _then,
  ) = __$ListRepCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'code') Object code,
    @JsonKey(name: 'message', readValue: apiFromJsonMessage) String message,
    @JsonKey(name: 'data') List<T> list,
    @JsonKey(includeFromJson: false, includeToJson: false) Object succeedCode,
  });
}

/// @nodoc
class __$ListRepCopyWithImpl<T, $Res> implements _$ListRepCopyWith<T, $Res> {
  __$ListRepCopyWithImpl(this._self, this._then);

  final _ListRep<T> _self;
  final $Res Function(_ListRep<T>) _then;

  /// Create a copy of ListRep
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? code = null,
    Object? message = null,
    Object? list = null,
    Object? succeedCode = null,
  }) {
    return _then(
      _ListRep<T>(
        code: null == code ? _self.code : code,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        list: null == list
            ? _self._list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
        succeedCode: null == succeedCode ? _self.succeedCode : succeedCode,
      ),
    );
  }
}

/// @nodoc
mixin _$Paged<T> implements DiagnosticableTreeMixin {
  @JsonKey(name: 'current')
  int get page;
  @JsonKey(name: 'size')
  int get size;
  @JsonKey(name: 'pages')
  int get pages;
  @JsonKey(name: 'total')
  int get total;
  @JsonKey(name: 'records')
  List<T> get list;

  /// Create a copy of Paged
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PagedCopyWith<T, Paged<T>> get copyWith =>
      _$PagedCopyWithImpl<T, Paged<T>>(this as Paged<T>, _$identity);

  /// Serializes this Paged to a JSON map.
  Map<String, dynamic> toJson(Object? Function(T) toJsonT);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'Paged<$T>'))
      ..add(DiagnosticsProperty('page', page))
      ..add(DiagnosticsProperty('size', size))
      ..add(DiagnosticsProperty('pages', pages))
      ..add(DiagnosticsProperty('total', total))
      ..add(DiagnosticsProperty('list', list));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Paged<T> &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other.list, list));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    page,
    size,
    pages,
    total,
    const DeepCollectionEquality().hash(list),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Paged<$T>(page: $page, size: $size, pages: $pages, total: $total, list: $list)';
  }
}

/// @nodoc
abstract mixin class $PagedCopyWith<T, $Res> {
  factory $PagedCopyWith(Paged<T> value, $Res Function(Paged<T>) _then) =
      _$PagedCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'current') int page,
    @JsonKey(name: 'size') int size,
    @JsonKey(name: 'pages') int pages,
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'records') List<T> list,
  });
}

/// @nodoc
class _$PagedCopyWithImpl<T, $Res> implements $PagedCopyWith<T, $Res> {
  _$PagedCopyWithImpl(this._self, this._then);

  final Paged<T> _self;
  final $Res Function(Paged<T>) _then;

  /// Create a copy of Paged
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? size = null,
    Object? pages = null,
    Object? total = null,
    Object? list = null,
  }) {
    return _then(
      _self.copyWith(
        page: null == page
            ? _self.page
            : page // ignore: cast_nullable_to_non_nullable
                  as int,
        size: null == size
            ? _self.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int,
        pages: null == pages
            ? _self.pages
            : pages // ignore: cast_nullable_to_non_nullable
                  as int,
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        list: null == list
            ? _self.list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable(genericArgumentFactories: true)
class _Paged<T> with DiagnosticableTreeMixin implements Paged<T> {
  const _Paged({
    @JsonKey(name: 'current') required this.page,
    @JsonKey(name: 'size') required this.size,
    @JsonKey(name: 'pages') required this.pages,
    @JsonKey(name: 'total') this.total = 0,
    @JsonKey(name: 'records') final List<T> list = const [],
  }) : _list = list;
  factory _Paged.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$PagedFromJson(json, fromJsonT);

  @override
  @JsonKey(name: 'current')
  final int page;
  @override
  @JsonKey(name: 'size')
  final int size;
  @override
  @JsonKey(name: 'pages')
  final int pages;
  @override
  @JsonKey(name: 'total')
  final int total;
  final List<T> _list;
  @override
  @JsonKey(name: 'records')
  List<T> get list {
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_list);
  }

  /// Create a copy of Paged
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PagedCopyWith<T, _Paged<T>> get copyWith =>
      __$PagedCopyWithImpl<T, _Paged<T>>(this, _$identity);

  @override
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$PagedToJson<T>(this, toJsonT);
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'Paged<$T>'))
      ..add(DiagnosticsProperty('page', page))
      ..add(DiagnosticsProperty('size', size))
      ..add(DiagnosticsProperty('pages', pages))
      ..add(DiagnosticsProperty('total', total))
      ..add(DiagnosticsProperty('list', list));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Paged<T> &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._list, _list));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    page,
    size,
    pages,
    total,
    const DeepCollectionEquality().hash(_list),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'Paged<$T>(page: $page, size: $size, pages: $pages, total: $total, list: $list)';
  }
}

/// @nodoc
abstract mixin class _$PagedCopyWith<T, $Res>
    implements $PagedCopyWith<T, $Res> {
  factory _$PagedCopyWith(_Paged<T> value, $Res Function(_Paged<T>) _then) =
      __$PagedCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'current') int page,
    @JsonKey(name: 'size') int size,
    @JsonKey(name: 'pages') int pages,
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'records') List<T> list,
  });
}

/// @nodoc
class __$PagedCopyWithImpl<T, $Res> implements _$PagedCopyWith<T, $Res> {
  __$PagedCopyWithImpl(this._self, this._then);

  final _Paged<T> _self;
  final $Res Function(_Paged<T>) _then;

  /// Create a copy of Paged
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? page = null,
    Object? size = null,
    Object? pages = null,
    Object? total = null,
    Object? list = null,
  }) {
    return _then(
      _Paged<T>(
        page: null == page
            ? _self.page
            : page // ignore: cast_nullable_to_non_nullable
                  as int,
        size: null == size
            ? _self.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int,
        pages: null == pages
            ? _self.pages
            : pages // ignore: cast_nullable_to_non_nullable
                  as int,
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        list: null == list
            ? _self._list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
      ),
    );
  }
}

/// @nodoc
mixin _$AstroxPaged<T> implements DiagnosticableTreeMixin {
  @JsonKey(name: 'pageNum')
  int get page;
  @JsonKey(name: 'pageSize')
  int get size;
  @JsonKey(name: 'totalPage')
  int get pages;
  @JsonKey(name: 'total')
  int get total;
  @JsonKey(name: 'list')
  List<T> get list;

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstroxPagedCopyWith<T, AstroxPaged<T>> get copyWith =>
      _$AstroxPagedCopyWithImpl<T, AstroxPaged<T>>(
        this as AstroxPaged<T>,
        _$identity,
      );

  /// Serializes this AstroxPaged to a JSON map.
  Map<String, dynamic> toJson(Object? Function(T) toJsonT);

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AstroxPaged<$T>'))
      ..add(DiagnosticsProperty('page', page))
      ..add(DiagnosticsProperty('size', size))
      ..add(DiagnosticsProperty('pages', pages))
      ..add(DiagnosticsProperty('total', total))
      ..add(DiagnosticsProperty('list', list));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstroxPaged<T> &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other.list, list));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    page,
    size,
    pages,
    total,
    const DeepCollectionEquality().hash(list),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AstroxPaged<$T>(page: $page, size: $size, pages: $pages, total: $total, list: $list)';
  }
}

/// @nodoc
abstract mixin class $AstroxPagedCopyWith<T, $Res> {
  factory $AstroxPagedCopyWith(
    AstroxPaged<T> value,
    $Res Function(AstroxPaged<T>) _then,
  ) = _$AstroxPagedCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'pageNum') int page,
    @JsonKey(name: 'pageSize') int size,
    @JsonKey(name: 'totalPage') int pages,
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'list') List<T> list,
  });
}

/// @nodoc
class _$AstroxPagedCopyWithImpl<T, $Res>
    implements $AstroxPagedCopyWith<T, $Res> {
  _$AstroxPagedCopyWithImpl(this._self, this._then);

  final AstroxPaged<T> _self;
  final $Res Function(AstroxPaged<T>) _then;

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? page = null,
    Object? size = null,
    Object? pages = null,
    Object? total = null,
    Object? list = null,
  }) {
    return _then(
      _self.copyWith(
        page: null == page
            ? _self.page
            : page // ignore: cast_nullable_to_non_nullable
                  as int,
        size: null == size
            ? _self.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int,
        pages: null == pages
            ? _self.pages
            : pages // ignore: cast_nullable_to_non_nullable
                  as int,
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        list: null == list
            ? _self.list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable(genericArgumentFactories: true)
class _AstroxPaged<T> with DiagnosticableTreeMixin implements AstroxPaged<T> {
  const _AstroxPaged({
    @JsonKey(name: 'pageNum') required this.page,
    @JsonKey(name: 'pageSize') required this.size,
    @JsonKey(name: 'totalPage') required this.pages,
    @JsonKey(name: 'total') this.total = 0,
    @JsonKey(name: 'list') final List<T> list = const [],
  }) : _list = list;
  factory _AstroxPaged.fromJson(
    Map<String, dynamic> json,
    T Function(Object?) fromJsonT,
  ) => _$AstroxPagedFromJson(json, fromJsonT);

  @override
  @JsonKey(name: 'pageNum')
  final int page;
  @override
  @JsonKey(name: 'pageSize')
  final int size;
  @override
  @JsonKey(name: 'totalPage')
  final int pages;
  @override
  @JsonKey(name: 'total')
  final int total;
  final List<T> _list;
  @override
  @JsonKey(name: 'list')
  List<T> get list {
    if (_list is EqualUnmodifiableListView) return _list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_list);
  }

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstroxPagedCopyWith<T, _AstroxPaged<T>> get copyWith =>
      __$AstroxPagedCopyWithImpl<T, _AstroxPaged<T>>(this, _$identity);

  @override
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$AstroxPagedToJson<T>(this, toJsonT);
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'AstroxPaged<$T>'))
      ..add(DiagnosticsProperty('page', page))
      ..add(DiagnosticsProperty('size', size))
      ..add(DiagnosticsProperty('pages', pages))
      ..add(DiagnosticsProperty('total', total))
      ..add(DiagnosticsProperty('list', list));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstroxPaged<T> &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.total, total) || other.total == total) &&
            const DeepCollectionEquality().equals(other._list, _list));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    page,
    size,
    pages,
    total,
    const DeepCollectionEquality().hash(_list),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'AstroxPaged<$T>(page: $page, size: $size, pages: $pages, total: $total, list: $list)';
  }
}

/// @nodoc
abstract mixin class _$AstroxPagedCopyWith<T, $Res>
    implements $AstroxPagedCopyWith<T, $Res> {
  factory _$AstroxPagedCopyWith(
    _AstroxPaged<T> value,
    $Res Function(_AstroxPaged<T>) _then,
  ) = __$AstroxPagedCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'pageNum') int page,
    @JsonKey(name: 'pageSize') int size,
    @JsonKey(name: 'totalPage') int pages,
    @JsonKey(name: 'total') int total,
    @JsonKey(name: 'list') List<T> list,
  });
}

/// @nodoc
class __$AstroxPagedCopyWithImpl<T, $Res>
    implements _$AstroxPagedCopyWith<T, $Res> {
  __$AstroxPagedCopyWithImpl(this._self, this._then);

  final _AstroxPaged<T> _self;
  final $Res Function(_AstroxPaged<T>) _then;

  /// Create a copy of AstroxPaged
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? page = null,
    Object? size = null,
    Object? pages = null,
    Object? total = null,
    Object? list = null,
  }) {
    return _then(
      _AstroxPaged<T>(
        page: null == page
            ? _self.page
            : page // ignore: cast_nullable_to_non_nullable
                  as int,
        size: null == size
            ? _self.size
            : size // ignore: cast_nullable_to_non_nullable
                  as int,
        pages: null == pages
            ? _self.pages
            : pages // ignore: cast_nullable_to_non_nullable
                  as int,
        total: null == total
            ? _self.total
            : total // ignore: cast_nullable_to_non_nullable
                  as int,
        list: null == list
            ? _self._list
            : list // ignore: cast_nullable_to_non_nullable
                  as List<T>,
      ),
    );
  }
}

/// @nodoc
mixin _$ApiException implements DiagnosticableTreeMixin {
  // Typically `String` or `int`.
  Object get code;
  String? get message;
  Object? get data;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApiExceptionCopyWith<ApiException> get copyWith =>
      _$ApiExceptionCopyWithImpl<ApiException>(
        this as ApiException,
        _$identity,
      );

  /// Serializes this ApiException to a JSON map.
  Map<String, dynamic> toJson();

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'ApiException'))
      ..add(DiagnosticsProperty('code', code))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApiException &&
            const DeepCollectionEquality().equals(other.code, code) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(code),
    message,
    const DeepCollectionEquality().hash(data),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ApiException(code: $code, message: $message, data: $data)';
  }
}

/// @nodoc
abstract mixin class $ApiExceptionCopyWith<$Res> {
  factory $ApiExceptionCopyWith(
    ApiException value,
    $Res Function(ApiException) _then,
  ) = _$ApiExceptionCopyWithImpl;
  @useResult
  $Res call({Object code, String? message, Object? data});
}

/// @nodoc
class _$ApiExceptionCopyWithImpl<$Res> implements $ApiExceptionCopyWith<$Res> {
  _$ApiExceptionCopyWithImpl(this._self, this._then);

  final ApiException _self;
  final $Res Function(ApiException) _then;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? message = freezed,
    Object? data = freezed,
  }) {
    return _then(
      _self.copyWith(
        code: null == code ? _self.code : code,
        message: freezed == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
        data: freezed == data ? _self.data : data,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _ApiException with DiagnosticableTreeMixin implements ApiException {
  const _ApiException({required this.code, this.message, this.data});
  factory _ApiException.fromJson(Map<String, dynamic> json) =>
      _$ApiExceptionFromJson(json);

  // Typically `String` or `int`.
  @override
  final Object code;
  @override
  final String? message;
  @override
  final Object? data;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApiExceptionCopyWith<_ApiException> get copyWith =>
      __$ApiExceptionCopyWithImpl<_ApiException>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ApiExceptionToJson(this);
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    properties
      ..add(DiagnosticsProperty('type', 'ApiException'))
      ..add(DiagnosticsProperty('code', code))
      ..add(DiagnosticsProperty('message', message))
      ..add(DiagnosticsProperty('data', data));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApiException &&
            const DeepCollectionEquality().equals(other.code, code) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(code),
    message,
    const DeepCollectionEquality().hash(data),
  );

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'ApiException(code: $code, message: $message, data: $data)';
  }
}

/// @nodoc
abstract mixin class _$ApiExceptionCopyWith<$Res>
    implements $ApiExceptionCopyWith<$Res> {
  factory _$ApiExceptionCopyWith(
    _ApiException value,
    $Res Function(_ApiException) _then,
  ) = __$ApiExceptionCopyWithImpl;
  @override
  @useResult
  $Res call({Object code, String? message, Object? data});
}

/// @nodoc
class __$ApiExceptionCopyWithImpl<$Res>
    implements _$ApiExceptionCopyWith<$Res> {
  __$ApiExceptionCopyWithImpl(this._self, this._then);

  final _ApiException _self;
  final $Res Function(_ApiException) _then;

  /// Create a copy of ApiException
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? code = null,
    Object? message = freezed,
    Object? data = freezed,
  }) {
    return _then(
      _ApiException(
        code: null == code ? _self.code : code,
        message: freezed == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String?,
        data: freezed == data ? _self.data : data,
      ),
    );
  }
}
