import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import '../../../models/card.dart';

class MetalCustomizeView extends StatelessWidget {
  const MetalCustomizeView({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.titleController,
    required this.companyController,
    required this.coverInfo,
    required this.isLoading,
    required this.onNext,
    required this.onConfirm,
    required this.onBack,
    required this.validateName,
    required this.validateTitle,
    required this.validateCompany,
    required this.calculateFontSize,
    this.isConfirmView = false,
  });

  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController titleController;
  final TextEditingController companyController;
  final CoverInfo? coverInfo;
  final bool isLoading;
  final Function() onNext;
  final Function() onConfirm;
  final Function() onBack;
  final String? Function(String?) validateName;
  final String? Function(String?) validateTitle;
  final String? Function(String?) validateCompany;
  final double Function(String) calculateFontSize;
  final bool isConfirmView;

  @override
  Widget build(BuildContext context) {
    if (isConfirmView) {
      return _buildConfirmView(context);
    } else {
      return _buildMainView(context);
    }
  }

  Widget _buildMainView(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: AppBackButton(
          onPressed: () => onBack(),
        ),
      ),
      body: Column(
        children: [
          // 卡片预览区域 - 居中显示
          Expanded(
            child: Center(
              child: _buildMetalCardPreview(),
            ),
          ),

          // 表单输入区域 - 直接放在页面底部
          Container(
            decoration: const BoxDecoration(
              color: ColorName.cardColorDark,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            padding: const EdgeInsets.all(24),
            child: Form(
              key: formKey,
              child: Column(
                children: [
                  _buildMetalFormFields(),
                  const SizedBox(height: 32),
                  _buildMetalBottomButton(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmView(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        leading: AppBackButton(
          onPressed: () => onBack(),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 顶部AppBar和标题
            const SizedBox(height: 24),
            const Text(
              'Please check again before\nprinting:',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            // 卡片预览区域
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(height: 20),
                    // Front标签
                    const Text(
                      'Front',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // 正面卡片
                    _buildMetalCardPreview(),
                    const SizedBox(height: 40),
                    // Back标签
                    const Text(
                      'Back',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // 背面卡片
                    _buildMetalBackCard(),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),

            // 底部确认按钮
            Container(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 20),
              child: SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: isLoading ? null : onConfirm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorName.themeColorDark,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 0,
                  ),
                  child: isLoading
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : const Text(
                          'Confirm',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildLightningBackground() {
  //   return Container(
  //     decoration: const BoxDecoration(
  //       color: Colors.black,
  //     ),
  //     child: Stack(
  //       children: [
  //         // 闪电效果 - 简化版
  //         Positioned(
  //           top: 100,
  //           right: 50,
  //           child: Container(
  //             width: 100,
  //             height: 200,
  //             decoration: BoxDecoration(
  //               gradient: LinearGradient(
  //                 begin: Alignment.topLeft,
  //                 end: Alignment.bottomRight,
  //                 colors: [
  //                   Colors.purple.withValues(alpha: 0.3),
  //                   Colors.blue.withValues(alpha: 0.2),
  //                   Colors.transparent,
  //                 ],
  //               ),
  //             ),
  //           ),
  //         ),
  //         Positioned(
  //           bottom: 200,
  //           left: 30,
  //           child: Container(
  //             width: 120,
  //             height: 150,
  //             decoration: BoxDecoration(
  //               gradient: LinearGradient(
  //                 begin: Alignment.bottomLeft,
  //                 end: Alignment.topRight,
  //                 colors: [
  //                   Colors.purple.withValues(alpha: 0.2),
  //                   Colors.transparent,
  //                 ],
  //               ),
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildMetalCardPreview() {
    return Container(
      width: 280,
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // 使用 metalFrontcover 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Assets.icons.images.metalFrontcover.svg(
                fit: BoxFit.contain,
              ),
            ),
          ),
          // 文字内容覆盖在背景上
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  nameController.text.isEmpty ? 'Your name' : nameController.text,
                  style: TextStyle(
                    fontSize: calculateFontSize(nameController.text) * 1.3,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (titleController.text.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    titleController.text,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
                if (companyController.text.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    companyController.text,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetalBackCard() {
    return Container(
      width: 280,
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Assets.icons.images.metalBackcover.svg(
          fit: BoxFit.contain,
        ),
      ),
    );
  }

  Widget _buildMetalFormFields() {
    return Column(
      children: [
        // 姓名输入框
        TextFormField(
          controller: nameController,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1A1A1A),
            hintText: 'Your name',
            hintStyle: const TextStyle(color: Colors.grey),
            errorStyle: const TextStyle(
              color: Colors.redAccent,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ColorName.themeColorDark, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.redAccent, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.redAccent, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateName,
          onChanged: (value) => {/* setState will be handled by parent */},
        ),
        const SizedBox(height: 16),

        // 职位输入框
        TextFormField(
          controller: titleController,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1A1A1A),
            hintText: 'Title (Optional)',
            hintStyle: const TextStyle(color: Colors.grey),
            errorStyle: const TextStyle(
              color: Colors.redAccent,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ColorName.themeColorDark, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.redAccent, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.redAccent, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateTitle,
          onChanged: (value) => {/* setState will be handled by parent */},
        ),
        const SizedBox(height: 16),

        // 公司输入框
        TextFormField(
          controller: companyController,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF1A1A1A),
            hintText: 'Company (Optional)',
            hintStyle: const TextStyle(color: Colors.grey),
            errorStyle: const TextStyle(
              color: Colors.redAccent,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ColorName.themeColorDark, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.redAccent, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.redAccent, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateCompany,
          onChanged: (value) => {/* setState will be handled by parent */},
        ),
      ],
    );
  }

  Widget _buildMetalBottomButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: onNext,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorName.themeColorDark,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: const Text(
          'Next',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}
