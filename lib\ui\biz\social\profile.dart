import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/card.dart' show EthccProfile, Social, SocialPlatform;
import '/models/user.dart' show UserInfo, UserRelation;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart';
import '/provider/user.dart' show fetchUserRelationProvider;
import '/ui/widgets/social/data.dart' show SocialSvgIcon;

@FFRoute(name: '/social/profile')
class SocialProfilePage extends ConsumerStatefulWidget {
  const SocialProfilePage({
    super.key,
    required this.code,
    this.profile,
  });

  final String code;
  final UserInfo? profile;

  @override
  ConsumerState<SocialProfilePage> createState() => _SocialProfilePageState();
}

class _SocialProfilePageState extends ConsumerState<SocialProfilePage> {
  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      overrides: [_dataProvider.overrideWithValue((widget.code, widget.profile))],
      child: AppScaffold(
        defaultTextStyle: const TextStyle(color: Colors.white),
        backgroundColor: ColorName.backgroundColorDark,
        body: CustomScrollView(
          slivers: [
            const SliverGap.v(40.0),
            const _Header(),
            const SliverGap.v(20.0),
            Consumer(
              builder: (context, ref, _) {
                final isETHCCMode = switch (widget.profile) {
                  UserInfo() => ref.watch(validateETHCCProfileProvider(validateProfile: true)),
                  _ => ref.watch(validateETHCCProfileByCodeProvider(code: widget.code)).valueOrNull,
                };
                if (isETHCCMode != true) {
                  return const SliverToBoxAdapter();
                }
                return const _ETHCCProfile();
              },
            ),
            const _SocialCardList(),
            const SliverGap.v(80.0),
          ],
        ),
        bottomButtonBuilder: (_) => const _Relation(),
      ),
    );
  }
}

final _dataProvider = Provider.autoDispose<(String, UserInfo?)>(
  (ref) => throw UnimplementedError(),
);

class _Header extends ConsumerWidget {
  const _Header();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, profile) = ref.watch(_dataProvider);
    final profileResult = ref.watch(fetchPublicProfileProvider(code: code));
    final effectiveProfile = profileResult.valueOrNull ?? profile;
    return SliverToBoxAdapter(
      child: Column(
        spacing: 6.0,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: ColorName.dividerColorDark,
                width: 2,
              ),
            ),
            child: ClipOval(
              child: Builder(
                builder: (context) {
                  if (profile == null && profileResult.isLoading) {
                    return const AppLoading();
                  }
                  if (effectiveProfile == null) {
                    return _buildAvatarPlaceholder(context);
                  }
                  return effectiveProfile.run(
                    (it) => it.avatar.isNotEmpty
                        ? MEImage(
                            it.avatar,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => _buildAvatarPlaceholder(context),
                          )
                        : _buildAvatarPlaceholder(context),
                  );
                },
              ),
            ),
          ),
          const Gap.v(12.0),
          Text(
            effectiveProfile?.run((it) => it.name.isNotEmpty ? it.name : '(Name)') ?? '',
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: ColorName.primaryTextColorDark,
            ),
            textAlign: TextAlign.center,
          ),
          if (effectiveProfile?.title case final title? when title.isNotEmpty)
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: ColorName.captionTextColorDark,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          if (effectiveProfile?.company case final company? when company.isNotEmpty)
            Text(
              company,
              style: const TextStyle(
                fontSize: 16,
                color: ColorName.captionTextColorDark,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }

  Widget _buildAvatarPlaceholder(BuildContext context) {
    return FittedBox(
      fit: BoxFit.cover,
      child: Icon(
        Icons.account_circle,
        color: context.themeColor,
      ),
    );
  }
}

Widget _buildTitle(BuildContext context, String title) {
  return Padding(
    padding: const EdgeInsets.only(top: 12.0),
    child: Text(
      title,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
      ),
    ),
  );
}

class _ETHCCProfile extends ConsumerWidget {
  const _ETHCCProfile();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, _) = ref.watch(_dataProvider);
    final result = ref.watch(fetchEthccProfileProvider(code: code));
    return SliverToBoxAdapter(
      child: AnimatedSize(
        duration: kThemeAnimationDuration,
        curve: Curves.easeInOutCubic,
        child: result.maybeWhen(
          data: (data) {
            if (data == null || data.isEmpty) {
              return const SizedBox.shrink();
            }
            return _buildProfile(context, data);
          },
          orElse: () => const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _buildProfile(BuildContext context, EthccProfile profile) {
    return Column(
      spacing: 6.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (profile.roles.isNotEmpty) ...[
          _buildTitle(context, 'I am a/an'),
          _buildItems(context, profile.roles),
        ],
        if (profile.topics.isNotEmpty) ...[
          _buildTitle(context, 'Talk to me about'),
          _buildItems(context, profile.topics),
        ],
        if (profile.githubHandle.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 12.0),
            child: _GitHubContributions(profile.githubHandle),
          ),
      ],
    );
  }

  Widget _buildItems(
    BuildContext context,
    List<String> items, {
    void Function(String item)? onItemTap,
  }) {
    return Wrap(
      spacing: 6.0,
      runSpacing: 6.0,
      children: items
          .map(
            (item) => RippleTap(
              onTap: () => onItemTap?.call(item),
              padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
              shape: RoundedRectangleBorder(
                borderRadius: RadiusConstants.max,
                side: BorderSide(color: context.themeColor, width: 2.0),
              ),
              child: Text(
                item,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          )
          .toList(),
    );
  }
}

class _GitHubContributions extends ConsumerWidget {
  const _GitHubContributions(this.handle);

  final String handle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final result = ref.watch(fetchGitHubContributionsProvider(handle: handle));
    final noDataWidget = Container(
      height: 129.0,
      alignment: Alignment.center,
      child: const Text(
        'No data available.',
        style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
      ),
    );
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 20),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: Column(
        spacing: 12.0,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: handle,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: "'s contributions in the last year"),
                ],
              ),
            ),
          ),
          result.when(
            data: (data) {
              if (data == null) {
                return noDataWidget;
              }
              return GitHubContributionGraph(collection: data);
            },
            loading: () => const SizedBox(height: 129.0, child: AppLoading()),
            error: (e, s) => noDataWidget,
          ),
        ],
      ),
    );
  }
}

class _SocialItem {
  const _SocialItem({
    required this.platform,
    required this.handle,
    required this.isVerified,
  });

  final SocialPlatform platform;
  final String? handle;
  final bool isVerified;
}

class _SocialCardList extends ConsumerWidget {
  const _SocialCardList();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final (code, _) = ref.watch(_dataProvider);
    final socialsAsync = ref.watch(fetchSocialsProvider(code: code));
    return SliverToBoxAdapter(
      child: AnimatedSize(
        duration: kThemeAnimationDuration,
        curve: Curves.easeInOutCubic,
        child: socialsAsync.maybeWhen(
          data: (data) => _buildSocialCardsList(context, data),
          orElse: () => const SizedBox.shrink(),
        ),
      ),
    );
  }

  Widget _buildSocialCardsList(BuildContext context, List<Social> socials) {
    final socialItems = <_SocialItem>[];
    for (final social in socials) {
      final platform = SocialPlatform.fromName(social.platformName);
      if (platform != null) {
        socialItems.add(
          _SocialItem(
            platform: platform,
            handle: social.handleName,
            isVerified: social.isVerify,
          ),
        );
      }
    }
    if (socialItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      spacing: 16.0,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildTitle(context, 'Contact me on'),
        ...socialItems.map(
          (item) => ProviderScope(
            overrides: [_socialItemProvider.overrideWithValue(item)],
            child: const _SocialCard(),
          ),
        ),
      ],
    );
  }
}

final _socialItemProvider = Provider.autoDispose<_SocialItem>(
  (ref) => throw UnimplementedError(),
);

class _SocialCard extends ConsumerWidget {
  const _SocialCard();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final item = ref.watch(_socialItemProvider);
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: ColorName.cardColorDark,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: ColorName.dividerColorDark,
          width: 1,
        ),
      ),
      child: Row(
        spacing: 16.0,
        children: [
          Container(
            width: 48.0,
            height: 48.0,
            padding: const EdgeInsets.all(10.0),
            decoration: const BoxDecoration(
              color: Colors.black,
              shape: BoxShape.circle,
            ),
            child: SocialSvgIcon(platform: item.platform),
          ),
          Expanded(
            child: Text(
              item.handle ?? '(---)',
              style: const TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.w600,
                color: ColorName.primaryTextColorDark,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          if (item.isVerified) Assets.icons.verified.svg(width: 24, height: 24),
        ],
      ),
    );
  }
}

final _relationLoadingProvider = StateProvider.autoDispose<bool>((ref) => false);

class _Relation extends ConsumerStatefulWidget {
  const _Relation();

  @override
  ConsumerState<_Relation> createState() => _RelationState();
}

class _RelationState extends ConsumerState<_Relation> {
  late final code = ref.read(_dataProvider).$1;
  late UserRelation? _userRelation = ref.read(fetchUserRelationProvider(code: code)).valueOrNull;

  Future<void> _toggleFollowing(UserRelation relation) async {
    final (code, _) = ref.read(_dataProvider);
    ref.read(_relationLoadingProvider.notifier).state = true;
    try {
      final newRelation = await ref
          .read(apiServiceProvider)
          .toggleUserFollow(
            code: code,
            follow: !relation.following,
          );
      if (mounted) {
        _userRelation = newRelation;
        setState(() {});
      }
    } finally {
      if (mounted) {
        ref.read(_relationLoadingProvider.notifier).state = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final (code, _) = ref.watch(_dataProvider);
    final result = ref.watch(fetchUserRelationProvider(code: code));
    return AnimatedSize(
      duration: kThemeAnimationDuration,
      curve: Curves.easeInOutCubic,
      child: result.maybeWhen(
        skipLoadingOnRefresh: false,
        data: (data) {
          final relation = _userRelation ?? data;
          if (relation == null) {
            return const SizedBox.shrink();
          }
          return _buildButton(relation);
        },
        orElse: () => _userRelation != null ? _buildButton(_userRelation!) : const SizedBox.shrink(),
      ),
    );
  }

  Widget _buildButton(UserRelation relation) {
    return Consumer(
      builder: (context, ref, child) {
        final loading = ref.watch(_relationLoadingProvider);
        return ThemeTextButton(
          onPressed: () => _toggleFollowing(relation),
          child: loading
              ? const AppLoading()
              : Text(
                  switch (relation) {
                    UserRelation(following: true, followedBy: true) => 'Mutual Following',
                    UserRelation(following: true, followedBy: _) => 'Following',
                    UserRelation(following: false, followedBy: _) => 'Follow',
                  },
                ),
        );
      },
    );
  }
}
