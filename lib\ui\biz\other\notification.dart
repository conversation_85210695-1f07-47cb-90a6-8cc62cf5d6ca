import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/internals/date_time.dart';
import '/models/business.dart';
import '/provider/api.dart';
import '/provider/business.dart';
import '/provider/chain.dart';
import '/provider/settings.dart';

@FFRoute(name: '/notification')
class NotificationPage extends ConsumerStatefulWidget {
  const NotificationPage({super.key});

  @override
  ConsumerState<NotificationPage> createState() => _NotificationPageState();
}

class _NotificationPageState extends ConsumerState<NotificationPage> {
  final scrollController = ScrollController();
  bool isLoading = true;
  bool isLoadingMore = false;
  bool hasMoreData = true;
  int currentPage = 1;
  final int pageSize = 10;
  List<Message> messages = [];

  @override
  void initState() {
    super.initState();
    // 添加滚动监听器实现无限滚动
    scrollController.addListener(_scrollListener);
    _fetchMessages();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    // 检查是否需要加载更多
    final bool needLoadMore = scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200;

    if (needLoadMore && !isLoadingMore && hasMoreData) {
      _loadMoreMessages();
    }
  }

  Future<void> _fetchMessages() async {
    setState(() {
      isLoading = true;
    });
    try {
      final result = await ref
          .read(apiServiceProvider)
          .listMessages(
            pageNum: currentPage,
            pageSize: pageSize,
          );

      messages = List<Message>.from(result.list);
      isLoading = false;
      // 判断是否还有更多数据
      hasMoreData = result.page < result.pages;
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToLoadNotifications);
    } finally {
      safeSetState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _loadMoreMessages() async {
    if (isLoadingMore || !hasMoreData) {
      return;
    }

    try {
      setState(() {
        isLoadingMore = true;
        currentPage++;
      });

      final result = await ref
          .read(apiServiceProvider)
          .listMessages(
            pageNum: currentPage,
            pageSize: pageSize,
          );
      setState(() {
        messages.addAll(List<Message>.from(result.list));
        isLoadingMore = false;
        // 判断是否还有更多数据
        hasMoreData = result.page < result.pages;
      });
    } catch (e) {
      setState(() {
        isLoadingMore = false;
        currentPage--; // 恢复页码
      });
      Card3ToastUtil.showToast(message: ToastMessages.failedToLoadMore);
    }
  }

  void showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  Future<void> _switchNetwork(Network network) async {
    // 检查全局网络切换状态
    final isSwitching = ref.read(networkSwitchingProvider);
    if (isSwitching) {
      return;
    }

    try {
      // 切换网络 (provider内部会设置 networkSwitchingProvider 状态)
      await ref.read(currentNetworkProvider.notifier).switchNetwork(network);

      if (mounted) {
        Navigator.pop(context, network);
      }
    } catch (e) {
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToSwitchNetwork);
      }
    }
  }

  // 从通知消息中提取内容
  Map<String, String> extractBracketContent(String content) {
    final RegExp bracketRegex = RegExp(r'\[(.*?)\]');
    final match = bracketRegex.firstMatch(content);
    String? extractedContent;
    String cleanedText = content;

    if (match != null) {
      extractedContent = match.group(1);
      cleanedText = content.replaceAll(bracketRegex, '').trim();
    }

    return {
      'extractedContent': extractedContent ?? '',
      'cleanedText': cleanedText,
    };
  }

  // 根据消息类型渲染不同的图标
  Widget _renderIcon(String messageType) {
    switch (messageType) {
      case 'PUSH':
        return const Icon(
          Icons.chat_bubble,
          size: 24,
          color: Colors.black,
        );
      case 'SAY_HI':
        return const Icon(
          Icons.waving_hand,
          size: 24,
          color: Colors.black,
        );
      case 'AIRDROP':
        return const Icon(
          Icons.paid,
          size: 24,
          color: Colors.black,
        );
      case 'INTEGRAL':
        return const Icon(
          Icons.redeem,
          size: 24,
          color: Colors.black,
        );
      default:
        return const Icon(
          Icons.notifications,
          size: 24,
          color: Colors.black,
        );
    }
  }

  // 根据消息类型渲染内容
  Widget _renderContent(Message message) {
    final content = message.message;
    final primaryColor = Theme.of(context).primaryColor;

    switch (message.messageType) {
      case 'PUSH':
        final extracted = extractBracketContent(content);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 8),
            Text(
              extracted['cleanedText']!,
              style: const TextStyle(fontSize: 16),
            ),
            if (extracted['extractedContent']!.isNotEmpty)
              Row(
                children: [
                  Text(
                    '${extracted['extractedContent']!.split('|')[0]} :',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(width: 4),
                  GestureDetector(
                    onTap: () {
                      // 打开链接
                      // TODO: 实现链接跳转
                    },
                    child: Text(
                      extracted['extractedContent']!.split('|')[1],
                      style: TextStyle(
                        fontSize: 16,
                        color: primaryColor,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              ),
          ],
        );

      case 'AIRDROP':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            Text(content, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            GestureDetector(
              child: Text(
                'Check it out now',
                style: TextStyle(
                  fontSize: 16,
                  color: primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        );

      case 'INTEGRAL':
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 12),
            Text(content, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            GestureDetector(
              child: Text(
                'Check it out now',
                style: TextStyle(
                  fontSize: 16,
                  color: primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ],
        );

      default:
        return Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: Text(content, style: const TextStyle(fontSize: 16)),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const AppScaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return AppScaffold(
      body: messages.isEmpty ? _buildEmptyState() : _buildMessageList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageList() {
    final networks = ref.watch(fetchNetworksProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Text(
              'Notifications',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
          ),
          Expanded(
            child: ListView.builder(
              controller: scrollController,
              itemCount: messages.length + (isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == messages.length) {
                  return const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: Center(child: CircularProgressIndicator()),
                  );
                }

                final message = messages[index];
                return GestureDetector(
                  onTap: () {
                    if (message.messageType == 'INTEGRAL') {
                      meNavigator.pushNamed(Routes.funPointRecord.name);
                    }
                    if (message.messageType == 'AIRDROP') {
                      ref.read(selectedIndexProvider.notifier).state = 2;

                      // 解析 JSON 字符串
                      final params = jsonDecode(message.params);
                      final chainId = params['chainId'];

                      // 从 networks 中找到对应的 Network
                      final network = networks.when(
                        data: (data) => data.firstWhere(
                          (network) => network.id == chainId,
                          orElse: () => data.first,
                        ),
                        loading: () => null,
                        error: (_, _) => null,
                      );

                      if (network != null) {
                        _switchNetwork(network);
                      }
                      meNavigator.popAndPushNamed(Routes.home.name);
                    }
                  },
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _renderIcon(message.messageType),
                              Text(
                                formatDateTime(message.createTime),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                          _renderContent(message),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
