import 'package:flutter/widgets.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_misc/me_misc.dart';

import '../l10n/gen/app_localizations.dart';

extension BuildContextExtension on BuildContext {
  AppLocalizations get l10n => AppLocalizations.of(this)!;
}

AppLocalizations get globalL10n => meContext.l10n;

MELocalizations get globalL10nME => meContext.l10nME;

extension UIKit on BuildContext {
  MediaQueryData get mediaQueryData => MediaQuery.of(this);

  Size get screenSize => MediaQuery.sizeOf(this);

  double get devicePixelRatio => MediaQuery.devicePixelRatioOf(this);

  double get screenWidth => screenSize.width;

  double get screenHeight => screenSize.height;

  double get screenRatio => screenSize.aspectRatio;

  EdgeInsets get safePadding => MediaQuery.paddingOf(this);

  double get statusBarHeight => safePadding.top;

  double get navBarHeight => safePadding.bottom;
}
