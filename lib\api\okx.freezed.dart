// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'okx.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$OkxTicker {
  @Json<PERSON>ey(name: 'instId')
  String get instId;
  @JsonKey(name: 'last')
  double get last;
  @JsonKey(name: 'askPx')
  double get askPx;
  @JsonKey(name: 'askSz')
  double get askSz;
  @JsonKey(name: 'bidPx')
  double get bidPx;
  @JsonKey(name: 'bidSz')
  double get bidSz;

  /// Create a copy of OkxTicker
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $OkxTickerCopyWith<OkxTicker> get copyWith =>
      _$OkxTickerCopyWithImpl<OkxTicker>(this as OkxTicker, _$identity);

  /// Serializes this OkxTicker to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is OkxTicker &&
            (identical(other.instId, instId) || other.instId == instId) &&
            (identical(other.last, last) || other.last == last) &&
            (identical(other.askPx, askPx) || other.askPx == askPx) &&
            (identical(other.askSz, askSz) || other.askSz == askSz) &&
            (identical(other.bidPx, bidPx) || other.bidPx == bidPx) &&
            (identical(other.bidSz, bidSz) || other.bidSz == bidSz));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, instId, last, askPx, askSz, bidPx, bidSz);

  @override
  String toString() {
    return 'OkxTicker(instId: $instId, last: $last, askPx: $askPx, askSz: $askSz, bidPx: $bidPx, bidSz: $bidSz)';
  }
}

/// @nodoc
abstract mixin class $OkxTickerCopyWith<$Res> {
  factory $OkxTickerCopyWith(OkxTicker value, $Res Function(OkxTicker) _then) =
      _$OkxTickerCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'instId') String instId,
    @JsonKey(name: 'last') double last,
    @JsonKey(name: 'askPx') double askPx,
    @JsonKey(name: 'askSz') double askSz,
    @JsonKey(name: 'bidPx') double bidPx,
    @JsonKey(name: 'bidSz') double bidSz,
  });
}

/// @nodoc
class _$OkxTickerCopyWithImpl<$Res> implements $OkxTickerCopyWith<$Res> {
  _$OkxTickerCopyWithImpl(this._self, this._then);

  final OkxTicker _self;
  final $Res Function(OkxTicker) _then;

  /// Create a copy of OkxTicker
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? instId = null,
    Object? last = null,
    Object? askPx = null,
    Object? askSz = null,
    Object? bidPx = null,
    Object? bidSz = null,
  }) {
    return _then(
      _self.copyWith(
        instId: null == instId
            ? _self.instId
            : instId // ignore: cast_nullable_to_non_nullable
                  as String,
        last: null == last
            ? _self.last
            : last // ignore: cast_nullable_to_non_nullable
                  as double,
        askPx: null == askPx
            ? _self.askPx
            : askPx // ignore: cast_nullable_to_non_nullable
                  as double,
        askSz: null == askSz
            ? _self.askSz
            : askSz // ignore: cast_nullable_to_non_nullable
                  as double,
        bidPx: null == bidPx
            ? _self.bidPx
            : bidPx // ignore: cast_nullable_to_non_nullable
                  as double,
        bidSz: null == bidSz
            ? _self.bidSz
            : bidSz // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _OkxTicker implements OkxTicker {
  const _OkxTicker({
    @JsonKey(name: 'instId') required this.instId,
    @JsonKey(name: 'last') required this.last,
    @JsonKey(name: 'askPx') required this.askPx,
    @JsonKey(name: 'askSz') required this.askSz,
    @JsonKey(name: 'bidPx') required this.bidPx,
    @JsonKey(name: 'bidSz') required this.bidSz,
  });
  factory _OkxTicker.fromJson(Map<String, dynamic> json) =>
      _$OkxTickerFromJson(json);

  @override
  @JsonKey(name: 'instId')
  final String instId;
  @override
  @JsonKey(name: 'last')
  final double last;
  @override
  @JsonKey(name: 'askPx')
  final double askPx;
  @override
  @JsonKey(name: 'askSz')
  final double askSz;
  @override
  @JsonKey(name: 'bidPx')
  final double bidPx;
  @override
  @JsonKey(name: 'bidSz')
  final double bidSz;

  /// Create a copy of OkxTicker
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$OkxTickerCopyWith<_OkxTicker> get copyWith =>
      __$OkxTickerCopyWithImpl<_OkxTicker>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$OkxTickerToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _OkxTicker &&
            (identical(other.instId, instId) || other.instId == instId) &&
            (identical(other.last, last) || other.last == last) &&
            (identical(other.askPx, askPx) || other.askPx == askPx) &&
            (identical(other.askSz, askSz) || other.askSz == askSz) &&
            (identical(other.bidPx, bidPx) || other.bidPx == bidPx) &&
            (identical(other.bidSz, bidSz) || other.bidSz == bidSz));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, instId, last, askPx, askSz, bidPx, bidSz);

  @override
  String toString() {
    return 'OkxTicker(instId: $instId, last: $last, askPx: $askPx, askSz: $askSz, bidPx: $bidPx, bidSz: $bidSz)';
  }
}

/// @nodoc
abstract mixin class _$OkxTickerCopyWith<$Res>
    implements $OkxTickerCopyWith<$Res> {
  factory _$OkxTickerCopyWith(
    _OkxTicker value,
    $Res Function(_OkxTicker) _then,
  ) = __$OkxTickerCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'instId') String instId,
    @JsonKey(name: 'last') double last,
    @JsonKey(name: 'askPx') double askPx,
    @JsonKey(name: 'askSz') double askSz,
    @JsonKey(name: 'bidPx') double bidPx,
    @JsonKey(name: 'bidSz') double bidSz,
  });
}

/// @nodoc
class __$OkxTickerCopyWithImpl<$Res> implements _$OkxTickerCopyWith<$Res> {
  __$OkxTickerCopyWithImpl(this._self, this._then);

  final _OkxTicker _self;
  final $Res Function(_OkxTicker) _then;

  /// Create a copy of OkxTicker
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? instId = null,
    Object? last = null,
    Object? askPx = null,
    Object? askSz = null,
    Object? bidPx = null,
    Object? bidSz = null,
  }) {
    return _then(
      _OkxTicker(
        instId: null == instId
            ? _self.instId
            : instId // ignore: cast_nullable_to_non_nullable
                  as String,
        last: null == last
            ? _self.last
            : last // ignore: cast_nullable_to_non_nullable
                  as double,
        askPx: null == askPx
            ? _self.askPx
            : askPx // ignore: cast_nullable_to_non_nullable
                  as double,
        askSz: null == askSz
            ? _self.askSz
            : askSz // ignore: cast_nullable_to_non_nullable
                  as double,
        bidPx: null == bidPx
            ? _self.bidPx
            : bidPx // ignore: cast_nullable_to_non_nullable
                  as double,
        bidSz: null == bidSz
            ? _self.bidSz
            : bidSz // ignore: cast_nullable_to_non_nullable
                  as double,
      ),
    );
  }
}
