import 'dart:typed_data';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/api.dart' show apiServiceProvider;
import '/provider/user.dart' show fetchUserInfoProvider;
import '/ui/widgets/social/profile/avatar_img.dart';

class Avatar extends ConsumerStatefulWidget {
  const Avatar({super.key, this.socialId, this.isSingleLink = false});

  final int? socialId;
  final bool isSingleLink;

  @override
  ConsumerState<Avatar> createState() => _AvatarState();
}

class _AvatarState extends ConsumerState<Avatar> {
  Uint8List? _imageBytes;
  String? _avatarUrl;
  String? _base64Image;
  bool _isUploading = false;
  bool _isLoading = true;
  final GlobalKey<AvatarImgPickerState> _pickerKey = GlobalKey<AvatarImgPickerState>();

  @override
  void initState() {
    super.initState();
    _loadCurrentAvatar();
  }

  Future<void> _loadCurrentAvatar() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 获取用户信息
      final userInfo = await ref.read(fetchUserInfoProvider().future);

      if (userInfo.avatar.isNotEmpty) {
        _avatarUrl = userInfo.avatar;
        _isLoading = false;
      } else {
        _isLoading = false;
      }
      safeSetState(() {});
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  void _handleImageSelected(String base64Image, Uint8List imageBytes) {
    setState(() {
      _avatarUrl = base64Image;
      _imageBytes = imageBytes;
      _base64Image = base64Image;
    });
  }

  // 处理上传按钮点击
  void _handleUploadButtonPressed() {
    if (_imageBytes != null) {
      // 如果已经有图片，则上传
      _uploadAvatar();
    } else {
      // 如果没有图片，则打开图片选择器
      _openImagePicker();
    }
  }

  // 打开图片选择器
  void _openImagePicker() {
    // 如果AvatarImgPicker组件可用，调用其pickImage方法
    if (_pickerKey.currentState != null) {
      _pickerKey.currentState!.pickImage();
    } else {
      // 如果组件不可用，显示错误消息
      LogUtil.e('AvatarImgPicker组件不可用，无法调用pickImage');
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.failedToOpenImagePicker);
      }
    }
  }

  Future<void> _uploadAvatar() async {
    if (_imageBytes == null || _base64Image == null) {
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      // 直接使用AvatarImgPicker已经压缩好的图片
      // AvatarImgPicker中已经将图片压缩到最大650x650像素

      // 将压缩后的图片上传到服务器
      final url = await ref.read(apiServiceProvider).uploadAvatar(fileContent: _base64Image!);

      // 更新用户信息
      await ref.read(apiServiceProvider).updateUserInfo(avatar: url);

      // 刷新用户信息
      ref.invalidate(fetchUserInfoProvider);

      if (mounted) {
        Navigator.pop(context);
        Card3ToastUtil.showToast(message: ToastMessages.uploadAvatarSuccess);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      if (mounted) {
        Card3ToastUtil.showToast(message: ToastMessages.uploadAvatarError);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _deleteAvatar() {
    return AppLoading.run(() async {
      await ref.read(apiServiceProvider).updateUserInfo(avatar: '');
      ref.invalidate(fetchUserInfoProvider);
      Navigator.pop(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16).copyWith(bottom: 0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题与关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Upload new avatar',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 头像预览区域 - 使用AvatarImgPicker组件
          Expanded(
            child: Center(
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        // 头像选择器
                        AvatarImgPicker(
                          key: _pickerKey,
                          avatarUrl: _avatarUrl,
                          onImageSelected: _handleImageSelected,
                          size: 240,
                        ),

                        // 图片选择提示
                        if (_imageBytes == null)
                          Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: TextButton.icon(
                              onPressed: _openImagePicker,
                              icon: const Icon(Icons.photo_library, size: 20),
                              label: const Text(
                                'Select from Gallery',
                                style: TextStyle(fontSize: 16),
                              ),
                            ),
                          ),
                      ],
                    ),
            ),
          ),

          // 底部按钮
          Row(
            children: [
              // 取消按钮
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red.shade300),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: TextButton(
                  onPressed: () => _deleteAvatar(),
                  style: TextButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: Text(
                    'Clear',
                    style: TextStyle(
                      color: Colors.red.shade400,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // 上传按钮
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF8560FA),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: TextButton.icon(
                    icon: _isUploading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Icon(Icons.upload, color: Colors.white),
                    label: Text(
                      _isUploading ? 'Uploading...' : (_imageBytes == null ? 'Choose Image' : 'Upload'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    onPressed: _isUploading ? null : _handleUploadButtonPressed,
                    style: TextButton.styleFrom(
                      backgroundColor: const Color(0xFF8560FA),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const Gap.v(50.0),
        ],
      ),
    );
  }
}
