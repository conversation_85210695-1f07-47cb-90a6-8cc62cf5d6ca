import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RefCanceledException extends Error {}

extension RefExtension on Ref {
  CancelToken cancelToken() {
    final cancelToken = CancelToken();
    onDispose(cancelToken.cancel);
    return cancelToken;
  }

  Future<void> debouncing({
    Duration delay = const Duration(milliseconds: 500),
    CancelToken? cancelToken,
  }) {
    final ct = cancelToken ?? this.cancelToken();
    return Future<void>.delayed(delay).whenComplete(() {
      if (ct.isCancelled) {
        throw RefCanceledException();
      }
    });
  }
}

extension AutoDisposeRefExtension on Ref {
  ({KeepAliveLink link, CancelToken cancelToken}) autoDisposeWithCancelTokenDelay({
    Duration delay = const Duration(seconds: 60),
  }) {
    final cancelToken = CancelToken();
    final link = keepAlive();
    Timer? timer;
    onDispose(() {
      timer?.cancel();
      cancelToken.cancel();
    });
    onCancel(() {
      timer?.cancel();
      timer = Timer(delay, link.close);
    });
    onResume(() {
      timer?.cancel();
    });
    return (link: link, cancelToken: cancelToken);
  }

  KeepAliveLink autoDisposeDelay({
    Duration delay = const Duration(seconds: 60),
  }) {
    final link = keepAlive();
    Timer? timer;
    onDispose(() {
      timer?.cancel();
    });
    onCancel(() {
      timer?.cancel();
      timer = Timer(delay, link.close);
    });
    onResume(() {
      timer?.cancel();
    });
    return link;
  }
}
