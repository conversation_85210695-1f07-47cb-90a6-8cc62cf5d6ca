import 'package:card3/exports.dart';

@FFAutoImport()
import 'package:card3/models/business.dart';

import 'package:flutter/material.dart';

import '/provider/chain.dart';
import '/provider/token.dart';
import '/services/select_token.dart';

@FFRoute(name: '/wallet/send')
class SendPage extends ConsumerStatefulWidget {
  const SendPage({
    super.key,
    this.tokenBalance,
  });

  final TokenBalance? tokenBalance;

  @override
  ConsumerState<SendPage> createState() => _SendPageState();
}

class _SendPageState extends ConsumerState<SendPage> {
  late TokenBalance _selectedToken;
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  bool _isLoading = false;
  String _errorMessage = '';
  String? _txHash;
  bool _showSuccessView = false;

  @override
  void initState() {
    super.initState();
    // 解析传入的参数
    _selectedToken = widget.tokenBalance ?? _getDefaultToken();
  }

  @override
  void dispose() {
    _addressController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  TokenBalance _getDefaultToken() {
    // 默认使用USDC或第一个可用的代币
    final tokenBalances = ref.read(allTokenBalancesProvider).valueOrNull ?? [];

    if (tokenBalances.isEmpty) {
      // 返回一个空的TokenBalance，使用必要的参数
      return TokenBalance(
        token: const Token(
          blockChain: '',
          contractAddress: '',
          createTime: '',
          symbol: 'USDC',
          name: 'USD Coin',
          digits: 6,
          icon: '',
          standard: '',
        ),
        balance: Decimal.zero,
        price: 0,
        usdValue: '0',
      );
    }

    // 尝试找USDC
    final usdcToken = tokenBalances.firstWhere(
      (tb) => tb.token.symbol.toUpperCase() == 'USDC',
      orElse: () => tokenBalances.first,
    );

    return usdcToken;
  }

  Future<void> _handleSend() async {
    // 清除之前的错误
    setState(() {
      _errorMessage = '';
      _isLoading = true;
    });

    try {
      final address = _addressController.text.trim();
      if (address.isEmpty) {
        setState(() {
          _errorMessage = 'Please enter the receiving address';
          _isLoading = false;
        });
        return;
      }

      // 验证地址格式
      // if ( address.isNotEmpty && !address.startsWith('0x') || address.length != 42) {
      //   setState(() {
      //     _errorMessage = 'Invalid address format';
      //     _isLoading = false;
      //   });
      //   return;
      // }

      // 解析金额
      Decimal amount;
      try {
        amount = Decimal.parse(_amountController.text);
      } catch (e) {
        setState(() {
          _errorMessage = 'Invalid amount';
          _isLoading = false;
        });
        return;
      }

      // 检查金额是否足够
      if (amount <= Decimal.zero || amount > _selectedToken.balance) {
        setState(() {
          _errorMessage = 'Invalid amount or insufficient balance';
          _isLoading = false;
        });
        return;
      }

      // 执行发送交易
      final sendResult = await _sendTransaction(address, amount);

      // 处理交易结果
      if (sendResult) {
        if (mounted) {
          setState(() {
            _showSuccessView = true;
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<bool> _sendTransaction(String address, Decimal amount) async {
    try {
      // 获取当前选择的网络
      final currentNetwork = ref.watch(currentNetworkProvider);
      if (currentNetwork == null) {
        setState(() {
          _errorMessage = 'Network not initialized';
        });
        return false;
      }

      final chainManager = ref.read(chainManagerProvider);
      final token = _selectedToken.token;

      // 检查ChainManager的网络和当前选择的网络是否一致
      if (chainManager.currentNetwork.id != currentNetwork.id) {
        LogUtil.d('检测到网络不一致，尝试同步网络状态...');
        LogUtil.d(
          'ChainManager网络ID: ${chainManager.currentNetwork.id}, '
          'Provider网络ID: ${currentNetwork.id}',
        );

        try {
          // 强制同步网络
          await ref.read(currentNetworkProvider.notifier).switchNetwork(currentNetwork);
          LogUtil.d('网络同步完成');
        } catch (e, s) {
          handleExceptions(error: e, stackTrace: s);
          setState(() {
            _errorMessage = 'Failed to switch network';
          });
          return false;
        }
      }

      // 转换金额为wei单位 - 修复计算逻辑
      final decimals = BigInt.from(10).pow(token.digits);
      final amountInWei = BigInt.parse(
        (amount * Decimal.fromInt(decimals.toInt())).toString().split('.')[0],
      );

      final txHash = await chainManager.sendTransaction(
        token: token,
        to: address,
        value: amountInWei,
      );

      _txHash = txHash;
      ref.invalidate(allTokenBalancesProvider);
      return txHash.isNotEmpty;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      safeSetState(() {
        _errorMessage = 'Transaction failed: ${e.toString()}';
      });
      return false;
    }
  }

  void _checkStatus() {
    if (_txHash != null && _txHash!.isNotEmpty) {
      // 这里可以跳转到区块浏览器查看交易状态
      // 获取当前网络
      final currentNetwork = ref.read(currentNetworkProvider);
      if (currentNetwork != null && currentNetwork.blockExplorers.isNotEmpty) {
        final explorerUrl = '${currentNetwork.blockExplorers.first.url}/tx/$_txHash';
        LogUtil.d('explorerUrl: $explorerUrl');
        // 打开浏览器
        launchUrlString(explorerUrl);
      }
    }
  }

  void _backToWallet() {
    ref.invalidate(allTokenBalancesProvider);
    Navigator.pop(context, true);
  }

  @override
  Widget build(BuildContext context) {
    if (_showSuccessView) {
      return _buildSuccessView();
    }

    final tokenBalances = ref.watch(allTokenBalancesProvider);

    return Scaffold(
      backgroundColor: context.theme.cardColor,
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        elevation: 0,
        leading: const AppBackButton(),
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'To',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // 地址输入框
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.grey.shade300, width: 1),
                  ),
                  child: TextFormField(
                    controller: _addressController,
                    maxLines: 2,
                    minLines: 2,
                    decoration: const InputDecoration(
                      fillColor: Colors.white,
                      filled: true,
                      hintText: 'Enter Receiving Address',
                      hintStyle: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    ),
                  ),
                ),
                const SizedBox(height: 50),

                // 代币选择器
                GestureDetector(
                  onTap: () => showTokenSelection(context, tokenBalances.valueOrNull ?? [], (tokenBalance) {
                    setState(() {
                      _selectedToken = tokenBalance;
                      _amountController.clear();
                    });
                  }),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 20),
                    decoration: const BoxDecoration(
                      color: Color(0xFFEEEEF0),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: const BoxDecoration(
                            color: ColorName.themeColorDark,
                            shape: BoxShape.circle,
                          ),
                          child: _selectedToken.token.icon.isNotEmpty
                              ? MEImage(
                                  _selectedToken.token.icon,
                                  width: 32,
                                  height: 32,
                                  clipOval: true,
                                )
                              : Center(
                                  child: Text(
                                    _selectedToken.token.symbol.substring(0, 1).toUpperCase(),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          _selectedToken.token.symbol,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // 金额输入
                Stack(
                  children: [
                    Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                      ),
                      child: TextFormField(
                        controller: _amountController,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        style: const TextStyle(
                          fontSize: 48,
                          fontWeight: FontWeight.w400,
                        ),
                        decoration: const InputDecoration(
                          fillColor: Colors.white,
                          filled: true,
                          hintText: '0',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 10,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(0),
                              topRight: Radius.circular(0),
                              bottomLeft: Radius.circular(16),
                              bottomRight: Radius.circular(16),
                            ),
                            borderSide: BorderSide.none,
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(0),
                              topRight: Radius.circular(0),
                              bottomLeft: Radius.circular(16),
                              bottomRight: Radius.circular(16),
                            ),
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 12,
                      right: 12,
                      child: ElevatedButton(
                        onPressed: () {
                          // 设置为最大可用余额
                          _amountController.text = _selectedToken.balance.toString();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorName.themeColorDark,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 10,
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'MAX',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                // 可用余额
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                  child: Row(
                    children: [
                      Text(
                        'Available: ',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '${_selectedToken.balance.toStringAsFixed(5)} ${_selectedToken.token.symbol}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),

                // 错误消息
                if (_errorMessage.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: ElevatedButton(
            onPressed: _isLoading ? null : _handleSend,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorName.themeColorDark,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              minimumSize: const Size(double.infinity, 56),
            ),
            child: _isLoading
                ? const CircularProgressIndicator(color: Colors.white)
                : const Text(
                    'Next',
                    style: TextStyle(fontSize: 20, color: Colors.white),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessView() {
    return AppScaffold(
      body: SizedBox(
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Spacer(),
            // 成功图标
            Container(
              width: 80,
              height: 80,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: Assets.icons.check.svg(
                width: 80,
                height: 80,
                colorFilter: ColorName.successColor.filter,
              ),
            ),
            const SizedBox(height: 24),

            // Sent 文本
            const Text(
              'Sent',
              style: TextStyle(
                fontSize: 36,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),

            const Spacer(),

            // Check status 按钮
            ElevatedButton(
              onPressed: _checkStatus,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorName.themeColorDark,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                minimumSize: const Size(double.infinity, 56),
              ),
              child: const Text(
                'Check status',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),

            const SizedBox(height: 36),

            // Back to wallet 文本按钮
            GestureDetector(
              onTap: _backToWallet,
              child: const Text(
                'Back to wallet',
                style: TextStyle(
                  fontSize: 16,
                  color: Color(0xFF8560FA),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
}
