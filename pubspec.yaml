name: card3
description: "card3."
publish_to: 'none'
version: 0.9.3+13

environment:
  sdk: ^3.8.0
  flutter: '>=3.32.2'

me_package:
  url: &me_package_url "https://github.com/AstroxNetwork/me_packages"
  ref: &me_package_ref '0db189f0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Wallets
  privy_flutter: ^0.0.8
  web3dart: ^2.7.3
#  solana: ^0.31.0
  solana:
    git:
      url: https://github.com/AstroxNetwork/espresso-cash-public
      path: 'packages/solana'
      ref: '04fc10d6'

  me_constants: # Override.
  me_extensions: # Override.
  me_fonts: # Override.
  me_l10n: # Override.
  me_misc: # Override.
  me_models: # Override.
  me_ui: # Override.
  me_utils: # Override.

  app_links: 6.4.0
  auto_size_text: ^3.0.0
  cbor: ^6.3.5
  collection: ^1.19.0
  connectivity_plus: ^6.1.3
  convert: ^3.1.2
  crypto: ^3.0.6
  decimal: ^3.0.0
  device_info_plus: ^11.3.0
  dio: ^5.8.0
  extended_image: ^10.0.0
  ff_annotation_route_library: ^3.1.0
  flutter_inappwebview: 6.2.0-beta.2
  flutter_secure_storage: 9.2.4
  flutter_smart_dialog: ^4.9.8+8
  flutter_svg: ^2.0.17
  freezed_annotation: ^3.0.0
  hive_ce: ^2.10.1
  hive_ce_flutter: ^2.2.0
  http: ^1.3.0
  image_editor: ^1.6.0
  image_picker: ^1.1.2
  image_picker_android: # Transparent.
  image_picker_platform_interface: # Transparent.
  in_app_update: ^4.2.3
  intl: # Determined by SDKs.
  json_annotation: ^4.9.0
  lottie: ^3.0.0
  mime: ^2.0.0
  mobile_scanner: ^7.0.0
  package_info_plus: ^8.0.0
  path: ^1.9.0
  path_provider: ^2.0.15
  permission_handler: ^12.0.0
  pin_code_fields: ^8.0.1
  qr_flutter: ^4.1.0
  rational: # Transparent
  share_plus: ^11.0.0
  shimmer: ^3.0.0
  sliver_tools: ^0.2.12
  stack_trace: ^1.11.0
  url_launcher: ^6.3.0
  vector_graphics: # Transparent
  video_player: ^2.10.0

  # Firebase integrations
  firebase_core: '>=3.13.0 <3.14.0'
  firebase_analytics: '>=11.4.6 <11.5.0'
  firebase_crashlytics: '>=4.3.6 <4.4.0'
  firebase_messaging: '>=15.2.0 <15.3.0'
  flutter_local_notifications: ^19.1.0

  # Hooks & Riverpod
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.0

  # NFC
  nfc_manager: ^4.0.2
  nfc_manager_ndef: ^1.0.1
  ndef_record: ^1.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: any

  build_runner: ^2.4.15
  custom_lint: # Transparent
  flutter_gen_runner: ^5.9.0
  freezed: ^3.0.0
#  hive_ce_generator: ^1.8.2
  json_serializable: ^6.9.0
  riverpod_generator: ^2.6.3
  riverpod_lint: ^2.6.3
  yaml: # Transparent

dependency_overrides:
  me_constants:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_constants
  me_extensions:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_extensions
  me_fonts:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_fonts
  me_l10n:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_l10n
  me_misc:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_misc
  me_models:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_models
  me_ui:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_ui
  me_utils:
    git:
      url: *me_package_url
      ref: *me_package_ref
      path: packages/me_utils

#  me_constants:
#    path: ../me_packages/packages/me_constants
#  me_extensions:
#    path: ../me_packages/packages/me_extensions
#  me_fonts:
#    path: ../me_packages/packages/me_fonts
#  me_l10n:
#    path: ../me_packages/packages/me_l10n
#  me_misc:
#    path: ../me_packages/packages/me_misc
#  me_ui:
#    path: ../me_packages/packages/me_ui
#  me_utils:
#    path: ../me_packages/packages/me_utils

flutter:
  uses-material-design: true
  generate: true
  assets:
    - 'assets/'
#    - 'assets/fonts/'
    - 'assets/icons/'
    - 'assets/icons/images/'
    - 'assets/icons/scan/'
    - 'assets/icons/social/'
    - 'assets/icons/social/demo/'
    - 'assets/icons/setting/'
    - 'assets/lottie/'
    - 'assets/media/'
  fonts:
    - family: "MMM Mono"
      fonts:
        - asset: assets/fonts/MMMMono.ttf
    - family: "HarmonyOS Sans"
      fonts:
        - asset: assets/fonts/HarmonyOS_Sans_Thin.ttf
          weight: 100
        - asset: assets/fonts/HarmonyOS_Sans_Light.ttf
          weight: 300
        - asset: assets/fonts/HarmonyOS_Sans_Regular.ttf
          weight: 400
        - asset: assets/fonts/HarmonyOS_Sans_Medium.ttf
          weight: 500
        - asset: assets/fonts/HarmonyOS_Sans_Bold.ttf
          weight: 700
        - asset: assets/fonts/HarmonyOS_Sans_Black.ttf
          weight: 900

flutter_gen:
  output: lib/res/
  line_length: 120
  integrations:
    flutter_svg: true
    lottie: true
  colors:
    inputs:
      - assets/color/theme.xml
