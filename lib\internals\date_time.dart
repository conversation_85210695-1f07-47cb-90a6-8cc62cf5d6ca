import 'package:intl/intl.dart';

// 格式化日期为显示格式（例如：Mar 11, 2025）
String formatDate(String dateString) {
  final DateTime date = DateTime.parse(dateString);
  if (date.isUtc) {
    return DateFormat('MMM dd, yyyy').format(date.toLocal());
  }
  return DateFormat('MMM dd, yyyy').format(date);
}

// 格式化时间为显示格式（例如：21:52）
String formatTime(String dateString) {
  final DateTime date = DateTime.parse(dateString);
  if (date.isUtc) {
    return DateFormat('HH:mm').format(date.toLocal());
  }
  return DateFormat('HH:mm').format(date);
}

// 格式化日期时间为显示格式（例如：Mar 11, 2025 21:52）
String formatDateTime(String dateString) {
  final DateTime date = DateTime.parse(dateString);
  if (date.isUtc) {
    return DateFormat('yyyy-MM-dd HH:mm').format(date.toLocal());
  }
  return DateFormat('yyyy-MM-dd HH:mm').format(date);
}
