import 'package:flutter/material.dart';
import 'package:me_extensions/me_extensions.dart' show MEColorExtension;

import '/res/assets.gen.dart';

class AppLogo extends StatelessWidget {
  const AppLogo({
    super.key,
    this.brightness,
    this.width,
    this.height,
    this.color,
  });

  final Brightness? brightness;
  final double? width;
  final double? height;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final isDark = (brightness ?? Theme.of(context).brightness) == Brightness.dark;
    final gen = isDark ? Assets.icons.logoTextDark : Assets.icons.logoTextLight;
    return SizedBox(
      width: width,
      height: height,
      child: AspectRatio(
        aspectRatio: 963 / 200,
        child: gen.svg(fit: BoxFit.cover, colorFilter: color?.filter),
      ),
    );
  }
}
