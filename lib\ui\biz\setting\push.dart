import 'dart:io';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '/feat/push/provider.dart';
import '/models/user.dart' show UserSettings;

@FFRoute(name: '/setting/push')
class PushNotificationSettingPage extends ConsumerStatefulWidget {
  const PushNotificationSettingPage({super.key});

  @override
  ConsumerState<PushNotificationSettingPage> createState() => _PushNotificationSettingPageState();
}

class _PushNotificationSettingPageState extends ConsumerState<PushNotificationSettingPage> {
  @override
  void initState() {
    super.initState();
    // 检查权限状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(pushNotificationProvider.notifier).checkPermission();
    });
  }

  @override
  Widget build(BuildContext context) {
    final pushState = ref.watch(pushNotificationProvider);
    final fcmTokenAsync = ref.watch(fcmTokenProvider);
    final fcmTokenByServerAsync = ref.watch(fcmTokenByServerProvider);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text('Push Notification Settings (${PushHelper.getCurrentPlatformName()})'),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: const AppBackButton(),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 权限状态卡片
            _buildPermissionCard(pushState),
            const SizedBox(height: 24),

            // FCM Token信息
            _buildTokenCard(fcmTokenAsync, fcmTokenByServerAsync),
            const SizedBox(height: 24),

            // 测试推送
            _buildTestSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionCard(PushNotificationState state) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  state.hasPermission ? Icons.notifications_active : Icons.notifications_off,
                  color: state.hasPermission ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                const Text(
                  '通知权限',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (state.isLoading)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              state.hasPermission ? '✅ 已授权' : '❌ 未授权',
              style: TextStyle(
                color: state.hasPermission ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (state.error != null) ...[
              const SizedBox(height: 8),
              Text(
                '错误: ${state.error}',
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
            if (!state.hasPermission) ...[
              const SizedBox(height: 8),
              const Text(
                '需要授权通知权限才能接收推送消息',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: state.isLoading
                    ? null
                    : () async {
                        await ref.read(pushNotificationProvider.notifier).openAppSettings();
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorName.themeColorDark,
                ),
                child: const Text(
                  '去设置',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTokenCard(AsyncValue<String?> fcmTokenAsync, AsyncValue<UserSettings> fcmTokenByServerAsync) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.key, color: ColorName.themeColorDark),
                const SizedBox(width: 8),
                Text(
                  'FCM Token (${PushHelper.getFCMFieldName()})',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                // 刷新按钮
                IconButton(
                  onPressed: () {
                    ref.invalidate(fcmTokenByServerProvider);
                    ref.read(pushNotificationProvider.notifier).refreshToken();
                  },
                  icon: const Icon(Icons.refresh),
                  tooltip: '刷新Token',
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 本地Token
            _buildTokenSection(
              title: '本地Token',
              tokenAsync: fcmTokenAsync,
              showCopyButton: true,
            ),

            const SizedBox(height: 16),

            // 服务器Token
            _buildServerTokenSection(fcmTokenByServerAsync),
          ],
        ),
      ),
    );
  }

  Widget _buildTokenSection({
    required String title,
    required AsyncValue<String?> tokenAsync,
    bool showCopyButton = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        tokenAsync.when(
          data: (token) => PushHelper.isValidToken(token)
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            PushHelper.formatTokenForDisplay(token),
                            style: const TextStyle(
                              fontSize: 12,
                              fontFamily: 'Courier',
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '长度: ${token?.length ?? 0} 字符',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (showCopyButton) ...[
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: token!));
                          Card3ToastUtil.showToast(message: ToastMessages.tokenCopied);
                        },
                        icon: const Icon(Icons.copy, size: 16),
                        label: const Text('复制完整Token'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorName.themeColorDark,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ],
                )
              : Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '❌ Token无效或未获取',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
          loading: () => const CircularProgressIndicator(),
          error: (error, stack) => Text('获取Token失败: $error'),
        ),
      ],
    );
  }

  Widget _buildServerTokenSection(AsyncValue<UserSettings> fcmTokenByServerAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '服务器Token',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        fcmTokenByServerAsync.when(
          data: (data) {
            final currentPlatformField = PushHelper.getFCMFieldName();
            final currentPlatformToken = data.fcmAndroid;

            return Column(
              children: [
                // 当前平台Token
                if (PushHelper.isValidToken(currentPlatformToken))
                  _buildPlatformToken(
                    '${PushHelper.getCurrentPlatformName()} (当前平台)',
                    currentPlatformToken!,
                    isCurrentPlatform: true,
                  ),

                // 其他平台Token
                if (data.fcmAndroid?.isNotEmpty == true && currentPlatformField != 'fcmAndroid')
                  _buildPlatformToken('Android', data.fcmAndroid!),
                if (data.fcmIos?.isNotEmpty == true && currentPlatformField != 'fcmIos')
                  _buildPlatformToken('iOS', data.fcmIos!),
                if (data.fcmWeb?.isNotEmpty == true && currentPlatformField != 'fcmWeb')
                  _buildPlatformToken('Web', data.fcmWeb!),

                if (data.fcmAndroid == null && data.fcmIos == null && data.fcmWeb == null)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange[50],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Text(
                      '⚠️ 服务器端暂无有效Token记录',
                      style: TextStyle(color: Colors.orange),
                    ),
                  ),
              ],
            );
          },
          loading: () => const CircularProgressIndicator(),
          error: (error, stack) => Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '❌ 获取服务器Token失败: $error',
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPlatformToken(String platform, String token, {bool isCurrentPlatform = false}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isCurrentPlatform ? Colors.green[50] : Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCurrentPlatform ? Colors.green[200]! : Colors.blue[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                platform,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isCurrentPlatform ? Colors.green[700] : ColorName.themeColorDark,
                ),
              ),
              if (isCurrentPlatform) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.smartphone,
                  size: 16,
                  color: Colors.green[700],
                ),
              ],
            ],
          ),
          const SizedBox(height: 4),
          Text(
            PushHelper.formatTokenForDisplay(token),
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'Courier',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.bug_report, color: ColorName.themeColorDark),
                SizedBox(width: 8),
                Text(
                  '测试推送',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              '测试本地通知和远程推送功能',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),

            // 本地通知示例
            _buildLocalNotificationTests(),

            const SizedBox(height: 16),

            // 远程推送测试
            ElevatedButton(
              onPressed: () {
                _showTestPushDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorName.themeColorDark,
              ),
              child: const Text(
                '远程推送测试',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalNotificationTests() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '本地通知示例',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 12),

        // 基础通知
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _sendLocalNotification(
                  title: '基础通知',
                  body: '这是一条基础的本地通知消息',
                  payload: '{"type": "basic", "action": "test"}',
                ),
                icon: const Icon(Icons.notifications, size: 16),
                label: const Text('基础通知'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _sendLocalNotification(
                  title: '💰 钱包通知',
                  body: '您收到了一笔新的转账: 100 USDC',
                  payload: '{"type": "wallet", "action": "transfer", "amount": "100"}',
                ),
                icon: const Icon(Icons.account_balance_wallet, size: 16),
                label: const Text('钱包通知'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _sendLocalNotification(
                  title: '👥 社交通知',
                  body: '有人向您发送了名片交换请求',
                  payload: '{"type": "social", "action": "card_exchange"}',
                ),
                icon: const Icon(Icons.people, size: 16),
                label: const Text('社交通知'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _sendLocalNotification(
                  title: '⚙️ 系统通知',
                  body: '应用已更新到最新版本',
                  payload: '{"type": "system", "action": "update"}',
                ),
                icon: const Icon(Icons.system_update, size: 16),
                label: const Text('系统通知'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // 延迟通知
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _sendScheduledNotification,
            icon: const Icon(Icons.schedule, size: 16),
            label: const Text('5秒后延迟通知'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        const SizedBox(height: 8),

        // 权限调试按钮
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _debugNotificationPermissions,
            icon: const Icon(Icons.bug_report, size: 16),
            label: const Text('调试通知权限'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        const SizedBox(height: 8),

        // Token同步调试按钮
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _debugTokenSync,
            icon: const Icon(Icons.sync_problem, size: 16),
            label: const Text('Token同步调试'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.deepPurple,
              foregroundColor: Colors.white,
            ),
          ),
        ),

        // iOS专门调试按钮
        if (Platform.isIOS) ...[
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _debugIOSNotifications,
              icon: const Icon(Icons.phone_iphone, size: 16),
              label: const Text('iOS通知深度调试'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ],
    );
  }

  void _showTestPushDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('测试推送'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('这个功能需要在服务端实现'),
            SizedBox(height: 8),
            Text(
              '可以使用FCM控制台或服务端API发送测试消息',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 发送本地通知
  Future<void> _sendLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      LogUtil.d('🔔 准备发送本地通知: $title');

      final service = ref.read(pushNotificationServiceProvider);

      // 使用增强的权限检查
      final hasPermission = await service.checkAndRequestPermission();

      if (!hasPermission) {
        Card3ToastUtil.showToast(message: ToastMessages.notificationPermissionDenied);
        LogUtil.w('❌ 通知权限检查失败');
        return;
      }

      LogUtil.d('✅ 通知权限检查通过，发送通知...');

      await service.showLocalNotification(
        title: title,
        body: body,
        payload: payload,
      );

      Card3ToastUtil.showToast(message: ToastMessages.localNotificationSent);
      LogUtil.d('✅ 本地通知发送完成');
    } catch (e) {
      Card3ToastUtil.showToast(message: '发送失败: $e');
      rethrow;
    }
  }

  /// 发送延迟通知
  Future<void> _sendScheduledNotification() async {
    try {
      LogUtil.d('⏰ 准备设置延迟通知...');

      final service = ref.read(pushNotificationServiceProvider);

      // 检查权限
      final hasPermission = await service.checkAndRequestPermission();

      if (!hasPermission) {
        Card3ToastUtil.showToast(message: ToastMessages.notificationPermissionDenied);
        return;
      }

      Card3ToastUtil.showToast(message: ToastMessages.delayedNotificationSet);
      LogUtil.d('⏰ 延迟通知已设置');

      // 延迟5秒后发送通知
      await Future.delayed(const Duration(seconds: 5), () async {
        await service.showLocalNotification(
          title: '⏰ 延迟通知',
          body: '这是一条5秒后发送的延迟通知',
          payload: '{"type": "scheduled", "action": "delayed"}',
        );
        LogUtil.d('✅ 延迟通知发送完成');
      });
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.setDelayedNotificationFailed(e.toString()));
      rethrow;
    }
  }

  /// 调试通知权限
  Future<void> _debugNotificationPermissions() async {
    try {
      LogUtil.d('⚙️ 开始调试通知权限...');

      final service = ref.read(pushNotificationServiceProvider);

      // 检查权限
      final hasPermission = await service.isNotificationEnabled();
      LogUtil.d('📱 当前权限状态: $hasPermission');

      if (!hasPermission) {
        LogUtil.d('⚠️ 权限未授权，尝试请求权限...');
        final granted = await service.checkAndRequestPermission();
        LogUtil.d('📱 权限请求结果: $granted');

        if (!granted) {
          Card3ToastUtil.showToast(message: ToastMessages.notificationPermissionDeniedManual);
          return;
        }
      }

      Card3ToastUtil.showToast(message: ToastMessages.notificationPermissionGranted);
      LogUtil.d('✅ 通知权限调试完成');
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.debugFailed(e.toString()));
      rethrow;
    }
  }

  /// 调试iOS通知
  Future<void> _debugIOSNotifications() async {
    try {
      LogUtil.d('🍎 开始iOS通知深度调试...');

      final service = ref.read(pushNotificationServiceProvider);

      // 1. 详细检查权限状态
      LogUtil.d('🍎 步骤1: 检查权限状态');
      final hasPermission = await service.isNotificationEnabled();
      LogUtil.d('🍎 当前权限状态: $hasPermission');

      if (!hasPermission) {
        LogUtil.d('🍎 步骤2: 请求权限');
        final granted = await service.checkAndRequestPermission();
        LogUtil.d('🍎 权限请求结果: $granted');

        if (!granted) {
          Card3ToastUtil.showToast(message: ToastMessages.iosNotificationPermissionDenied);
          return;
        }
      }

      // 2. 测试发送本地通知
      LogUtil.d('🍎 步骤3: 发送测试通知');
      await service.showLocalNotification(
        title: '🍎 iOS测试通知',
        body: '如果你看到这条通知，说明iOS本地通知工作正常！',
        payload: '{"type": "ios_test", "timestamp": "${DateTime.now().millisecondsSinceEpoch}"}',
      );

      Card3ToastUtil.showToast(message: ToastMessages.iosDebugCompleted);
      LogUtil.d('🍎 ✅ iOS通知调试完成');
    } catch (e) {
      Card3ToastUtil.showToast(message: 'iOS调试失败: $e');
      rethrow;
    }
  }

  /// 全面的Token同步调试
  Future<void> _debugTokenSync() async {
    try {
      LogUtil.d('🔄 开始Token同步调试...');

      final service = ref.read(pushNotificationServiceProvider);

      // 获取本地token
      final localToken = service.fcmToken;

      // 获取服务器token
      final serverTokens = await ref.read(fcmTokenByServerProvider.future);
      final serverToken = serverTokens.fcmAndroid;

      // 生成调试信息
      final debugInfo = PushHelper.generateDebugInfo(
        currentToken: localToken,
        lastSentToken: serverToken,
        hasPermission: await service.isNotificationEnabled(),
        isInitialized: true,
      );

      LogUtil.d('🔄 调试信息: $debugInfo');

      // 显示调试对话框
      if (mounted) {
        _showTokenDebugDialog(debugInfo, localToken, serverToken);
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.tokenDebugFailed(e.toString()));
      rethrow;
    }
  }

  /// 显示Token调试对话框
  void _showTokenDebugDialog(Map<String, dynamic> debugInfo, String? localToken, String? serverToken) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('🔍 Token同步调试 (${debugInfo['platform']})'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDebugInfoRow('平台', debugInfo['platform']),
              _buildDebugInfoRow('FCM字段', debugInfo['fcmField']),
              _buildDebugInfoRow('权限状态', debugInfo['hasPermission'] ? '✅ 已授权' : '❌ 未授权'),
              _buildDebugInfoRow('初始化状态', debugInfo['isInitialized'] ? '✅ 已初始化' : '❌ 未初始化'),
              const Divider(),
              _buildDebugInfoRow('本地Token', debugInfo['currentToken']),
              _buildDebugInfoRow('Token有效', debugInfo['tokenValid'] ? '✅ 有效' : '❌ 无效'),
              const Divider(),
              _buildDebugInfoRow('服务器Token', debugInfo['lastSentToken']),
              _buildDebugInfoRow('需要更新', debugInfo['needsUpdate'] ? '🔄 是' : '✅ 否'),
              const Divider(),
              _buildDebugInfoRow('检查时间', debugInfo['timestamp']),

              if (debugInfo['needsUpdate'] == true) ...[
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      Navigator.pop(context);
                      if (localToken != null) {
                        try {
                          Card3ToastUtil.showToast(message: ToastMessages.syncingTokenToServer);
                          await ref.read(sendTokenToServerProvider(localToken).future);
                          Card3ToastUtil.showToast(message: '✅ Token同步成功');
                        } catch (e) {
                          Card3ToastUtil.showToast(message: '❌ Token同步失败: $e');
                        }
                      }
                    },
                    icon: const Icon(Icons.sync),
                    label: const Text('立即同步到服务器'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              final fullInfo =
                  '''
调试信息详情:
- 平台: ${debugInfo['platform']}
- FCM字段: ${debugInfo['fcmField']}
- 权限状态: ${debugInfo['hasPermission']}
- 本地Token: ${localToken ?? '无'}
- 服务器Token: ${serverToken ?? '无'}
- 需要更新: ${debugInfo['needsUpdate']}
- 检查时间: ${debugInfo['timestamp']}
              ''';
              Clipboard.setData(ClipboardData(text: fullInfo));
              Card3ToastUtil.showToast(message: '调试信息已复制');
            },
            child: const Text('复制详情'),
          ),
        ],
      ),
    );
  }

  Widget _buildDebugInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontFamily: 'Courier',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
