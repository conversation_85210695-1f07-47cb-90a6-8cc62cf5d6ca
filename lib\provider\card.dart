import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/front_end.dart';
import '/models/card.dart';
import '/models/user.dart' show ProfileMode, UserInfo;
import '/provider/api.dart';
import 'business.dart' show configProvider;
import 'user.dart' show fetchUserInfoProvider;

part 'card.g.dart';

@riverpod
Future<List<CardInfo>> fetchMyCards(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getMyCards();
}

@riverpod
Future<CardInfo> fetchPublicCard(
  Ref ref, {
  required String code,
}) {
  return ref.read(apiServiceProvider).getCard(cardCode: code);
}

@riverpod
Future<UserInfo> fetchPublicProfile(
  Ref ref, {
  required String code,
}) {
  return ref.read(apiServiceProvider).getPublicProfile(code: code);
}

@riverpod
Future<List<Social>> fetchSocials(
  Ref ref, {
  String? code,
}) {
  if (code == null) {
    return ref.read(apiServiceProvider).socialQuery();
  } else {
    return ref.read(apiServiceProvider).getPublicSocials(code: code);
  }
}

@riverpod
Future<List<String>> fetchEthccTopics(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getEthccTopics();
}

@riverpod
Future<List<String>> fetchEthccRoles(
  Ref ref,
) {
  return ref.read(apiServiceProvider).getEthccRoles();
}

@riverpod
Future<EthccProfile?> fetchEthccProfile(
  Ref ref, {
  String? code,
}) {
  if (code != null) {
    return ref.read(apiServiceProvider).getEthccPublicProfile(code: code);
  } else {
    return ref.read(apiServiceProvider).getEthccProfile();
  }
}

@riverpod
Future<FrontEndGitHubContributionCollection?> fetchGitHubContributions(
  Ref ref, {
  required String handle,
}) {
  return ref.read(apiFrontEndProvider).getGitHubContributions(handle: handle);
}

@riverpod
bool? validateETHCCProfile(
  Ref ref, {
  required bool validateProfile,
}) {
  // 1. 统一获取所有依赖的数据
  final cardsResult = ref.watch(fetchMyCardsProvider);
  final userInfoResult = ref.watch(fetchUserInfoProvider());

  // 2. 统一处理加载状态
  if (cardsResult.isLoading || userInfoResult.isLoading) {
    return null;
  }

  // 3. 统一处理错误/空值状态
  final cards = cardsResult.valueOrNull;
  final userInfo = userInfoResult.valueOrNull;
  if (cards == null || userInfo == null) {
    // 如果任何一个关键数据加载失败或为空，则认为验证不通过
    return false; // 返回 false 比 null 更明确地表示“未满足条件”
  }

  return ref.watch(
    _validateETHCCProfileInnerProvider(
      cards: cards,
      userInfo: userInfo,
      validateProfile: validateProfile,
    ),
  );
}

@riverpod
Future<bool> validateETHCCProfileByCode(
  Ref ref, {
  required String code,
}) async {
  late final CardInfo card;
  late final UserInfo user;
  await Future.wait([
    ref.read(fetchPublicCardProvider(code: code).future).then((value) => card = value),
    ref.read(fetchPublicProfileProvider(code: code).future).then((value) => user = value),
  ]);
  final result = ref.read(
    _validateETHCCProfileInnerProvider(
      // ignore: provider_parameters
      cards: [card],
      userInfo: user,
      validateProfile: true,
    ),
  );
  return result ?? false;
}

@riverpod
bool? _validateETHCCProfileInner(
  Ref ref, {
  required List<CardInfo> cards,
  required UserInfo userInfo,
  required bool validateProfile,
}) {
  final config = ref.watch(configProvider);
  final configEvents = Set<int>.from(config.ethccEventIds);
  if (configEvents.isEmpty) {
    return false;
  }

  final cardEvents = cards.map((e) => e.card3EventId);
  final hasEventMatch = configEvents.contains(0) || cardEvents.any(configEvents.contains);

  // 如果不需要验证 profile，直接返回事件匹配结果
  if (!validateProfile) {
    return hasEventMatch;
  }

  // **核心逻辑2: 确定要验证的 Profile**
  final ProfileMode profileToCheck;
  if (userInfo.profileMode == ProfileMode.EMPTY) {
    profileToCheck = config.defaultMode;
  } else {
    profileToCheck = userInfo.profileMode;
  }

  return hasEventMatch && (profileToCheck == ProfileMode.ETHCC);
}
