import 'dart:io';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:ndef_record/ndef_record.dart';
import 'package:nfc_manager/nfc_manager.dart';
import 'package:nfc_manager/nfc_manager_ios.dart';
import 'package:nfc_manager_ndef/nfc_manager_ndef.dart';

/// NFC扫描结果回调
typedef NfcResultCallback = void Function(String tagData, bool success);

/// NFC标签类型
enum NfcTagType {
  ndef, // NFC215类型
  isoDep, // NFC424类型
  unknown, // 未知类型
}

/// NFC扫描状态
enum NfcScanState {
  idle, // 空闲状态
  scanning, // 扫描中
  processing, // 处理中
  success, // 扫描成功
  failed, // 扫描失败
}

/// NFC处理器组件
class NfcHandler extends StatefulWidget {
  const NfcHandler({
    super.key,
    required this.child,
    required this.onResult,
    this.onStateChanged,
    this.autoStart = false,
  });

  /// 子Widget，点击此Widget将触发NFC扫描
  final Widget child;

  /// NFC扫描结果回调
  final NfcResultCallback onResult;

  /// NFC扫描状态变化回调（可选）
  final Function(NfcScanState state)? onStateChanged;

  /// 是否自动开始扫描
  final bool autoStart;

  @override
  State<NfcHandler> createState() => _NfcHandlerState();
}

class _NfcHandlerState extends State<NfcHandler> with WidgetsBindingObserver, TickerProviderStateMixin {
  NfcScanState _state = NfcScanState.idle;
  bool _isNfcAvailable = false;
  bool _isSessionRunning = false;
  bool _isProcessing = false;

  // 用于Android的NFC UI显示
  OverlayEntry? _androidNfcOverlay;
  final nfc = NfcManager.instance;

  late AnimationController _pulseController;

  // late Animation<double> _pulseAnimation;
  late AnimationController _waveController;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 初始化脉冲动画控制器
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    // _pulseAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
    //   CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    // );

    // 初始化波纹动画控制器
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();

    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.linear),
    );

    _checkNfcAvailability().then((_) {
      if (widget.autoStart && _isNfcAvailable) {
        _startNfcScan();
      }
    });
  }

  // @override
  // void didChangeAppLifecycleState(AppLifecycleState state) {
  //   super.didChangeAppLifecycleState(state);

  //   switch (state) {
  //     case AppLifecycleState.resumed:
  //       // 应用恢复时，如果之前在扫描状态，重新启动扫描
  //       Future.delayed(const Duration(milliseconds: 500), () {
  //         if (mounted && _state == NfcScanState.scanning && !_isSessionRunning) {
  //           _restartNfcSession();
  //         }
  //       });
  //       break;
  //     case AppLifecycleState.paused:
  //     case AppLifecycleState.inactive:
  //     case AppLifecycleState.detached:
  //       // 应用暂停、非活跃或分离时，立即停止NFC会话
  //       _forceStopNfcSession();
  //       break;
  //     case AppLifecycleState.hidden:
  //       // 应用隐藏时也停止NFC会话
  //       _forceStopNfcSession();
  //       break;
  //   }
  // }

  @override
  void dispose() {
    _removeAndroidNfcOverlay();
    _forceStopNfcSession();
    WidgetsBinding.instance.removeObserver(this);
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  /// 检查设备NFC可用性
  Future<bool> _checkNfcAvailability() async {
    try {
      final isAvailable = await NfcManager.instance.isAvailable();
      setState(() {
        _isNfcAvailable = isAvailable;
      });
      return isAvailable;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return false;
    }
  }

  /// 开始NFC扫描
  Future<void> _startNfcScan() async {
    if (_isSessionRunning || _isProcessing) {
      return;
    }

    if (!_isNfcAvailable) {
      final available = await _checkNfcAvailability();
      if (!available) {
        _updateState(NfcScanState.failed);
        widget.onResult(
          'Device does not support NFC or NFC is not enabled',
          false,
        );
        return;
      }
    }

    _updateState(NfcScanState.scanning);

    // 在Android上显示NFC扫描UI
    if (Platform.isAndroid) {
      _showAndroidNfcUI();
    }

    try {
      // 确保之前的会话已经停止
      await nfc.stopSession().catchError((_) {});

      // 短暂延迟确保会话完全关闭
      await Future.delayed(const Duration(milliseconds: 300));

      await nfc.startSession(
        onDiscovered: _processNfcTag,
        pollingOptions: NfcPollingOption.values.toSet(),
        onSessionErrorIos: _handleNfcError,
      );

      setState(() {
        _isSessionRunning = true;
      });
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s);

      _updateState(NfcScanState.failed);
      widget.onResult('Failed to start NFC scan: ${e.toString()}', false);

      // 确保会话被正确关闭
      await nfc.stopSession().catchError((_) {});

      // 移除Android NFC UI
      _removeAndroidNfcOverlay();
    }
  }

  /// 停止NFC会话
  Future<void> _stopNfcSession() async {
    if (!_isSessionRunning) {
      return;
    }

    try {
      await nfc.stopSession();
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s);
    } finally {
      setState(() {
        _isSessionRunning = false;
      });

      // 移除Android NFC UI
      _removeAndroidNfcOverlay();
    }
  }

  /// 重启NFC会话
  Future<void> _restartNfcSession() async {
    await _stopNfcSession();
    await Future.delayed(const Duration(milliseconds: 500));
    await _startNfcScan();
  }

  /// 处理NFC错误
  Future<void> _handleNfcError(NfcReaderSessionErrorIos error) async {
    final canceled = error.code == NfcReaderErrorCodeIos.readerSessionInvalidationErrorUserCanceled;
    LogUtil.e(
      '${error.code} ${error.message}',
      stackTrace: StackTrace.current,
      enabled: !canceled,
    );

    setState(() {
      _isSessionRunning = false;
    });

    // 尝试停止会话
    await nfc.stopSession().catchError((_) {});

    // 移除Android NFC UI
    _removeAndroidNfcOverlay();

    // 用户取消不视为错误
    if (canceled) {
      _updateState(NfcScanState.idle);
      return;
    }

    // 处理意外中断
    if (error.message.contains('Session invalidated unexpectedly')) {
      await Future.delayed(const Duration(seconds: 1));
      _restartNfcSession();
      return;
    }

    _updateState(NfcScanState.failed);
    widget.onResult('NFC scan error: ${error.message}', false);
  }

  /// 处理扫描到的NFC标签
  Future<void> _processNfcTag(NfcTag tag) async {
    // ignore: invalid_use_of_protected_member
    LogUtil.d('Detected NFC tag: ${tag.data}');

    if (_isProcessing) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    _updateState(NfcScanState.processing);

    // 更新Android UI为处理中状态
    if (Platform.isAndroid) {
      _updateAndroidNfcUI(NfcScanState.processing);
    }

    try {
      // 尝试解析为NDEF格式（NFC215）
      final ndef = Ndef.from(tag);

      if (ndef != null) {
        await _processNdefTag(ndef);
        // ignore: invalid_use_of_protected_member
      } else if (tag.data case final Map data when data.containsKey('isoDep')) {
        // 尝试解析为ISO-DEP格式（NFC424）
        // 注意：这需要特殊的iOS权限
        _updateState(NfcScanState.failed);
        widget.onResult(
          'Detected NFC424 card, current version does not support this type of card',
          false,
        );
      } else {
        _updateState(NfcScanState.failed);
        widget.onResult('Unsupported NFC tag format', false);
      }
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s);
      _updateState(NfcScanState.failed);
      widget.onResult('Read NFC tag error: ${e.toString()}', false);
    } finally {
      setState(() {
        _isProcessing = false;
        _isSessionRunning = false;
      });

      // 关闭会话 - 修复：Android平台也需要停止会话
      try {
        await nfc.stopSession();
      } catch (e, s) {
        LogUtil.e(e, stackTrace: s);
      }

      // 延迟移除Android UI，让用户看到结果
      if (Platform.isAndroid) {
        if (_state == NfcScanState.success) {
          _updateAndroidNfcUI(NfcScanState.success);
          await Future.delayed(const Duration(seconds: 2));
        }
        _removeAndroidNfcOverlay();
      }
    }
  }

  /// 处理NDEF格式标签（NFC215）
  Future<void> _processNdefTag(Ndef ndef) async {
    final records = ndef.cachedMessage?.records;

    if (records == null || records.isEmpty) {
      _updateState(NfcScanState.failed);
      widget.onResult('No NFC records found', false);
      return;
    }

    // 处理读取的数据
    String tagContent = '';

    for (final record in records) {
      if (record.typeNameFormat == TypeNameFormat.wellKnown && String.fromCharCodes(record.type) == 'T') {
        // 文本记录
        final payload = record.payload;
        final languageCodeLength = payload[0] & 0x3f;
        final isUTF16 = (payload[0] & 0x80) != 0;

        final languageCode = String.fromCharCodes(
          payload.sublist(1, 1 + languageCodeLength),
        );

        final textBytes = payload.sublist(1 + languageCodeLength);
        final text = isUTF16 ? String.fromCharCodes(textBytes) : utf8.decode(textBytes);

        tagContent += 'Text: $text (Language: $languageCode)\n';
      } else if (record.typeNameFormat == TypeNameFormat.wellKnown && String.fromCharCodes(record.type) == 'U') {
        // URI记录
        final payload = record.payload;
        final uri = String.fromCharCodes(payload.sublist(1));

        tagContent += 'URI: $uri\n';
      } else {
        // 其他类型记录
        tagContent += 'Other data: ${record.payload}\n';
      }
    }

    if (tagContent.isNotEmpty) {
      _updateState(NfcScanState.success);
      widget.onResult(tagContent, true);
    } else {
      _updateState(NfcScanState.failed);
      widget.onResult('Failed to parse NFC data', false);
    }
  }

  /// 更新NFC扫描状态并触发回调
  void _updateState(NfcScanState state) {
    setState(() {
      _state = state;
    });

    if (Platform.isAndroid) {
      _updateAndroidNfcUI(state);
    }

    if (widget.onStateChanged != null) {
      widget.onStateChanged!(state);
    }
  }

  // Android特有的NFC UI显示方法
  void _showAndroidNfcUI() {
    _removeAndroidNfcOverlay(); // 确保先移除已存在的overlay

    final overlay = OverlayEntry(
      builder: (context) => _buildAndroidNfcOverlay(NfcScanState.scanning),
    );

    Overlay.of(context).insert(overlay);
    _androidNfcOverlay = overlay;
  }

  // 更新Android NFC UI状态
  void _updateAndroidNfcUI(NfcScanState state) {
    if (_androidNfcOverlay == null) {
      return;
    }

    _androidNfcOverlay!.remove();
    final overlay = OverlayEntry(
      builder: (context) => _buildAndroidNfcOverlay(state),
    );

    Overlay.of(context).insert(overlay);
    _androidNfcOverlay = overlay;
  }

  // 移除Android NFC UI
  void _removeAndroidNfcOverlay() {
    _androidNfcOverlay?.remove();
    _androidNfcOverlay = null;
  }

  Widget _buildAndroidNfcOverlay(NfcScanState state) {
    return Material(
      color: Colors.transparent,
      child: Container(
        color: Colors.black45,
        alignment: Alignment.bottomCenter,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 顶部带关闭按钮的标题
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 10, 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _getNfcStatusText(state),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: _stopNfcSession,
                    ),
                  ],
                ),
              ),

              // 中间的动画NFC图标
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 30),
                child: state == NfcScanState.success ? _getNfcStatusIcon(state) : _buildScanningAnimation(),
              ),

              // 底部取消按钮
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                margin: const EdgeInsets.all(16),
                child: TextButton(
                  onPressed: _stopNfcSession,
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.grey.shade200,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 新方法：构建扫描动画
  Widget _buildScanningAnimation() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 外部波纹动画
        ...List.generate(3, (index) {
          return AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              final delay = index / 3;
              final animationValue = (_waveAnimation.value + delay) % 1.0;

              return Opacity(
                opacity: 1.0 - animationValue,
                child: Transform.scale(
                  scale: 0.8 + (animationValue * 0.5),
                  child: Container(
                    width: 170,
                    height: 170,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.blue.withValues(alpha: 0.5),
                        width: 2,
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        }),

        // 中心手机图标脉冲动画
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.blue.shade50,
            border: Border.all(color: Colors.blue, width: 2),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.smartphone,
                size: 80,
                color: Colors.blue.shade700,
              ),
              const SizedBox(height: 4),
            ],
          ),
        ),
      ],
    );
  }

  String _getNfcStatusText(NfcScanState state) {
    switch (state) {
      case NfcScanState.scanning:
        return 'NFC scanning ready';
      case NfcScanState.processing:
        return 'Reading NFC data';
      case NfcScanState.success:
        return 'NFC scan success';
      case NfcScanState.failed:
        return 'NFC scan failed';
      default:
        return 'Ready to scan NFC';
    }
  }

  Widget _getNfcStatusIcon(NfcScanState state) {
    switch (state) {
      case NfcScanState.scanning:
        return const Icon(
          Icons.nfc,
          size: 120,
          color: Colors.blue,
        );
      case NfcScanState.processing:
        return const SizedBox(
          width: 120,
          height: 120,
          child: CircularProgressIndicator(),
        );
      case NfcScanState.success:
        return const Icon(
          Icons.check_circle,
          size: 120,
          color: Colors.green,
        );
      case NfcScanState.failed:
        return const Icon(
          Icons.error,
          size: 120,
          color: Colors.red,
        );
      default:
        return const Icon(
          Icons.nfc,
          size: 120,
          color: Colors.grey,
        );
    }
  }

  /// 强制停止NFC会话（用于应用生命周期变化和组件销毁）
  void _forceStopNfcSession() {
    if (_isSessionRunning || _isProcessing) {
      // 立即更新状态
      setState(() {
        _isSessionRunning = false;
        _isProcessing = false;
      });

      // 异步停止会话，不等待结果
      nfc.stopSession().catchError((e, s) {
        LogUtil.e(e, stackTrace: s);
      });

      // 移除Android NFC UI
      _removeAndroidNfcOverlay();

      // 更新状态为空闲
      _updateState(NfcScanState.idle);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _startNfcScan,
      child: widget.child,
    );
  }
}
