import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:me_extensions/me_extensions.dart';

import '/models/card.dart' show SocialPlatform;

/// 自定义SVG图标组件
class SocialSvgIcon extends StatelessWidget {
  const SocialSvgIcon({
    super.key,
    required this.platform,
    this.size,
    this.color,
  });

  final SocialPlatform platform;
  final double? size;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      'assets/icons/social/${platform.iconImageKey}.svg',
      width: size ?? platform.iconSize,
      height: size ?? platform.iconSize,
      colorFilter: color?.filter ?? platform.iconColor?.filter ?? Colors.white.filter,
    );
  }
}

/// 自定义SVG图标组件
class SocialDemoImage extends StatelessWidget {
  const SocialDemoImage({
    super.key,
    required this.name,
  });

  final String name;

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'assets/icons/social/demo/$name.png',
    );
  }
}
