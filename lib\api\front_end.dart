import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_extensions/me_extensions.dart';

import '/constants/envs.dart' show envApiUrlFE;
import '/provider/api.dart';

part 'front_end.freezed.dart';

part 'front_end.g.dart';

final class FrontEndApi {
  FrontEndApi(this.ref);

  final Ref ref;

  late final http = ref
      .read(httpProvider)
      .clone(
        options: BaseOptions(
          baseUrl: '$envApiUrlFE/api',
        ),
      );

  Future<FrontEndGitHubContributionCollection?> getGitHubContributions({
    required String handle,
    CancelToken? cancelToken,
  }) async {
    final res = await http.get('/github/contributions/$handle');
    final rep = Rep.fromJson(
      res.data,
      (v) => switch (v.asJson()['data']?['user']?['contributionsCollection']) {
        final Map<String, dynamic> json => FrontEndGitHubContributionCollection.fromJson(json),
        _ => null,
      },
    );
    return rep.data;
  }
}

@freezed
sealed class FrontEndGitHubContributionCollection with _$FrontEndGitHubContributionCollection {
  const factory FrontEndGitHubContributionCollection({
    @JsonKey(name: 'contributionCalendar') required FrontEndGitHubContributionCalendar calendar,
  }) = _FrontEndGitHubContributionCollection;

  factory FrontEndGitHubContributionCollection.fromJson(Map<String, dynamic> json) =>
      _$FrontEndGitHubContributionCollectionFromJson(json);
}

@freezed
sealed class FrontEndGitHubContributionCalendar with _$FrontEndGitHubContributionCalendar {
  const factory FrontEndGitHubContributionCalendar({
    @JsonKey(name: 'totalContributions') required int total,
    @JsonKey(name: 'weeks') required List<FrontEndGitHubContributionDays> weeks,
  }) = _FrontEndGitHubContributionCalendar;

  factory FrontEndGitHubContributionCalendar.fromJson(Map<String, dynamic> json) =>
      _$FrontEndGitHubContributionCalendarFromJson(json);
}

@freezed
sealed class FrontEndGitHubContributionDays with _$FrontEndGitHubContributionDays {
  const factory FrontEndGitHubContributionDays({
    @JsonKey(name: 'contributionDays') required List<FrontEndGitHubContributionDay> days,
  }) = _FrontEndGitHubContributionDays;

  factory FrontEndGitHubContributionDays.fromJson(Map<String, dynamic> json) =>
      _$FrontEndGitHubContributionDaysFromJson(json);
}

@freezed
sealed class FrontEndGitHubContributionDay with _$FrontEndGitHubContributionDay {
  const factory FrontEndGitHubContributionDay({
    @JsonKey(name: 'contributionCount') required int contributionCount,
    @JsonKey(name: 'date') required String rawDate,
  }) = _FrontEndGitHubContributionDay;

  const FrontEndGitHubContributionDay._();

  factory FrontEndGitHubContributionDay.fromJson(Map<String, dynamic> json) =>
      _$FrontEndGitHubContributionDayFromJson(json);

  DateTime get date => DateTime.parse(rawDate);
}
