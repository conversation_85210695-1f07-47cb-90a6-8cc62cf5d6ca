import 'dart:async';
import 'dart:convert';

import 'package:cbor/simple.dart';
import 'package:convert/convert.dart' show hex;
import 'package:flutter/widgets.dart' show BuildContext;
import 'package:flutter_inappwebview/flutter_inappwebview.dart'
    show InApp<PERSON>eb<PERSON>iewController, JavaScriptHandlerFunctionData;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:me_misc/me_misc.dart' show MEError;
import 'package:me_utils/me_utils.dart' show LogUtil;

import '../../internals/methods.dart' show handleExceptions;

part 'rpc_call.freezed.dart';

part 'rpc_call.g.dart';

abstract interface class JsonRPC {
  const JsonRPC({
    required this.id,
    this.jsonrpc = JSON_RPC_VERSION,
  });

  static const String JSON_RPC_VERSION = '2.0';

  @Json<PERSON>ey(name: 'id')
  final String id;

  @JsonKey(name: 'jsonrpc')
  final String jsonrpc;
}

String parseJsonRpcId(dynamic id) {
  return id.toString();
}

@freezed
sealed class JsonRPCRequest extends JsonRPC with _$JsonRPCRequest {
  const factory JsonRPCRequest({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) required String id,
    @JsonKey(name: 'method') required String method,
    @JsonKey(name: 'params') required List params,
    @Default(JsonRPC.JSON_RPC_VERSION) String jsonrpc,
  }) = _JsonRPCRequest;

  const JsonRPCRequest._({required super.id, super.jsonrpc});

  factory JsonRPCRequest.fromJson(Map<String, Object?> json) => _$JsonRPCRequestFromJson(json);
}

@freezed
abstract class JsonRPCResponse extends JsonRPC with _$JsonRPCResponse {
  const factory JsonRPCResponse({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) required String id,
    @JsonKey(name: 'jsonrpc') @Default(JsonRPC.JSON_RPC_VERSION) String jsonrpc,
    @JsonKey(name: 'result') dynamic result,
    @JsonKey(name: 'error') JsonRPCError? error,
  }) = _JsonRPCResponse;

  const JsonRPCResponse._({required super.id, super.jsonrpc});

  factory JsonRPCResponse.ok(JsonRPCRequest request, dynamic result) =>
      _JsonRPCResponse(id: request.id, result: result);

  factory JsonRPCResponse.error(JsonRPCRequest request, JsonRPCError? error) =>
      _JsonRPCResponse(id: request.id, error: error);

  factory JsonRPCResponse.fromJson(Map<String, Object?> json) => _$JsonRPCResponseFromJson(json);
}

@freezed
sealed class JsonRPCError with _$JsonRPCError {
  const factory JsonRPCError({
    @JsonKey(name: 'code') required int code,
    @JsonKey(name: 'message') required String message,
  }) = _JsonRPCError;

  factory JsonRPCError.fromJson(Map<String, Object?> json) => _$JsonRPCErrorFromJson(json);

  static const int bridgeUnknownError = 50000;
  static const int bridgeUnknownMethod = 50001;
  static const int bridgeIllegalArguments = 50002;
  static const int bridgeIllegalState = 50003;
  static const int bridgeUnsupportedError = 50004;
  static const int bridgeIdentityNotFound = 50005;
  static const int bridgeIdentityExpired = 50006;
  static const int bridgeWalletNotFound = 50007;
  static const int bridgeOperationCancelled = 50008;
}

@freezed
sealed class JsonRPCResult with _$JsonRPCResult {
  const factory JsonRPCResult({
    @JsonKey(name: 'result') dynamic result,
    @JsonKey(name: 'error') JsonRPCError? error,
  }) = _JsonRPCResult;

  factory JsonRPCResult.fromJson(Map<String, Object?> json) => _$JsonRPCResultFromJson(json);
}

abstract interface class JsonRPCAdapter<Req extends JsonRPC, Rep extends JsonRPC> {
  const JsonRPCAdapter();

  FutureOr<Req> decodeRequest(String buf);

  FutureOr<String> encodeResponse(Rep response);
}

class DefaultJsonRPCAdapter implements JsonRPCAdapter<JsonRPCRequest, JsonRPCResponse> {
  const DefaultJsonRPCAdapter();

  @override
  FutureOr<JsonRPCRequest> decodeRequest(String buf) {
    final buffer = hex.decode(buf);
    final req = cbor.decode(buffer) as Map;
    return JsonRPCRequest.fromJson(req.cast<String, dynamic>());
  }

  @override
  FutureOr<String> encodeResponse(JsonRPCResponse response) {
    final json = response.toJson();
    final hex = cbor.encode(json).map((e) => e.toRadixString(16).padLeft(2, '0')).join();
    return hex;
  }
}

class RawJsonRPCAdapter implements JsonRPCAdapter<JsonRPCRequest, JsonRPCResponse> {
  const RawJsonRPCAdapter();

  @override
  FutureOr<JsonRPCRequest> decodeRequest(String buf) {
    final req = jsonDecode(buf) as Map<String, dynamic>;
    return JsonRPCRequest.fromJson(req);
  }

  @override
  FutureOr<String> encodeResponse(JsonRPCResponse response) {
    final json = response.toJson();
    final res = jsonEncode(json);
    return res;
  }
}

@immutable
class JsonRPCMethod {
  const JsonRPCMethod(this.name);

  final String name;

  /// call on method without catch exception.
  FutureOr<dynamic> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) {
    return null;
  }

  /// call on method with catch exception.
  FutureOr<JsonRPCResult> callOnRaw(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    try {
      final result = await call(context, ref, bridge, controller, request);
      return JsonRPCResult(result: result);
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s, report: false);
      if (e is JsonRPCError) {
        return JsonRPCResult(error: e);
      } else if (e is MEError) {
        return JsonRPCResult(
          error: JsonRPCError(code: e.code, message: e.toString()),
        );
      } else if (e is StateError) {
        return JsonRPCResult(
          error: JsonRPCError(
            code: JsonRPCError.bridgeIllegalState,
            message: e.message,
          ),
        );
      } else if (e is UnsupportedError) {
        return JsonRPCResult(
          error: JsonRPCError(
            code: JsonRPCError.bridgeUnsupportedError,
            message: e.message ?? 'error (${JsonRPCError.bridgeUnsupportedError})',
          ),
        );
      } else if (e is ArgumentError) {
        return JsonRPCResult(
          error: JsonRPCError(
            code: JsonRPCError.bridgeIllegalArguments,
            message: e.message ?? 'error (${JsonRPCError.bridgeIllegalArguments})',
          ),
        );
      }

      return JsonRPCResult(
        error: JsonRPCError(
          code: JsonRPCError.bridgeUnknownError,
          message: e.toString(),
        ),
      );
    }
  }

  @override
  String toString() {
    return 'JsonRPCMethod($name)';
  }
}

abstract interface class WebViewBridge {
  const WebViewBridge(this.method, this.subMethods, this.adapter);

  final String method;
  final Set<JsonRPCMethod> subMethods;
  final JsonRPCAdapter<JsonRPCRequest, JsonRPCResponse> adapter;

  void inject(
    BuildContext context,
    WidgetRef ref,
    InAppWebViewController controller,
  );
}

@immutable
class DefaultWebViewBridge implements WebViewBridge {
  const DefaultWebViewBridge({
    required this.method,
    required this.subMethods,
    required this.adapter,
  });

  @override
  final String method;
  @override
  final Set<JsonRPCMethod> subMethods;
  @override
  final JsonRPCAdapter<JsonRPCRequest, JsonRPCResponse> adapter;

  @override
  void inject(
    BuildContext context,
    WidgetRef ref,
    InAppWebViewController controller,
  ) {
    final subs = Map<String, JsonRPCMethod>.fromEntries(
      subMethods.map((JsonRPCMethod e) => MapEntry(e.name, e)),
    );
    controller.addJavaScriptHandler(
      handlerName: method,
      callback: (JavaScriptHandlerFunctionData data) async {
        final buf = data.args.firstOrNull;
        final request = await adapter.decodeRequest(buf);
        return handleJsonRPCCall(
          context,
          ref,
          this,
          controller,
          subs,
          request,
        );
      },
    );
  }
}

Future<String> handleJsonRPCCall(
  BuildContext context,
  WidgetRef ref,
  WebViewBridge bridge,
  InAppWebViewController controller,
  Map<String, JsonRPCMethod> subMethods,
  JsonRPCRequest request,
) async {
  final method = request.method;
  LogUtil.dd(
    () =>
        'bridge request : '
        '${bridge.runtimeType}(${bridge.method}), '
        'method: $method, '
        'req: ${jsonEncode(request)}',
  );
  final JsonRPCResponse response;
  if (subMethods.containsKey(method)) {
    final caller = subMethods[method];
    JsonRPCResult result;
    try {
      result = await caller!.callOnRaw(
        context,
        ref,
        bridge,
        controller,
        request,
      );
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      result = JsonRPCResult(
        error: JsonRPCError(
          code: JsonRPCError.bridgeUnknownError,
          message: e.toString(),
        ),
      );
    }
    response = JsonRPCResponse(
      id: request.id,
      jsonrpc: request.jsonrpc,
      result: result.result,
      error: result.error,
    );
  } else {
    response = JsonRPCResponse(
      id: request.id,
      jsonrpc: request.jsonrpc,
      error: JsonRPCError(
        code: JsonRPCError.bridgeUnknownMethod,
        message: request.method,
      ),
    );
  }
  LogUtil.dd(
    () =>
        'bridge response: '
        '${bridge.runtimeType}(${bridge.method}), '
        'method: $method, '
        'rep: ${jsonEncode(response)}',
  );
  return bridge.adapter.encodeResponse(response);
}
