import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart' show METhemeBuildContextExtension;
import 'package:me_extensions/me_extensions.dart';

import 'animated_text.dart';

class ChangeText extends StatelessWidget {
  const ChangeText(
    this.change, {
    super.key,
    this.style,
  });

  final double? change;
  final TextStyle? style;

  @override
  Widget build(BuildContext context) {
    return AnimatedText.rich(
      TextSpan(
        children: [
          if (change case final change?) TextSpan(text: change >= 0 ? '+' : '-'),
          // const TextSpan(text: '\$'),
          TextSpan(text: '${change?.abs().toNumerical() ?? '-'}%'),
        ],
      ),
      style: TextStyle(
        color: switch (change) {
          final c? when c > 0 => context.meTheme.successColor,
          final c? when c < 0 => context.meTheme.failingColor,
          _ => context.meTheme.captionTextColor,
        },
        fontSize: 12.0,
      ).merge(style),
      textAlign: TextAlign.end,
    );
  }
}
