import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;

import '/models/user.dart' show UserSettings;
import '/provider/api.dart' show apiServiceProvider;
import 'handle.dart';

/// 推送通知状态
class PushNotificationState {
  const PushNotificationState({
    this.isInitialized = false,
    this.fcmToken,
    this.hasPermission = false,
    this.isLoading = false,
    this.error,
  });

  final bool isInitialized;
  final String? fcmToken;
  final bool hasPermission;
  final bool isLoading;
  final String? error;

  PushNotificationState copyWith({
    bool? isInitialized,
    String? fcmToken,
    bool? hasPermission,
    bool? isLoading,
    String? error,
  }) {
    return PushNotificationState(
      isInitialized: isInitialized ?? this.isInitialized,
      fcmToken: fcmToken ?? this.fcmToken,
      hasPermission: hasPermission ?? this.hasPermission,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 推送通知状态管理
class PushNotificationNotifier extends StateNotifier<PushNotificationState> {
  PushNotificationNotifier(this._ref) : super(const PushNotificationState()) {
    _initializeService();
  }

  final Ref _ref;

  PushNotificationService get _service => _ref.read(pushNotificationServiceProvider);

  /// 初始化服务
  Future<void> _initializeService() async {
    try {
      state = state.copyWith(isLoading: true);

      // 设置消息处理回调
      _service.setMessageHandlers(
        onWalletMessage: _handleWalletMessage,
        onSocialMessage: _handleSocialMessage,
        onSystemMessage: _handleSystemMessage,
      );

      // 初始化推送服务
      await _service.initialize();

      // 检查权限状态
      final hasPermission = await _service.isNotificationEnabled();

      // 更新状态
      state = state.copyWith(
        isInitialized: true,
        fcmToken: _service.fcmToken,
        hasPermission: hasPermission,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// 检查并更新权限状态
  Future<void> checkPermission() async {
    try {
      final hasPermission = await _service.isNotificationEnabled();
      state = state.copyWith(hasPermission: hasPermission);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 打开应用设置
  Future<void> openAppSettings() async {
    try {
      await _service.openAppSettings();
      // 延迟后重新检查权限
      await Future.delayed(const Duration(seconds: 2));
      await checkPermission();
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 手动刷新FCM Token
  Future<void> refreshToken() async {
    try {
      state = state.copyWith(isLoading: true);
      await _service.refreshToken();
      state = state.copyWith(
        fcmToken: _service.fcmToken,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  /// 处理钱包通知
  void _handleWalletMessage(Map<String, dynamic> data) {
    LogUtil.d('💰 处理钱包通知: $data');
    // TODO: 根据业务需求处理钱包相关通知
    // 例如：导航到钱包页面，显示交易详情等
  }

  /// 处理社交通知
  void _handleSocialMessage(Map<String, dynamic> data) {
    LogUtil.d('👥 处理社交通知: $data');
    // TODO: 根据业务需求处理社交相关通知
    // 例如：导航到社交页面，显示新的连接请求等
  }

  /// 处理系统通知
  void _handleSystemMessage(Map<String, dynamic> data) {
    LogUtil.d('⚙️ 处理系统通知: $data');
    // TODO: 根据业务需求处理系统相关通知
    // 例如：显示系统公告，应用更新提醒等
  }
}

/// PushNotificationService 单例Provider
final pushNotificationServiceProvider = Provider<PushNotificationService>(
  PushNotificationService.new,
);

/// 推送通知状态Provider
final pushNotificationProvider = StateNotifierProvider<PushNotificationNotifier, PushNotificationState>(
  PushNotificationNotifier.new,
);

/// FCM Token Provider - 获取本地存储的token
final fcmTokenProvider = FutureProvider<String?>((ref) async {
  final service = ref.read(pushNotificationServiceProvider);
  return service.fcmToken;
});

/// 服务器端FCM Token Provider - 从服务器获取已保存的tokens
final fcmTokenByServerProvider = FutureProvider.autoDispose<UserSettings>((ref) async {
  final result = await ref.read(apiServiceProvider).getUserSettings();
  return result;
});

/// 手动发送Token到服务器的Provider（强制发送，不检查缓存）
final sendTokenToServerProvider = FutureProvider.family<void, String>((ref, token) async {
  final service = ref.read(pushNotificationServiceProvider);
  await service.sendTokenToServer(token);
  // 刷新服务器端token显示
  ref.invalidate(fcmTokenByServerProvider);
});
