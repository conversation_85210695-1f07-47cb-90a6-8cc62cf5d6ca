import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

@FFAutoImport()
import '/models/card.dart' show Social, SocialName, SocialPlatform;
import '/provider/api.dart' show apiServiceProvider;
import '/provider/card.dart';
import '/provider/user.dart' show fetchUserInfoProvider;
import '/ui/widgets/social/data.dart';

/// 社交平台配置页面
@FFRoute(name: '/social')
class SocialPage extends ConsumerStatefulWidget {
  const SocialPage({
    super.key,
    this.isSingleLink = false,
    this.action,
    this.social,
    this.platform,
    this.currentHandle,
  });

  final bool isSingleLink;
  final String? action;
  final Social? social;
  final SocialPlatform? platform;
  final String? currentHandle;

  @override
  ConsumerState<SocialPage> createState() => _SocialPageState();
}

class _SocialPageState extends ConsumerState<SocialPage> {
  // 表单控制器
  final _formKey = GlobalKey<FormState>();
  late final _handleController = TextEditingController(
    text: widget.currentHandle ?? widget.social?.handleName,
  );

  // 状态变量
  bool _isClearing = false;
  bool _isSubmitting = false;
  late final int? _socialId = widget.social?.id;
  late final bool _isSingleLink = widget.isSingleLink;
  late final String? _action = widget.action;

  @override
  void initState() {
    super.initState();

    if (widget.social?.handleName case final handle? when handle.isNotEmpty && _handleController.text.isEmpty) {
      _handleController.text = handle;
    }
  }

  @override
  void dispose() {
    _handleController.dispose();
    super.dispose();
  }

  // 验证表单
  String? _validateHandle(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }

    if (widget.platform?.name == SocialName.email) {
      final emailRegex = RegExp(r'^[\w-.]+@([\w-]+\.)+[\w-]{2,}$');
      if (!emailRegex.hasMatch(value)) {
        return 'Please enter a valid email address';
      }
    }

    return null;
  }

  // 处理表单提交
  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final platform = widget.platform;
      if (platform == null) {
        throw Exception('No social platform selected');
      }

      // 处理特殊字符
      String handle = _handleController.text;
      handle = handle.replaceAll(platform.url, '');

      if (platform.name == SocialName.youtube || platform.name == SocialName.telegram) {
        handle = handle.replaceAll(RegExp(r'^@'), '');
      }

      if (platform.name == SocialName.whatsapp) {
        handle = handle.replaceAll(RegExp(r'\+'), '');
      }

      // 根据操作类型执行不同API调用
      if (_action == 'ethcc_github') {
        // 处理GitHub提交
        await ref
            .read(apiServiceProvider)
            .updateEthccGithubHandle(
              githubHandle: handle,
            );
        // 这里可以添加刷新profile的逻辑
        ref.invalidate(fetchEthccProfileProvider);
      } else {
        if (_socialId != null) {
          await ref
              .read(apiServiceProvider)
              .socialUpdate(
                socialId: _socialId.toString(),
                platformName: platform.name.displayName,
                handleName: handle,
                platformUrl: platform.url,
              );
          ref.invalidate(fetchSocialsProvider);
        } else {
          // 添加社交媒体
          await ref
              .read(apiServiceProvider)
              .socialAdd(
                platformName: platform.name.displayName,
                handleName: handle,
                platformUrl: platform.url,
              );
          ref.invalidate(fetchSocialsProvider);
        }

        // 单链接模式更新用户信息
        if (_isSingleLink) {
          // await updateUserInfo({ redirectUrl: fullUrl });
        }
      }
      // 返回上一页
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToSubmit);
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  // 清除社交链接
  Future<void> _handleClear() async {
    setState(() {
      _isClearing = true;
    });

    try {
      if (_action == 'ethcc_github') {
        await ref
            .read(apiServiceProvider)
            .updateEthccGithubHandle(
              githubHandle: '',
            );
        // 这里可以添加刷新profile的逻辑
        ref.invalidate(fetchEthccProfileProvider);
      } else if (_socialId != null) {
        await ref
            .read(apiServiceProvider)
            .socialDelete(
              socialId: _socialId,
            );

        ref.invalidate(fetchSocialsProvider);

        // 单链接模式下重置用户重定向
        if (_isSingleLink) {
          // 此处应获取其他社交链接并设置新的重定向
          final socials = ref.read(fetchSocialsProvider()).valueOrNull ?? [];
          if (socials.isNotEmpty) {
            await ref
                .read(apiServiceProvider)
                .updateUserInfo(
                  redirectUrl: socials[0].platformUrl + socials[0].handleName,
                );
            ref.invalidate(fetchUserInfoProvider);
            ref.invalidate(fetchSocialsProvider);
          } else {
            await ref.read(apiServiceProvider).updateUserInfo(currentType: 3);
          }
        }
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToClear);
    } finally {
      setState(() {
        _isClearing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: Padding(
        padding: const EdgeInsets.only(top: 40),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: const EdgeInsets.only(top: 16, bottom: 24),
                        // padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          // borderRadius: BorderRadius.circular(16),
                          border: widget.platform?.demoImageKey != null
                              ? Border.all(color: Colors.grey[300]!)
                              : Border.all(color: Colors.transparent),
                        ),
                        child: switch (widget.platform?.demoImageKey) {
                          final key? => SocialDemoImage(name: key),
                          _ => null,
                        },
                      ),

                      // 平台信息
                      if (widget.platform case final platform?)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: Row(
                            children: [
                              // 平台图标
                              Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: platform.backgroundColor,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Center(
                                  child: Transform.scale(
                                    scale: 0.6,
                                    child: SocialSvgIcon(platform: platform),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              // 平台名称
                              Text(
                                platform.alias ?? platform.name.displayName,
                                style: const TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                      // 用户名输入框
                      Container(
                        decoration: BoxDecoration(
                          color: context.theme.cardColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: TextFormField(
                          controller: _handleController,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                          decoration: InputDecoration(
                            filled: true,
                            fillColor: context.theme.cardColor,
                            hintText: widget.platform?.placeholder ?? 'Enter username',
                            border: InputBorder.none,
                            prefixIcon: switch (widget.platform?.addon) {
                              final a? when a.isNotEmpty => Container(
                                padding: const EdgeInsets.only(
                                  left: 16,
                                  right: 16,
                                  top: 8,
                                ),
                                child: Text(
                                  a,
                                  style: const TextStyle(
                                    fontSize: 26,
                                    color: Colors.black26,
                                  ),
                                ),
                              ),
                              _ => null,
                            },
                            contentPadding: const EdgeInsets.symmetric(
                              vertical: 16,
                              horizontal: 16,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderSide: const BorderSide(
                                color: Colors.deepPurpleAccent,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Colors.grey[300]!,
                                width: 1,
                              ),
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                          validator: _validateHandle,
                        ),
                      ),

                      // // 错误提示
                      // if (_formKey.currentState?.validate() == false)
                      //   Padding(
                      //     padding: const EdgeInsets.only(top: 8.0),
                      //     child: Text(
                      //       _validateHandle(_handleController.text) ?? '',
                      //       style: const TextStyle(
                      //         color: Colors.red,
                      //         fontSize: 12,
                      //       ),
                      //     ),
                      //   ),

                      // URL预览
                      if (widget.platform case final platform?)
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0, left: 4.0),
                          child: Text(
                            '${platform.url}${_handleController.text.replaceAll(platform.url, '')}',
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 16,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              Row(
                children: [
                  // 清除按钮
                  if (_socialId != null || (_action == 'ethcc_github' && _handleController.text.isNotEmpty))
                    Expanded(
                      flex: 3,
                      child: Container(
                        height: 56,
                        margin: const EdgeInsets.only(right: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.red[300]!),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: TextButton(
                          onPressed: _isClearing || _isSubmitting ? null : _handleClear,
                          style: TextButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: _isClearing
                              ? SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    color: context.meTheme.failingColor,
                                  ),
                                )
                              : Text(
                                  'Clear',
                                  style: TextStyle(
                                    color: context.meTheme.failingColor,
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ),
                    ),

                  // 提交按钮
                  Expanded(
                    flex: 7,
                    child: Container(
                      height: 56,
                      decoration: BoxDecoration(
                        color: _isSubmitting ? Colors.grey[300] : context.themeColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: TextButton(
                        onPressed: _isSubmitting || _isClearing ? null : _handleSubmit,
                        style: TextButton.styleFrom(
                          backgroundColor: _isSubmitting ? Colors.grey[300] : context.themeColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: _isSubmitting
                            ? SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(color: context.theme.cardColor),
                              )
                            : Text(
                                context.l10nME.submitButton,
                                style: TextStyle(
                                  color: context.theme.cardColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ),
                ],
              ),
              const Gap.v(24.0),
            ],
          ),
        ),
      ),
    );
  }
}
