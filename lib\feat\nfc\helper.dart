class NfcCardParams {
  const NfcCardParams({
    required this.uid,
    this.ctr,
    this.cmac,
    this.activeCode,
  });

  final String uid;
  final String? ctr;
  final String? cmac;
  final String? activeCode;

  @override
  String toString() => 'NfcCardParams(uid: $uid, ctr: $ctr, cmac: $cmac)';
}

/// NFC协议辅助工具类
abstract final class NfcHelper {
  /// 检查扫描内容是否符合Card3 NFC格式
  /// 支持两种格式：
  /// 1. t.card3.co/?uid=044C5FD2151990&ctr=00007C&cmac=6AC65BEA2E09D7E5
  ///    或 card3.co/?uid=044C5FD2151990&ctr=00007C&cmac=6AC65BEA2E09D7E5
  /// 2. card3.co/HXAODe或t.card3.co/044C5FD2151990
  static bool isCard3Format(String content) {
    // 清理可能的前缀，如"Text: "或"URI: "
    final cleanContent = _cleanNfcContent(content);

    // 匹配第一种格式：t.card3.co/?uid=... 或 card3.co/?uid=...
    final uidFormatRegex = RegExp(r'(t\.)?card3\.co/\?uid=\w+(&ctr=\w+)?(&cmac=\w+)?');

    // 匹配第二种格式：card3.co/...
    final shortFormatRegex = RegExp(r'(t\.)?card3\.co/\w+');

    return uidFormatRegex.hasMatch(cleanContent) || shortFormatRegex.hasMatch(cleanContent);
  }

  /// 从Card3 NFC内容中提取标识符
  /// 返回包含uid、ctr和cmac的参数对象
  static NfcCardParams? extractIdentifier(String content) {
    final cleanContent = _cleanNfcContent(content);

    // 尝试匹配第一种格式：t.card3.co/?uid=044C5FD2151990&ctr=00007C&cmac=6AC65BEA2E09D7E5
    // 或 card3.co/?uid=044C5FD2151990&ctr=00007C&cmac=6AC65BEA2E09D7E5
    final uidMatch = RegExp(r'(t\.)?card3\.co/\?uid=(\w+)').firstMatch(cleanContent);
    if (uidMatch != null && uidMatch.groupCount >= 2) {
      final uid = uidMatch.group(2)!;

      // 提取ctr参数（如果存在）
      String? ctr;
      final ctrMatch = RegExp(r'ctr=(\w+)').firstMatch(cleanContent);
      if (ctrMatch != null && ctrMatch.groupCount >= 1) {
        ctr = ctrMatch.group(1);
      }

      // 提取cmac参数（如果存在）
      String? cmac;
      final cmacMatch = RegExp(r'cmac=(\w+)').firstMatch(cleanContent);
      if (cmacMatch?.group(1) case final match?) {
        cmac = match;
      }

      return NfcCardParams(uid: uid, ctr: ctr, cmac: cmac);
    }

    // 尝试匹配第二种格式：card3.co/HXAODe或t.card3.co/044C5FD2151990
    final shortMatch = RegExp(r'(t\.)?card3\.co/(\w+)').firstMatch(cleanContent);
    if (shortMatch?.group(1) case final shortCode?) {
      return NfcCardParams(uid: shortCode);
    }

    return null;
  }

  /// 清理NFC内容，移除可能的前缀
  static String _cleanNfcContent(String content) {
    // 移除可能的前缀如"Text: "或"URI: "和多余空格
    String cleanContent = content.trim();

    if (cleanContent.startsWith('Text: ')) {
      cleanContent = cleanContent.substring(6);
    } else if (cleanContent.startsWith('URI: ')) {
      cleanContent = cleanContent.substring(5);
    }

    // 处理可能的http(s)://前缀
    if (cleanContent.startsWith('http://')) {
      cleanContent = cleanContent.substring(7);
    } else if (cleanContent.startsWith('https://')) {
      cleanContent = cleanContent.substring(8);
    }

    return cleanContent.trim();
  }
}
