import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/ui/widgets/app_logo.dart';

@FFRoute(name: '/setting/about')
class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'About Card3',
      body: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: SizedBox(
                  width: 250,
                  height: 250,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      // 旋转的环
                      Assets.lottie.acard.lottie(
                        width: 250,
                        height: 250,
                        fit: BoxFit.contain,
                      ),
                    ],
                  ),
                ),
              ),
              Center(
                child: Column(
                  children: [
                    Container(
                      margin: const EdgeInsets.only(bottom: 5),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8.0,
                        vertical: 2.0,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: RadiusConstants.max,
                        color: context.theme.cardColor,
                      ),
                      child: Text(
                        '${PackageUtil.versionName} (${PackageUtil.versionCode})',
                        style: context.textTheme.titleSmall,
                      ),
                    ),
                    Text(
                      '${DateTime.parse(Release.buildTime).format(format: 'yyyy-MM-dd HH:mm:ss')} '
                      '(${Release.commitRef})',
                      style: context.textTheme.bodySmall?.copyWith(fontSize: 10.0),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  // Twitter 卡片
                  Expanded(
                    child: _buildLinkCard(
                      context,
                      icon: Assets.icons.social.twitter.svg(width: 40, height: 40),
                      title: 'Follow us on X',
                      onTap: () => _launchUrl('https://x.com/card3_ai'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // 官网卡片
                  Expanded(
                    child: _buildLinkCard(
                      context,
                      icon: const AppLogo(width: 80.0),
                      title: envUrlWebsite,
                      onTap: () => _launchUrl(envUrlWebsite),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildLinkCard(
                      context,
                      icon: SizedBox(
                        width: 80,
                        height: 40,
                        child: Assets.icons.social.telegram.svg(width: 80),
                      ),
                      title: 'Card3 Telegram',
                      onTap: () => _launchUrl('https://t.me/card3official'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: SizedBox(),
                  ),
                ],
              ),
              const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }

  // 创建链接卡片
  Widget _buildLinkCard(
    BuildContext context, {
    required Widget icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 120,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
          color: context.theme.cardColor,
        ),
        child: Column(
          spacing: 16.0,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: FittedBox(fit: BoxFit.scaleDown, child: icon),
            ),
            Text(
              title,
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // 打开URL
  Future<void> _launchUrl(String url) async {
    if (await canLaunchUrlString(url)) {
      await launchUrlString(url, mode: LaunchMode.externalApplication);
    } else {
      Card3ToastUtil.showToast(message: ToastMessages.couldNotLaunch(url));
    }
  }
}
