import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:me_utils/me_utils.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../link/helper.dart';
import 'uni_qr.dart';

abstract class ScanHandler<T> implements ScanLifecycle {
  ScanHandler() {
    onInit();
  }

  @override
  @mustCallSuper
  void onInit() {}

  @override
  @mustCallSuper
  void onResume() {}

  @override
  @mustCallSuper
  void onPause() {}

  @override
  @mustCallSuper
  void dispose() {}

  FutureOr<ScanDat> accept(BarcodeCapture result) async {
    LogUtil.dd(() => result);
    for (final code in result.barcodes) {
      if (code.rawValue != null) {
        final r = await acceptSuccess(code.rawValue!.trim());
        if (r.state != ScanState.mismatched) {
          return r;
        }
      }
    }
    return ScanDat.mismatched();
  }

  FutureOr<ScanDat> acceptSuccess(String text);
}

class AppLinkHandler extends <PERSON>an<PERSON><PERSON><PERSON><String> {
  @override
  FutureOr<ScanDat> acceptSuccess(String text) async {
    final uri = Uri.tryParse(text);
    if (uri == null) {
      return ScanDat.mismatched();
    }
    final handled = AppLinkHelper.handleUri(uri);
    return handled ? ScanDat.processed(text) : ScanDat.mismatched();
  }
}
