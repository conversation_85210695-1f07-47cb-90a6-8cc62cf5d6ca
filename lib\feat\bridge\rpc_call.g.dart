// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rpc_call.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_JsonRPCRequest _$JsonRPCRequestFromJson(Map json) => _JsonRPCRequest(
  id: json['id'] as String,
  method: json['method'] as String,
  params: json['params'] as List<dynamic>,
  jsonrpc: json['jsonrpc'] as String? ?? JsonRPC.JSON_RPC_VERSION,
);

Map<String, dynamic> _$JsonRPCRequestToJson(_JsonRPCRequest instance) =>
    <String, dynamic>{
      'id': instance.id,
      'jsonrpc': instance.jsonrpc,
      'method': instance.method,
      'params': instance.params,
    };

_JsonRPCResponse _$JsonRPCResponseFromJson(Map json) => _JsonRPCResponse(
  id: json['id'] as String,
  jsonrpc: json['jsonrpc'] as String? ?? JsonRPC.JSON_RPC_VERSION,
  result: json['result'],
  error: json['error'] == null
      ? null
      : JsonRPCError.fromJson(Map<String, Object?>.from(json['error'] as Map)),
);

Map<String, dynamic> _$JsonRPCResponseToJson(_JsonRPCResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'jsonrpc': instance.jsonrpc,
      'result': instance.result,
      'error': instance.error?.toJson(),
    };

_JsonRPCError _$JsonRPCErrorFromJson(Map json) => _JsonRPCError(
  code: (json['code'] as num).toInt(),
  message: json['message'] as String,
);

Map<String, dynamic> _$JsonRPCErrorToJson(_JsonRPCError instance) =>
    <String, dynamic>{'code': instance.code, 'message': instance.message};

_JsonRPCResult _$JsonRPCResultFromJson(Map json) => _JsonRPCResult(
  result: json['result'],
  error: json['error'] == null
      ? null
      : JsonRPCError.fromJson(Map<String, Object?>.from(json['error'] as Map)),
);

Map<String, dynamic> _$JsonRPCResultToJson(_JsonRPCResult instance) =>
    <String, dynamic>{
      'result': instance.result,
      'error': instance.error?.toJson(),
    };
