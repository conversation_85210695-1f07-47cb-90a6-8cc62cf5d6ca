import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;
import 'dart:typed_data';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;

import '/constants/envs.dart' as envs;
import '/models/user.dart';
import 'methods.dart' show udid;

const _secGroupId = '7Y7B57CS89.fun.card3';
const _secAccountName = '_osp_lovieo_';

List<int> _generateSecureKey() {
  if (io.Platform.isMacOS) {
    return utf8.encode(envs.envActive.SEC_KEY_MACOS);
  }
  return Hive.generateSecureKey();
}

Future<Uint8List> _prepareEncryptKey() async {
  final keyField = 'card3_ec_key_${io.Platform.operatingSystem}';
  const storage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.unlocked_this_device,
      accountName: _secAccountName,
      groupId: _secGroupId,
      synchronizable: false,
    ),
    mOptions: MacOsOptions(
      accessibility: KeychainAccessibility.unlocked_this_device,
      accountName: _secAccountName,
      groupId: _secGroupId,
      synchronizable: false,
    ),
  );

  final stored = await storage.read(key: keyField);
  if (stored != null) {
    return base64Url.decode(stored);
  }

  // Creates a new key if no previous key found.
  final newKey = base64UrlEncode(_generateSecureKey());
  await storage.write(key: keyField, value: newKey);
  return base64Url.decode(newKey);
}

final class Boxes {
  const Boxes._();

  static const _tag = '💾 Hive';

  static late final Box user;
  static late final Box settings;
  static late final Box cache;
  static late final Box subToken;

  // Set this to true to allow boxes recovery.
  static bool get recoveredFromAnotherDevice => _recoveredFromAnotherDevice;
  static bool _recoveredFromAnotherDevice = false;

  static Future<void> init() async {
    await Hive.initFlutter();
    final key = await _prepareEncryptKey();
    final cipher = HiveAesCipher(key);

    try {
      await _init(cipher);
    } on HiveError catch (e) {
      if (e.message.startsWith('Wrong checksum')) {
        _recoveredFromAnotherDevice = true;
        await _init(cipher);
        return;
      }
      rethrow;
    }
  }

  static Future<String> _getBoxPrefix() async {
    // if (await envs.EnvOverrides.localOverrode case final env?) {
    //   Env.$active = env;
    // }
    final buffer = StringBuffer(envs.envActive.env);
    if (envs.isAuditing) {
      buffer.write(':auditing');
    }
    return buffer.toString();
  }

  static Future<void> _init(HiveCipher cipher) async {
    LogUtil.d(
      'Opening boxes with cipher: ${cipher.calculateKeyCrc()}',
      tag: _tag,
    );
    final prefix = await _getBoxPrefix();
    await Future.wait([
      Hive.openBox(
        '${prefix}_user_v1',
        encryptionCipher: cipher,
        crashRecovery: _recoveredFromAnotherDevice,
      ).then((box) => user = box),
      Hive.openBox(
        '${prefix}_settings_v1',
        encryptionCipher: cipher,
        crashRecovery: _recoveredFromAnotherDevice,
      ).then((box) async {
        settings = box;
        if (!settings.containsKey('udid:1')) {
          await settings.put('udid:1', udid());
        }
      }),
      Hive.openBox(
        '${prefix}_cache_v1',
        encryptionCipher: cipher,
        crashRecovery: _recoveredFromAnotherDevice,
      ).then((box) => cache = box),
      Hive.openBox(
        '${prefix}_sub_token_v1',
      ).then((box) async {
        await box.clear();
        subToken = box;
      }),
    ]);
  }
}

final class BoxKeys {
  const BoxKeys._();

  static const token = 'token';
  static const info = 'info';
  static const settings = 'settings';
  static const chains = 'chain-list';
  static const selectedChain = 'chain-selected';
}

abstract class BoxCaches {
  static const kWalletPortfolio = 'wallet_portfolio:v1';

  static const keys = [
    kWalletPortfolio,
  ];

  static const balanceKeys = [
    kWalletPortfolio,
  ];

  static E? get<E>(dynamic key, {E? defaultValue}) {
    return Boxes.cache.get(key, defaultValue: defaultValue);
  }

  static Future<void> put<E>(dynamic key, E value) {
    return Boxes.cache.put(key, value);
  }

  static Future<void> clear() {
    final keys = BoxCaches.keys
        .map(
          (prefix) => Boxes.cache.keys.where(
            (k) => k.toString().startsWith(prefix),
          ),
        )
        .reduce((v, e) => [...v, ...e]);
    return Boxes.cache.deleteAll(keys);
  }

  static Future<void> clearByPrefix(String prefix) {
    final keys = Boxes.cache.keys.where(
      (k) => k.toString().startsWith(prefix),
    );
    return Boxes.cache.deleteAll(keys);
  }

  static Future<void> clearBalanceCaches() {
    return Future.wait(balanceKeys.map(clearByPrefix));
  }
}

final class BoxService {
  const BoxService._();

  static Stream<BoxEvent> watchUser({dynamic key}) {
    return Boxes.user.watch(key: key);
  }

  static Future<void> clearUserBox() => Boxes.user.clear();

  static String? getToken() => Boxes.user.get(BoxKeys.token) as String?;

  static Future<String> updateToken(String token) async {
    await Boxes.user.put(BoxKeys.token, token);
    return token;
  }

  static UserInfo? getUserInfo() {
    return switch (Boxes.user.get(BoxKeys.info)) {
      final String s when s.isNotEmpty => UserInfo.fromJson(s.deserialize()),
      _ => null,
    };
  }

  static Future<UserInfo> updateUser(UserInfo user) async {
    await Boxes.user.put(BoxKeys.info, user.toJson().serialize());
    return user;
  }

  static dynamic getSetting(dynamic key, {dynamic defaultValue}) {
    key = '${BoxKeys.settings}:$key';
    return Boxes.settings.get(key, defaultValue: defaultValue);
  }

  static Future<void> updateSetting(dynamic key, dynamic value) {
    key = '${BoxKeys.settings}:$key';
    return Boxes.settings.put(key, value);
  }
}
