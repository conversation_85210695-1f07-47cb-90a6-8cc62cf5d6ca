targets:
  $default:
    builders:
      json_serializable:
        options:
          any_map: true
          explicit_to_json: true
        generate_for:
          include:
            - lib/api/*.dart
            - lib/feat/bridge/rpc_call.dart
            - lib/models/*.dart
            - lib/provider/*.dart
          exclude:
            - lib/**/*.g.dart
      freezed:
        options:
          format: true
        generate_for:
          include:
            - lib/api/*.dart
            - lib/feat/bridge/rpc_call.dart
            - lib/models/*.dart
            - lib/provider/*.dart
          exclude:
            - lib/**/*.g.dart
      riverpod_generator:
        generate_for:
          include:
            - lib/provider/*.dart
          exclude:
            - lib/**/*.g.dart
#      hive_generator:
#        generate_for:
#          include:
#            - lib/models/*.dart
#          exclude:
#            - lib/**/*.freezed.dart
#            - lib/**/*.g.dart
      flutter_gen_runner:
        generate_for:
          exclude:
            - '**/*.dart'
