// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'card.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$Social {
  @Json<PERSON>ey(name: 'handleName')
  String get handleName;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'platformName')
  String get platformName;
  @JsonKey(name: 'platformUrl')
  String get platformUrl;
  @JsonKey(name: 'isVerify')
  bool get isVerify;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SocialCopyWith<Social> get copyWith =>
      _$SocialCopyWithImpl<Social>(this as Social, _$identity);

  /// Serializes this Social to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Social &&
            (identical(other.handleName, handleName) ||
                other.handleName == handleName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.platformName, platformName) ||
                other.platformName == platformName) &&
            (identical(other.platformUrl, platformUrl) ||
                other.platformUrl == platformUrl) &&
            (identical(other.isVerify, isVerify) ||
                other.isVerify == isVerify));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    handleName,
    id,
    platformName,
    platformUrl,
    isVerify,
  );

  @override
  String toString() {
    return 'Social(handleName: $handleName, id: $id, platformName: $platformName, platformUrl: $platformUrl, isVerify: $isVerify)';
  }
}

/// @nodoc
abstract mixin class $SocialCopyWith<$Res> {
  factory $SocialCopyWith(Social value, $Res Function(Social) _then) =
      _$SocialCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'handleName') String handleName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'platformName') String platformName,
    @JsonKey(name: 'platformUrl') String platformUrl,
    @JsonKey(name: 'isVerify') bool isVerify,
  });
}

/// @nodoc
class _$SocialCopyWithImpl<$Res> implements $SocialCopyWith<$Res> {
  _$SocialCopyWithImpl(this._self, this._then);

  final Social _self;
  final $Res Function(Social) _then;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? handleName = null,
    Object? id = null,
    Object? platformName = null,
    Object? platformUrl = null,
    Object? isVerify = null,
  }) {
    return _then(
      _self.copyWith(
        handleName: null == handleName
            ? _self.handleName
            : handleName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        platformName: null == platformName
            ? _self.platformName
            : platformName // ignore: cast_nullable_to_non_nullable
                  as String,
        platformUrl: null == platformUrl
            ? _self.platformUrl
            : platformUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        isVerify: null == isVerify
            ? _self.isVerify
            : isVerify // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Social implements Social {
  const _Social({
    @JsonKey(name: 'handleName') required this.handleName,
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'platformName') required this.platformName,
    @JsonKey(name: 'platformUrl') required this.platformUrl,
    @JsonKey(name: 'isVerify') this.isVerify = false,
  });
  factory _Social.fromJson(Map<String, dynamic> json) => _$SocialFromJson(json);

  @override
  @JsonKey(name: 'handleName')
  final String handleName;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'platformName')
  final String platformName;
  @override
  @JsonKey(name: 'platformUrl')
  final String platformUrl;
  @override
  @JsonKey(name: 'isVerify')
  final bool isVerify;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SocialCopyWith<_Social> get copyWith =>
      __$SocialCopyWithImpl<_Social>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SocialToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Social &&
            (identical(other.handleName, handleName) ||
                other.handleName == handleName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.platformName, platformName) ||
                other.platformName == platformName) &&
            (identical(other.platformUrl, platformUrl) ||
                other.platformUrl == platformUrl) &&
            (identical(other.isVerify, isVerify) ||
                other.isVerify == isVerify));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    handleName,
    id,
    platformName,
    platformUrl,
    isVerify,
  );

  @override
  String toString() {
    return 'Social(handleName: $handleName, id: $id, platformName: $platformName, platformUrl: $platformUrl, isVerify: $isVerify)';
  }
}

/// @nodoc
abstract mixin class _$SocialCopyWith<$Res> implements $SocialCopyWith<$Res> {
  factory _$SocialCopyWith(_Social value, $Res Function(_Social) _then) =
      __$SocialCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'handleName') String handleName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'platformName') String platformName,
    @JsonKey(name: 'platformUrl') String platformUrl,
    @JsonKey(name: 'isVerify') bool isVerify,
  });
}

/// @nodoc
class __$SocialCopyWithImpl<$Res> implements _$SocialCopyWith<$Res> {
  __$SocialCopyWithImpl(this._self, this._then);

  final _Social _self;
  final $Res Function(_Social) _then;

  /// Create a copy of Social
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? handleName = null,
    Object? id = null,
    Object? platformName = null,
    Object? platformUrl = null,
    Object? isVerify = null,
  }) {
    return _then(
      _Social(
        handleName: null == handleName
            ? _self.handleName
            : handleName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        platformName: null == platformName
            ? _self.platformName
            : platformName // ignore: cast_nullable_to_non_nullable
                  as String,
        platformUrl: null == platformUrl
            ? _self.platformUrl
            : platformUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        isVerify: null == isVerify
            ? _self.isVerify
            : isVerify // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }
}

/// @nodoc
mixin _$CardInfo {
  @JsonKey(name: 'active')
  bool get active;
  @JsonKey(name: 'activeTime')
  String get activeTime;
  @JsonKey(name: 'backCover')
  String get backCover;
  @JsonKey(name: 'card3EventId')
  int get card3EventId;
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'cardType')
  CardType get cardType;
  @JsonKey(name: 'chainId')
  int get chainId;
  @JsonKey(name: 'eventName')
  String get eventName;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'isActive')
  bool get isActive;
  @JsonKey(name: 'referralCode')
  String get referralCode;
  @JsonKey(name: 'virtualCard')
  bool get virtualCard;
  @JsonKey(name: 'nfcType')
  NfcType get nfcType;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CardInfoCopyWith<CardInfo> get copyWith =>
      _$CardInfoCopyWithImpl<CardInfo>(this as CardInfo, _$identity);

  /// Serializes this CardInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CardInfo &&
            (identical(other.active, active) || other.active == active) &&
            (identical(other.activeTime, activeTime) ||
                other.activeTime == activeTime) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.card3EventId, card3EventId) ||
                other.card3EventId == card3EventId) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.virtualCard, virtualCard) ||
                other.virtualCard == virtualCard) &&
            (identical(other.nfcType, nfcType) || other.nfcType == nfcType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    active,
    activeTime,
    backCover,
    card3EventId,
    cardCode,
    cardType,
    chainId,
    eventName,
    id,
    isActive,
    referralCode,
    virtualCard,
    nfcType,
  );

  @override
  String toString() {
    return 'CardInfo(active: $active, activeTime: $activeTime, backCover: $backCover, card3EventId: $card3EventId, cardCode: $cardCode, cardType: $cardType, chainId: $chainId, eventName: $eventName, id: $id, isActive: $isActive, referralCode: $referralCode, virtualCard: $virtualCard, nfcType: $nfcType)';
  }
}

/// @nodoc
abstract mixin class $CardInfoCopyWith<$Res> {
  factory $CardInfoCopyWith(CardInfo value, $Res Function(CardInfo) _then) =
      _$CardInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'active') bool active,
    @JsonKey(name: 'activeTime') String activeTime,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'card3EventId') int card3EventId,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'cardType') CardType cardType,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isActive') bool isActive,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'virtualCard') bool virtualCard,
    @JsonKey(name: 'nfcType') NfcType nfcType,
  });
}

/// @nodoc
class _$CardInfoCopyWithImpl<$Res> implements $CardInfoCopyWith<$Res> {
  _$CardInfoCopyWithImpl(this._self, this._then);

  final CardInfo _self;
  final $Res Function(CardInfo) _then;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? active = null,
    Object? activeTime = null,
    Object? backCover = null,
    Object? card3EventId = null,
    Object? cardCode = null,
    Object? cardType = null,
    Object? chainId = null,
    Object? eventName = null,
    Object? id = null,
    Object? isActive = null,
    Object? referralCode = null,
    Object? virtualCard = null,
    Object? nfcType = null,
  }) {
    return _then(
      _self.copyWith(
        active: null == active
            ? _self.active
            : active // ignore: cast_nullable_to_non_nullable
                  as bool,
        activeTime: null == activeTime
            ? _self.activeTime
            : activeTime // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        card3EventId: null == card3EventId
            ? _self.card3EventId
            : card3EventId // ignore: cast_nullable_to_non_nullable
                  as int,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardType: null == cardType
            ? _self.cardType
            : cardType // ignore: cast_nullable_to_non_nullable
                  as CardType,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isActive: null == isActive
            ? _self.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        virtualCard: null == virtualCard
            ? _self.virtualCard
            : virtualCard // ignore: cast_nullable_to_non_nullable
                  as bool,
        nfcType: null == nfcType
            ? _self.nfcType
            : nfcType // ignore: cast_nullable_to_non_nullable
                  as NfcType,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _CardInfo implements CardInfo {
  const _CardInfo({
    @JsonKey(name: 'active') required this.active,
    @JsonKey(name: 'activeTime') this.activeTime = '',
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'card3EventId') this.card3EventId = 0,
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'cardType') this.cardType = CardType.CARD,
    @JsonKey(name: 'chainId') this.chainId = 0,
    @JsonKey(name: 'eventName') this.eventName = '',
    @JsonKey(name: 'id') this.id = 0,
    @JsonKey(name: 'isActive') this.isActive = false,
    @JsonKey(name: 'referralCode') this.referralCode = '',
    @JsonKey(name: 'virtualCard') this.virtualCard = false,
    @JsonKey(name: 'nfcType') this.nfcType = NfcType.NFC215,
  });
  factory _CardInfo.fromJson(Map<String, dynamic> json) =>
      _$CardInfoFromJson(json);

  @override
  @JsonKey(name: 'active')
  final bool active;
  @override
  @JsonKey(name: 'activeTime')
  final String activeTime;
  @override
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'card3EventId')
  final int card3EventId;
  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'cardType')
  final CardType cardType;
  @override
  @JsonKey(name: 'chainId')
  final int chainId;
  @override
  @JsonKey(name: 'eventName')
  final String eventName;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'isActive')
  final bool isActive;
  @override
  @JsonKey(name: 'referralCode')
  final String referralCode;
  @override
  @JsonKey(name: 'virtualCard')
  final bool virtualCard;
  @override
  @JsonKey(name: 'nfcType')
  final NfcType nfcType;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CardInfoCopyWith<_CardInfo> get copyWith =>
      __$CardInfoCopyWithImpl<_CardInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CardInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CardInfo &&
            (identical(other.active, active) || other.active == active) &&
            (identical(other.activeTime, activeTime) ||
                other.activeTime == activeTime) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.card3EventId, card3EventId) ||
                other.card3EventId == card3EventId) &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.cardType, cardType) ||
                other.cardType == cardType) &&
            (identical(other.chainId, chainId) || other.chainId == chainId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.referralCode, referralCode) ||
                other.referralCode == referralCode) &&
            (identical(other.virtualCard, virtualCard) ||
                other.virtualCard == virtualCard) &&
            (identical(other.nfcType, nfcType) || other.nfcType == nfcType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    active,
    activeTime,
    backCover,
    card3EventId,
    cardCode,
    cardType,
    chainId,
    eventName,
    id,
    isActive,
    referralCode,
    virtualCard,
    nfcType,
  );

  @override
  String toString() {
    return 'CardInfo(active: $active, activeTime: $activeTime, backCover: $backCover, card3EventId: $card3EventId, cardCode: $cardCode, cardType: $cardType, chainId: $chainId, eventName: $eventName, id: $id, isActive: $isActive, referralCode: $referralCode, virtualCard: $virtualCard, nfcType: $nfcType)';
  }
}

/// @nodoc
abstract mixin class _$CardInfoCopyWith<$Res>
    implements $CardInfoCopyWith<$Res> {
  factory _$CardInfoCopyWith(_CardInfo value, $Res Function(_CardInfo) _then) =
      __$CardInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'active') bool active,
    @JsonKey(name: 'activeTime') String activeTime,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'card3EventId') int card3EventId,
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'cardType') CardType cardType,
    @JsonKey(name: 'chainId') int chainId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isActive') bool isActive,
    @JsonKey(name: 'referralCode') String referralCode,
    @JsonKey(name: 'virtualCard') bool virtualCard,
    @JsonKey(name: 'nfcType') NfcType nfcType,
  });
}

/// @nodoc
class __$CardInfoCopyWithImpl<$Res> implements _$CardInfoCopyWith<$Res> {
  __$CardInfoCopyWithImpl(this._self, this._then);

  final _CardInfo _self;
  final $Res Function(_CardInfo) _then;

  /// Create a copy of CardInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? active = null,
    Object? activeTime = null,
    Object? backCover = null,
    Object? card3EventId = null,
    Object? cardCode = null,
    Object? cardType = null,
    Object? chainId = null,
    Object? eventName = null,
    Object? id = null,
    Object? isActive = null,
    Object? referralCode = null,
    Object? virtualCard = null,
    Object? nfcType = null,
  }) {
    return _then(
      _CardInfo(
        active: null == active
            ? _self.active
            : active // ignore: cast_nullable_to_non_nullable
                  as bool,
        activeTime: null == activeTime
            ? _self.activeTime
            : activeTime // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        card3EventId: null == card3EventId
            ? _self.card3EventId
            : card3EventId // ignore: cast_nullable_to_non_nullable
                  as int,
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        cardType: null == cardType
            ? _self.cardType
            : cardType // ignore: cast_nullable_to_non_nullable
                  as CardType,
        chainId: null == chainId
            ? _self.chainId
            : chainId // ignore: cast_nullable_to_non_nullable
                  as int,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isActive: null == isActive
            ? _self.isActive
            : isActive // ignore: cast_nullable_to_non_nullable
                  as bool,
        referralCode: null == referralCode
            ? _self.referralCode
            : referralCode // ignore: cast_nullable_to_non_nullable
                  as String,
        virtualCard: null == virtualCard
            ? _self.virtualCard
            : virtualCard // ignore: cast_nullable_to_non_nullable
                  as bool,
        nfcType: null == nfcType
            ? _self.nfcType
            : nfcType // ignore: cast_nullable_to_non_nullable
                  as NfcType,
      ),
    );
  }
}

/// @nodoc
mixin _$CoverInfo {
  @JsonKey(name: 'activeMode')
  String get activeMode;
  @JsonKey(name: 'backCover')
  String get backCover;
  @JsonKey(name: 'eventId')
  String get eventId;
  @JsonKey(name: 'eventName')
  String get eventName;
  @JsonKey(name: 'paymentLink')
  String get paymentLink;
  @JsonKey(name: 'price')
  int get price;
  @JsonKey(name: 'priceDescription')
  String get priceDescription;
  @JsonKey(name: 'printType')
  PrintType get printType;
  @JsonKey(name: 'thirdPartyLink')
  String get thirdPartyLink;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CoverInfoCopyWith<CoverInfo> get copyWith =>
      _$CoverInfoCopyWithImpl<CoverInfo>(this as CoverInfo, _$identity);

  /// Serializes this CoverInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CoverInfo &&
            (identical(other.activeMode, activeMode) ||
                other.activeMode == activeMode) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceDescription, priceDescription) ||
                other.priceDescription == priceDescription) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.thirdPartyLink, thirdPartyLink) ||
                other.thirdPartyLink == thirdPartyLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    activeMode,
    backCover,
    eventId,
    eventName,
    paymentLink,
    price,
    priceDescription,
    printType,
    thirdPartyLink,
  );

  @override
  String toString() {
    return 'CoverInfo(activeMode: $activeMode, backCover: $backCover, eventId: $eventId, eventName: $eventName, paymentLink: $paymentLink, price: $price, priceDescription: $priceDescription, printType: $printType, thirdPartyLink: $thirdPartyLink)';
  }
}

/// @nodoc
abstract mixin class $CoverInfoCopyWith<$Res> {
  factory $CoverInfoCopyWith(CoverInfo value, $Res Function(CoverInfo) _then) =
      _$CoverInfoCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'activeMode') String activeMode,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventId') String eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'price') int price,
    @JsonKey(name: 'priceDescription') String priceDescription,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
  });
}

/// @nodoc
class _$CoverInfoCopyWithImpl<$Res> implements $CoverInfoCopyWith<$Res> {
  _$CoverInfoCopyWithImpl(this._self, this._then);

  final CoverInfo _self;
  final $Res Function(CoverInfo) _then;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? activeMode = null,
    Object? backCover = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? paymentLink = null,
    Object? price = null,
    Object? priceDescription = null,
    Object? printType = null,
    Object? thirdPartyLink = null,
  }) {
    return _then(
      _self.copyWith(
        activeMode: null == activeMode
            ? _self.activeMode
            : activeMode // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        priceDescription: null == priceDescription
            ? _self.priceDescription
            : priceDescription // ignore: cast_nullable_to_non_nullable
                  as String,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        thirdPartyLink: null == thirdPartyLink
            ? _self.thirdPartyLink
            : thirdPartyLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _CoverInfo implements CoverInfo {
  const _CoverInfo({
    @JsonKey(name: 'activeMode') this.activeMode = '',
    @JsonKey(name: 'backCover') this.backCover = '',
    @JsonKey(name: 'eventId') this.eventId = '',
    @JsonKey(name: 'eventName') this.eventName = '',
    @JsonKey(name: 'paymentLink') this.paymentLink = '',
    @JsonKey(name: 'price') this.price = 0,
    @JsonKey(name: 'priceDescription') this.priceDescription = '',
    @JsonKey(name: 'printType') this.printType = PrintType.NORMAL,
    @JsonKey(name: 'thirdPartyLink') this.thirdPartyLink = '',
  });
  factory _CoverInfo.fromJson(Map<String, dynamic> json) =>
      _$CoverInfoFromJson(json);

  @override
  @JsonKey(name: 'activeMode')
  final String activeMode;
  @override
  @JsonKey(name: 'backCover')
  final String backCover;
  @override
  @JsonKey(name: 'eventId')
  final String eventId;
  @override
  @JsonKey(name: 'eventName')
  final String eventName;
  @override
  @JsonKey(name: 'paymentLink')
  final String paymentLink;
  @override
  @JsonKey(name: 'price')
  final int price;
  @override
  @JsonKey(name: 'priceDescription')
  final String priceDescription;
  @override
  @JsonKey(name: 'printType')
  final PrintType printType;
  @override
  @JsonKey(name: 'thirdPartyLink')
  final String thirdPartyLink;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CoverInfoCopyWith<_CoverInfo> get copyWith =>
      __$CoverInfoCopyWithImpl<_CoverInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CoverInfoToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CoverInfo &&
            (identical(other.activeMode, activeMode) ||
                other.activeMode == activeMode) &&
            (identical(other.backCover, backCover) ||
                other.backCover == backCover) &&
            (identical(other.eventId, eventId) || other.eventId == eventId) &&
            (identical(other.eventName, eventName) ||
                other.eventName == eventName) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.priceDescription, priceDescription) ||
                other.priceDescription == priceDescription) &&
            (identical(other.printType, printType) ||
                other.printType == printType) &&
            (identical(other.thirdPartyLink, thirdPartyLink) ||
                other.thirdPartyLink == thirdPartyLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    activeMode,
    backCover,
    eventId,
    eventName,
    paymentLink,
    price,
    priceDescription,
    printType,
    thirdPartyLink,
  );

  @override
  String toString() {
    return 'CoverInfo(activeMode: $activeMode, backCover: $backCover, eventId: $eventId, eventName: $eventName, paymentLink: $paymentLink, price: $price, priceDescription: $priceDescription, printType: $printType, thirdPartyLink: $thirdPartyLink)';
  }
}

/// @nodoc
abstract mixin class _$CoverInfoCopyWith<$Res>
    implements $CoverInfoCopyWith<$Res> {
  factory _$CoverInfoCopyWith(
    _CoverInfo value,
    $Res Function(_CoverInfo) _then,
  ) = __$CoverInfoCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'activeMode') String activeMode,
    @JsonKey(name: 'backCover') String backCover,
    @JsonKey(name: 'eventId') String eventId,
    @JsonKey(name: 'eventName') String eventName,
    @JsonKey(name: 'paymentLink') String paymentLink,
    @JsonKey(name: 'price') int price,
    @JsonKey(name: 'priceDescription') String priceDescription,
    @JsonKey(name: 'printType') PrintType printType,
    @JsonKey(name: 'thirdPartyLink') String thirdPartyLink,
  });
}

/// @nodoc
class __$CoverInfoCopyWithImpl<$Res> implements _$CoverInfoCopyWith<$Res> {
  __$CoverInfoCopyWithImpl(this._self, this._then);

  final _CoverInfo _self;
  final $Res Function(_CoverInfo) _then;

  /// Create a copy of CoverInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? activeMode = null,
    Object? backCover = null,
    Object? eventId = null,
    Object? eventName = null,
    Object? paymentLink = null,
    Object? price = null,
    Object? priceDescription = null,
    Object? printType = null,
    Object? thirdPartyLink = null,
  }) {
    return _then(
      _CoverInfo(
        activeMode: null == activeMode
            ? _self.activeMode
            : activeMode // ignore: cast_nullable_to_non_nullable
                  as String,
        backCover: null == backCover
            ? _self.backCover
            : backCover // ignore: cast_nullable_to_non_nullable
                  as String,
        eventId: null == eventId
            ? _self.eventId
            : eventId // ignore: cast_nullable_to_non_nullable
                  as String,
        eventName: null == eventName
            ? _self.eventName
            : eventName // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as int,
        priceDescription: null == priceDescription
            ? _self.priceDescription
            : priceDescription // ignore: cast_nullable_to_non_nullable
                  as String,
        printType: null == printType
            ? _self.printType
            : printType // ignore: cast_nullable_to_non_nullable
                  as PrintType,
        thirdPartyLink: null == thirdPartyLink
            ? _self.thirdPartyLink
            : thirdPartyLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$CreateCardCoverResponse {
  @JsonKey(name: 'code')
  String get code;
  @JsonKey(name: 'paymentLink')
  String get paymentLink;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateCardCoverResponseCopyWith<CreateCardCoverResponse> get copyWith =>
      _$CreateCardCoverResponseCopyWithImpl<CreateCardCoverResponse>(
        this as CreateCardCoverResponse,
        _$identity,
      );

  /// Serializes this CreateCardCoverResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateCardCoverResponse &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, paymentLink);

  @override
  String toString() {
    return 'CreateCardCoverResponse(code: $code, paymentLink: $paymentLink)';
  }
}

/// @nodoc
abstract mixin class $CreateCardCoverResponseCopyWith<$Res> {
  factory $CreateCardCoverResponseCopyWith(
    CreateCardCoverResponse value,
    $Res Function(CreateCardCoverResponse) _then,
  ) = _$CreateCardCoverResponseCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'code') String code,
    @JsonKey(name: 'paymentLink') String paymentLink,
  });
}

/// @nodoc
class _$CreateCardCoverResponseCopyWithImpl<$Res>
    implements $CreateCardCoverResponseCopyWith<$Res> {
  _$CreateCardCoverResponseCopyWithImpl(this._self, this._then);

  final CreateCardCoverResponse _self;
  final $Res Function(CreateCardCoverResponse) _then;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? code = null, Object? paymentLink = null}) {
    return _then(
      _self.copyWith(
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _CreateCardCoverResponse implements CreateCardCoverResponse {
  const _CreateCardCoverResponse({
    @JsonKey(name: 'code') required this.code,
    @JsonKey(name: 'paymentLink') this.paymentLink = '',
  });
  factory _CreateCardCoverResponse.fromJson(Map<String, dynamic> json) =>
      _$CreateCardCoverResponseFromJson(json);

  @override
  @JsonKey(name: 'code')
  final String code;
  @override
  @JsonKey(name: 'paymentLink')
  final String paymentLink;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateCardCoverResponseCopyWith<_CreateCardCoverResponse> get copyWith =>
      __$CreateCardCoverResponseCopyWithImpl<_CreateCardCoverResponse>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$CreateCardCoverResponseToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateCardCoverResponse &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.paymentLink, paymentLink) ||
                other.paymentLink == paymentLink));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, paymentLink);

  @override
  String toString() {
    return 'CreateCardCoverResponse(code: $code, paymentLink: $paymentLink)';
  }
}

/// @nodoc
abstract mixin class _$CreateCardCoverResponseCopyWith<$Res>
    implements $CreateCardCoverResponseCopyWith<$Res> {
  factory _$CreateCardCoverResponseCopyWith(
    _CreateCardCoverResponse value,
    $Res Function(_CreateCardCoverResponse) _then,
  ) = __$CreateCardCoverResponseCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'code') String code,
    @JsonKey(name: 'paymentLink') String paymentLink,
  });
}

/// @nodoc
class __$CreateCardCoverResponseCopyWithImpl<$Res>
    implements _$CreateCardCoverResponseCopyWith<$Res> {
  __$CreateCardCoverResponseCopyWithImpl(this._self, this._then);

  final _CreateCardCoverResponse _self;
  final $Res Function(_CreateCardCoverResponse) _then;

  /// Create a copy of CreateCardCoverResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? code = null, Object? paymentLink = null}) {
    return _then(
      _CreateCardCoverResponse(
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String,
        paymentLink: null == paymentLink
            ? _self.paymentLink
            : paymentLink // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$EthccProfile {
  @JsonKey(name: 'githubHandle')
  String get githubHandle;
  @JsonKey(
    name: 'topics',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  List<String> get topics;
  @JsonKey(
    name: 'role',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  @JsonKey(name: 'role')
  List<String> get roles;

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EthccProfileCopyWith<EthccProfile> get copyWith =>
      _$EthccProfileCopyWithImpl<EthccProfile>(
        this as EthccProfile,
        _$identity,
      );

  /// Serializes this EthccProfile to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EthccProfile &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle) &&
            const DeepCollectionEquality().equals(other.topics, topics) &&
            const DeepCollectionEquality().equals(other.roles, roles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    githubHandle,
    const DeepCollectionEquality().hash(topics),
    const DeepCollectionEquality().hash(roles),
  );

  @override
  String toString() {
    return 'EthccProfile(githubHandle: $githubHandle, topics: $topics, roles: $roles)';
  }
}

/// @nodoc
abstract mixin class $EthccProfileCopyWith<$Res> {
  factory $EthccProfileCopyWith(
    EthccProfile value,
    $Res Function(EthccProfile) _then,
  ) = _$EthccProfileCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'githubHandle') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    List<String> roles,
  });
}

/// @nodoc
class _$EthccProfileCopyWithImpl<$Res> implements $EthccProfileCopyWith<$Res> {
  _$EthccProfileCopyWithImpl(this._self, this._then);

  final EthccProfile _self;
  final $Res Function(EthccProfile) _then;

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? githubHandle = null,
    Object? topics = null,
    Object? roles = null,
  }) {
    return _then(
      _self.copyWith(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
        topics: null == topics
            ? _self.topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        roles: null == roles
            ? _self.roles
            : roles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _EthccProfile extends EthccProfile {
  const _EthccProfile({
    @JsonKey(name: 'githubHandle') this.githubHandle = '',
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    final List<String> topics = const [],
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    final List<String> roles = const [],
  }) : _topics = topics,
       _roles = roles,
       super._();
  factory _EthccProfile.fromJson(Map<String, dynamic> json) =>
      _$EthccProfileFromJson(json);

  @override
  @JsonKey(name: 'githubHandle')
  final String githubHandle;
  final List<String> _topics;
  @override
  @JsonKey(
    name: 'topics',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  List<String> get topics {
    if (_topics is EqualUnmodifiableListView) return _topics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topics);
  }

  final List<String> _roles;
  @override
  @JsonKey(
    name: 'role',
    fromJson: EthccProfile.fromJoinedString,
    toJson: EthccProfile.toJoinedString,
  )
  @JsonKey(name: 'role')
  List<String> get roles {
    if (_roles is EqualUnmodifiableListView) return _roles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_roles);
  }

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EthccProfileCopyWith<_EthccProfile> get copyWith =>
      __$EthccProfileCopyWithImpl<_EthccProfile>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EthccProfileToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EthccProfile &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle) &&
            const DeepCollectionEquality().equals(other._topics, _topics) &&
            const DeepCollectionEquality().equals(other._roles, _roles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    githubHandle,
    const DeepCollectionEquality().hash(_topics),
    const DeepCollectionEquality().hash(_roles),
  );

  @override
  String toString() {
    return 'EthccProfile(githubHandle: $githubHandle, topics: $topics, roles: $roles)';
  }
}

/// @nodoc
abstract mixin class _$EthccProfileCopyWith<$Res>
    implements $EthccProfileCopyWith<$Res> {
  factory _$EthccProfileCopyWith(
    _EthccProfile value,
    $Res Function(_EthccProfile) _then,
  ) = __$EthccProfileCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'githubHandle') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    List<String> roles,
  });
}

/// @nodoc
class __$EthccProfileCopyWithImpl<$Res>
    implements _$EthccProfileCopyWith<$Res> {
  __$EthccProfileCopyWithImpl(this._self, this._then);

  final _EthccProfile _self;
  final $Res Function(_EthccProfile) _then;

  /// Create a copy of EthccProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? githubHandle = null,
    Object? topics = null,
    Object? roles = null,
  }) {
    return _then(
      _EthccProfile(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
        topics: null == topics
            ? _self._topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as List<String>,
        roles: null == roles
            ? _self._roles
            : roles // ignore: cast_nullable_to_non_nullable
                  as List<String>,
      ),
    );
  }
}

/// @nodoc
mixin _$UpdateEthccTopicsRequest {
  @JsonKey(name: 'topics')
  String get topics;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateEthccTopicsRequestCopyWith<UpdateEthccTopicsRequest> get copyWith =>
      _$UpdateEthccTopicsRequestCopyWithImpl<UpdateEthccTopicsRequest>(
        this as UpdateEthccTopicsRequest,
        _$identity,
      );

  /// Serializes this UpdateEthccTopicsRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateEthccTopicsRequest &&
            (identical(other.topics, topics) || other.topics == topics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, topics);

  @override
  String toString() {
    return 'UpdateEthccTopicsRequest(topics: $topics)';
  }
}

/// @nodoc
abstract mixin class $UpdateEthccTopicsRequestCopyWith<$Res> {
  factory $UpdateEthccTopicsRequestCopyWith(
    UpdateEthccTopicsRequest value,
    $Res Function(UpdateEthccTopicsRequest) _then,
  ) = _$UpdateEthccTopicsRequestCopyWithImpl;
  @useResult
  $Res call({@JsonKey(name: 'topics') String topics});
}

/// @nodoc
class _$UpdateEthccTopicsRequestCopyWithImpl<$Res>
    implements $UpdateEthccTopicsRequestCopyWith<$Res> {
  _$UpdateEthccTopicsRequestCopyWithImpl(this._self, this._then);

  final UpdateEthccTopicsRequest _self;
  final $Res Function(UpdateEthccTopicsRequest) _then;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? topics = null}) {
    return _then(
      _self.copyWith(
        topics: null == topics
            ? _self.topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UpdateEthccTopicsRequest implements UpdateEthccTopicsRequest {
  const _UpdateEthccTopicsRequest({
    @JsonKey(name: 'topics') required this.topics,
  });
  factory _UpdateEthccTopicsRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateEthccTopicsRequestFromJson(json);

  @override
  @JsonKey(name: 'topics')
  final String topics;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateEthccTopicsRequestCopyWith<_UpdateEthccTopicsRequest> get copyWith =>
      __$UpdateEthccTopicsRequestCopyWithImpl<_UpdateEthccTopicsRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UpdateEthccTopicsRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateEthccTopicsRequest &&
            (identical(other.topics, topics) || other.topics == topics));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, topics);

  @override
  String toString() {
    return 'UpdateEthccTopicsRequest(topics: $topics)';
  }
}

/// @nodoc
abstract mixin class _$UpdateEthccTopicsRequestCopyWith<$Res>
    implements $UpdateEthccTopicsRequestCopyWith<$Res> {
  factory _$UpdateEthccTopicsRequestCopyWith(
    _UpdateEthccTopicsRequest value,
    $Res Function(_UpdateEthccTopicsRequest) _then,
  ) = __$UpdateEthccTopicsRequestCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(name: 'topics') String topics});
}

/// @nodoc
class __$UpdateEthccTopicsRequestCopyWithImpl<$Res>
    implements _$UpdateEthccTopicsRequestCopyWith<$Res> {
  __$UpdateEthccTopicsRequestCopyWithImpl(this._self, this._then);

  final _UpdateEthccTopicsRequest _self;
  final $Res Function(_UpdateEthccTopicsRequest) _then;

  /// Create a copy of UpdateEthccTopicsRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? topics = null}) {
    return _then(
      _UpdateEthccTopicsRequest(
        topics: null == topics
            ? _self.topics
            : topics // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$UpdateEthccGithubRequest {
  @JsonKey(name: 'githubHandle')
  String get githubHandle;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateEthccGithubRequestCopyWith<UpdateEthccGithubRequest> get copyWith =>
      _$UpdateEthccGithubRequestCopyWithImpl<UpdateEthccGithubRequest>(
        this as UpdateEthccGithubRequest,
        _$identity,
      );

  /// Serializes this UpdateEthccGithubRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateEthccGithubRequest &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, githubHandle);

  @override
  String toString() {
    return 'UpdateEthccGithubRequest(githubHandle: $githubHandle)';
  }
}

/// @nodoc
abstract mixin class $UpdateEthccGithubRequestCopyWith<$Res> {
  factory $UpdateEthccGithubRequestCopyWith(
    UpdateEthccGithubRequest value,
    $Res Function(UpdateEthccGithubRequest) _then,
  ) = _$UpdateEthccGithubRequestCopyWithImpl;
  @useResult
  $Res call({@JsonKey(name: 'githubHandle') String githubHandle});
}

/// @nodoc
class _$UpdateEthccGithubRequestCopyWithImpl<$Res>
    implements $UpdateEthccGithubRequestCopyWith<$Res> {
  _$UpdateEthccGithubRequestCopyWithImpl(this._self, this._then);

  final UpdateEthccGithubRequest _self;
  final $Res Function(UpdateEthccGithubRequest) _then;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? githubHandle = null}) {
    return _then(
      _self.copyWith(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _UpdateEthccGithubRequest implements UpdateEthccGithubRequest {
  const _UpdateEthccGithubRequest({
    @JsonKey(name: 'githubHandle') required this.githubHandle,
  });
  factory _UpdateEthccGithubRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateEthccGithubRequestFromJson(json);

  @override
  @JsonKey(name: 'githubHandle')
  final String githubHandle;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateEthccGithubRequestCopyWith<_UpdateEthccGithubRequest> get copyWith =>
      __$UpdateEthccGithubRequestCopyWithImpl<_UpdateEthccGithubRequest>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$UpdateEthccGithubRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateEthccGithubRequest &&
            (identical(other.githubHandle, githubHandle) ||
                other.githubHandle == githubHandle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, githubHandle);

  @override
  String toString() {
    return 'UpdateEthccGithubRequest(githubHandle: $githubHandle)';
  }
}

/// @nodoc
abstract mixin class _$UpdateEthccGithubRequestCopyWith<$Res>
    implements $UpdateEthccGithubRequestCopyWith<$Res> {
  factory _$UpdateEthccGithubRequestCopyWith(
    _UpdateEthccGithubRequest value,
    $Res Function(_UpdateEthccGithubRequest) _then,
  ) = __$UpdateEthccGithubRequestCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(name: 'githubHandle') String githubHandle});
}

/// @nodoc
class __$UpdateEthccGithubRequestCopyWithImpl<$Res>
    implements _$UpdateEthccGithubRequestCopyWith<$Res> {
  __$UpdateEthccGithubRequestCopyWithImpl(this._self, this._then);

  final _UpdateEthccGithubRequest _self;
  final $Res Function(_UpdateEthccGithubRequest) _then;

  /// Create a copy of UpdateEthccGithubRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? githubHandle = null}) {
    return _then(
      _UpdateEthccGithubRequest(
        githubHandle: null == githubHandle
            ? _self.githubHandle
            : githubHandle // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}
