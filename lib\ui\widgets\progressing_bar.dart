import 'dart:async';
import 'dart:math' as math;
import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

final _key = GlobalKey<ProgressingBarState>();

ProgressingBarState get progressingBarState => _key.currentState!;

typedef ProgressingBarAnimationBuilder =
    Widget Function(
      AnimationController controller,
    );

class ProgressingBar extends StatefulWidget {
  const ProgressingBar._({
    super.key,
    required this.hashTag,
    this.initialAnimating = true,
    this.initialHeight = 60.0,
    this.initialChild,
    this.initialChildAnimationBuilder,
  }) : assert(
         initialChild == null && initialChildAnimationBuilder != null ||
             initialChild != null && initialChildAnimationBuilder == null,
       );

  final String hashTag;
  final bool initialAnimating;
  final double initialHeight;
  final Widget? initialChild;
  final ProgressingBarAnimationBuilder? initialChildAnimationBuilder;

  static Future<T?> show<T>({
    bool animating = true,
    double height = 60.0,
    Widget? child,
    ProgressingBarAnimationBuilder? childAnimationBuilder,
  }) {
    final hashTag = Object.hash(child, animating, height).toRadixString(16);
    final widget = ProgressingBar._(
      key: _key,
      hashTag: hashTag,
      initialAnimating: animating,
      initialHeight: height,
      initialChild: child,
      initialChildAnimationBuilder: childAnimationBuilder,
    );
    return SmartDialog.show(
      backType: SmartBackType.block,
      maskColor: Colors.transparent,
      clickMaskDismiss: false,
      usePenetrate: true,
      alignment: const Alignment(0.0, 0.8),
      tag: hashTag,
      builder: (_) => widget,
    );
  }

  static Future<void> run(
    Future<void> Function() callback, {
    required String ongoingText,
    required String succeedText,
    required String failedText,
    FutureOr<void> Function()? onSucceed,
    FutureOr<void> Function()? onFailed,
  }) async {
    ProgressingBar.show(
      childAnimationBuilder: (controller) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
        child: Row(
          spacing: 16.0,
          children: [
            Transform.rotate(
              angle: controller.value * math.pi * 2,
              child: Assets.icons.iconProgressAnimating.svg(),
            ),
            Expanded(child: Text(ongoingText)),
          ],
        ),
      ),
    );
    try {
      await callback();
      progressingBarState.update(
        animating: false,
        childAnimationBuilder: (controller) => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
          child: Row(
            spacing: 16.0,
            children: [
              Assets.icons.iconProgressAnimating.svg(),
              Expanded(child: Text(succeedText)),
            ],
          ),
        ),
      );
      await onSucceed?.call();
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      progressingBarState.update(
        animating: false,
        childAnimationBuilder: (controller) => Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
          child: Row(
            spacing: 16.0,
            children: [
              Assets.icons.iconProgressAnimating.svg(),
              Expanded(child: Text(failedText)),
            ],
          ),
        ),
      );
      await onFailed?.call();
    } finally {
      await Future.delayed(const Duration(seconds: 2));
      progressingBarState.dismiss();
    }
  }

  @override
  State<ProgressingBar> createState() => ProgressingBarState();
}

class ProgressingBarState extends State<ProgressingBar> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _animation;

  late bool animating = widget.initialAnimating;
  late double height = widget.initialHeight;
  late Widget child;

  void update({
    bool? animating,
    double? height,
    Widget? child,
    Widget Function(AnimationController controller)? childAnimationBuilder,
  }) {
    if (animating != null) {
      this.animating = animating;
    }
    if (height != null) {
      this.height = height;
    }
    if (childAnimationBuilder != null) {
      this.child = AnimatedBuilder(
        animation: _animation,
        builder: (context, _) => childAnimationBuilder(_controller),
      );
    } else if (child != null) {
      this.child = child;
    }
    setState(() {});
  }

  void dismiss() {
    SmartDialog.dismiss(tag: widget.hashTag);
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    )..repeat();
    _animation = Tween(begin: -2.0, end: 2.0).animate(_controller);
    child =
        widget.initialChild ??
        AnimatedBuilder(
          animation: _animation,
          builder: (context, _) => widget.initialChildAnimationBuilder!(_controller),
        );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const color = ColorName.themeColorDark;
    return AnimatedContainer(
      height: height,
      duration: kThemeAnimationDuration,
      color: color,
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) => Stack(
          fit: StackFit.expand,
          children: [
            RepaintBoundary(
              child: AnimatedOpacity(
                duration: kThemeAnimationDuration,
                opacity: animating ? 1.0 : 0.0,
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [color, Colors.grey[500]!],
                      begin: AlignmentDirectional(_animation.value - 1, 0),
                      end: AlignmentDirectional(_animation.value + 1, 0),
                      tileMode: TileMode.mirror,
                    ),
                  ),
                ),
              ),
            ),
            child!,
          ],
        ),
        child: DefaultTextStyle.merge(
          style: const TextStyle(
            color: Colors.black,
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
            height: 1.0,
          ),
          child: child,
        ),
      ),
    );
  }
}
