// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'astrox_gw.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$AstroxTicker {
  @JsonKey(name: 'price')
  double get price;
  @JsonKey(name: 'symbol')
  String get symbol;
  @JsonKey(name: 'timestamp')
  int get timestamp;

  /// Create a copy of AstroxTicker
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstroxTickerCopyWith<AstroxTicker> get copyWith =>
      _$AstroxTickerCopyWithImpl<AstroxTicker>(
        this as AstroxTicker,
        _$identity,
      );

  /// Serializes this AstroxTicker to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstroxTicker &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, price, symbol, timestamp);

  @override
  String toString() {
    return 'AstroxTicker(price: $price, symbol: $symbol, timestamp: $timestamp)';
  }
}

/// @nodoc
abstract mixin class $AstroxTickerCopyWith<$Res> {
  factory $AstroxTickerCopyWith(
    AstroxTicker value,
    $Res Function(AstroxTicker) _then,
  ) = _$AstroxTickerCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'price') double price,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'timestamp') int timestamp,
  });
}

/// @nodoc
class _$AstroxTickerCopyWithImpl<$Res> implements $AstroxTickerCopyWith<$Res> {
  _$AstroxTickerCopyWithImpl(this._self, this._then);

  final AstroxTicker _self;
  final $Res Function(AstroxTicker) _then;

  /// Create a copy of AstroxTicker
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? price = null,
    Object? symbol = null,
    Object? timestamp = null,
  }) {
    return _then(
      _self.copyWith(
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _self.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _AstroxTicker implements AstroxTicker {
  const _AstroxTicker({
    @JsonKey(name: 'price') this.price = 0,
    @JsonKey(name: 'symbol') required this.symbol,
    @JsonKey(name: 'timestamp') required this.timestamp,
  });
  factory _AstroxTicker.fromJson(Map<String, dynamic> json) =>
      _$AstroxTickerFromJson(json);

  @override
  @JsonKey(name: 'price')
  final double price;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;
  @override
  @JsonKey(name: 'timestamp')
  final int timestamp;

  /// Create a copy of AstroxTicker
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstroxTickerCopyWith<_AstroxTicker> get copyWith =>
      __$AstroxTickerCopyWithImpl<_AstroxTicker>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AstroxTickerToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstroxTicker &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, price, symbol, timestamp);

  @override
  String toString() {
    return 'AstroxTicker(price: $price, symbol: $symbol, timestamp: $timestamp)';
  }
}

/// @nodoc
abstract mixin class _$AstroxTickerCopyWith<$Res>
    implements $AstroxTickerCopyWith<$Res> {
  factory _$AstroxTickerCopyWith(
    _AstroxTicker value,
    $Res Function(_AstroxTicker) _then,
  ) = __$AstroxTickerCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'price') double price,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'timestamp') int timestamp,
  });
}

/// @nodoc
class __$AstroxTickerCopyWithImpl<$Res>
    implements _$AstroxTickerCopyWith<$Res> {
  __$AstroxTickerCopyWithImpl(this._self, this._then);

  final _AstroxTicker _self;
  final $Res Function(_AstroxTicker) _then;

  /// Create a copy of AstroxTicker
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? price = null,
    Object? symbol = null,
    Object? timestamp = null,
  }) {
    return _then(
      _AstroxTicker(
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        timestamp: null == timestamp
            ? _self.timestamp
            : timestamp // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}
