{
  // VSCode Tasks - equivalent to IntelliJ IDEA build configurations
  // Use Ctrl+Shift+P -> "Tasks: Run Task" to execute these
  "version": "2.0.0",
  "tasks": [
    // Configuration Tasks
    {
      "label": "[config] dev",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=dev", "--env-encoder=cbor", "--release-config-only"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [],
      "options": {
        "statusbar": {
          "label": "$(gear) Config DEV"
        }
      }
    },
    {
      "label": "[config] prod",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=prod", "--env-encoder=cbor", "--release-config-only"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": [],
      "options": {
        "statusbar": {
          "label": "$(gear) Config PROD"
        }
      }
    },
    // APK Build Tasks
    {
      "label": "build-dev-apk",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=dev", "--env-encoder=cbor", "--dist=apk"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "build-prod-apk",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=prod", "--env-encoder=cbor", "--dist=apk"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // IPA Build Tasks
    {
      "label": "build-dev-ipa",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=dev", "--env-encoder=cbor", "--dist=ipa"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "build-prod-ipa",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=prod", "--env-encoder=cbor", "--dist=ipa"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // Combined Build Tasks
    {
      "label": "build-dev-ipa-apk",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=dev", "--env-encoder=cbor", "--dist=ipa,apk"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // Sealed/Production Build Tasks
    {
      "label": "build-sealed-all",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=sealed", "--env-encoder=cbor"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "build-sealed-apk",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=sealed", "--env-encoder=cbor", "--dist=apk"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "build-sealed-ipa",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=sealed", "--env-encoder=cbor", "--dist=ipa"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "build-sealed-bundle",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=sealed", "--env-encoder=cbor", "--dist=bundle"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    {
      "label": "build-sealed-android",
      "type": "shell",
      "command": "astrox_build",
      "args": ["--env=sealed", "--env-encoder=cbor", "--platform=android"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    },
    // Package Management Tasks
    {
      "label": "upgrade-me-packages",
      "type": "shell",
      "command": "flutter",
      "args": ["pub", "upgrade"],
      "group": "build",
      "presentation": {
        "echo": true,
        "reveal": "always",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": true,
        "clear": false
      },
      "problemMatcher": []
    }
  ]
}
