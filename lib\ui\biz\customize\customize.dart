import 'dart:convert';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

import '/models/card.dart';
import '/models/user.dart' show UserInfo;
import '/provider/api.dart';
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;
import '/ui/widgets/social/profile/avatar_img.dart';
import 'metal.dart';
import 'normal.dart';

enum CustomizeViewType { start, main, confirm, pay, submitted }

/// 卡片定制页面
@FFRoute(name: '/customize')
class CustomizePage extends ConsumerStatefulWidget {
  const CustomizePage({
    super.key,
    this.code,
  });

  final String? code;

  @override
  ConsumerState<CustomizePage> createState() => _CustomizePageState();
}

class _CustomizePageState extends ConsumerState<CustomizePage> {
  // 表单控制器
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _titleController = TextEditingController();
  final _companyController = TextEditingController();

  // 状态变量
  CustomizeViewType _viewType = CustomizeViewType.main;
  String? _selectedImageBase64;
  bool _isLoading = false;
  bool _pageLoading = true;
  bool _isMetalType = false;
  CoverInfo? _coverInfo;
  CreateCardCoverResponse? _createCardCoverResponse;

  // AvatarImgPicker组件的Key (仅用于normal类型)
  final GlobalKey<AvatarImgPickerState> _avatarPickerKey = GlobalKey<AvatarImgPickerState>();

  @override
  void initState() {
    super.initState();
    _viewType = CustomizeViewType.main;
    _initializeData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _titleController.dispose();
    _companyController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() {
      _pageLoading = true;
    });

    try {
      // 并行初始化用户数据和封面信息
      await Future.wait([
        _initializeUserData(),
        _initializeCoverInfo(),
      ]);
    } finally {
      setState(() {
        _pageLoading = false;
      });
    }
  }

  Future<void> _initializeUserData() async {
    UserInfo? userInfo;
    try {
      userInfo = await ref.read(fetchUserInfoProvider().future);
    } catch (e) {
      LogUtil.d('从网络获取用户信息失败: $e');
      // 如果网络请求失败，尝试使用本地存储的用户信息
      final localUserInfo = ref.read(userRepoProvider);
      if (localUserInfo != null) {
        userInfo = localUserInfo;
        LogUtil.d('使用本地缓存的用户信息');
      } else {
        LogUtil.d('本地也没有用户信息缓存');
      }
    }

    // 应用用户信息到表单
    if (userInfo != null) {
      _nameController.text = userInfo.name;
      _titleController.text = userInfo.title;
      _companyController.text = userInfo.company;
      // 只有非Metal类型才需要头像
      if (userInfo.avatar.isNotEmpty && !_isMetalType) {
        _selectedImageBase64 = userInfo.avatar;
      }
      LogUtil.d('用户信息初始化成功: ${userInfo.name}');
    } else {
      LogUtil.d('无法获取用户信息，使用空表单');
    }
  }

  Future<void> _initializeCoverInfo() async {
    if (widget.code == null) {
      return;
    }
    try {
      final coverInfo = await ref.read(apiServiceProvider).getCoverInfo(code: widget.code ?? '');

      if (coverInfo.price != 0) {
        setState(() {
          _viewType = CustomizeViewType.start;
        });
      }

      setState(() {
        _coverInfo = coverInfo;
        _isMetalType = coverInfo.printType == PrintType.METAL;
      });

      LogUtil.d('封面信息初始化成功: ${coverInfo.printType}');
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToGetCoverInfo);
      meNavigator.pop();
      rethrow;
    }
  }

  void _handleImageSelected(String base64Image, Uint8List imageBytes) {
    setState(() {
      _selectedImageBase64 = base64Image;
    });
  }

  void _removeImage() {
    setState(() {
      _selectedImageBase64 = null;
    });
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    if (value.length > 30) {
      return 'Name length should be less than 30';
    }
    return null;
  }

  String? _validateTitle(String? value) {
    if (value != null && value.length > 30) {
      return 'Title length should be less than 30';
    }
    return null;
  }

  String? _validateCompany(String? value) {
    if (value != null && value.length > 30) {
      return 'Company length should be less than 30';
    }
    return null;
  }

  double _calculateFontSize(String text) {
    if (text.isEmpty) {
      return 16.0;
    }
    if (text.length <= 10) {
      return 16.0;
    }
    if (text.length <= 15) {
      return 14.0;
    }
    if (text.length <= 20) {
      return 14.0;
    }
    return 12.0;
  }

  Future<void> _handleNext() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // 只有非Metal类型才需要检查图片
    if (!_isMetalType && _selectedImageBase64 == null) {
      Card3ToastUtil.showToast(message: ToastMessages.pleaseUploadYourPicture);
      return;
    }

    setState(() {
      _viewType = CustomizeViewType.confirm;
    });
  }

  // 添加方法来处理图片转base64
  Future<String?> _processImageToBase64(String? imageData) async {
    if (imageData == null || imageData.isEmpty) {
      return null;
    }

    // 检查是否已经是base64格式
    if (imageData.startsWith('data:image/')) {
      return imageData;
    }

    try {
      // 当作远程图片URL处理
      final response = await http.get(Uri.parse(imageData));
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;

        // 根据响应头或者URL判断图片类型
        String mimeType = 'image/jpeg'; // 默认类型
        final contentType = response.headers['content-type'];
        if (contentType != null) {
          mimeType = contentType;
        } else {
          // 根据URL扩展名判断
          if (imageData.toLowerCase().contains('.png')) {
            mimeType = 'image/png';
          } else if (imageData.toLowerCase().contains('.gif')) {
            mimeType = 'image/gif';
          } else if (imageData.toLowerCase().contains('.webp')) {
            mimeType = 'image/webp';
          }
        }

        final base64String = base64Encode(bytes);
        return 'data:$mimeType;base64,$base64String';
      } else {
        LogUtil.e('Failed to load image from URL: ${response.statusCode}');
        return null;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return null;
    }
  }

  Future<void> _handleConfirm() async {
    setState(() {
      _isLoading = true;
    });

    try {
      String? fileContent = '';

      // 如果不是Metal类型，需要处理图片
      if (!_isMetalType) {
        fileContent = await _processImageToBase64(_selectedImageBase64);
        if (fileContent == null && _selectedImageBase64 != null) {
          Card3ToastUtil.showToast(message: ToastMessages.failedToProcessImage);
          return;
        }
      }

      final res = await ref
          .read(apiServiceProvider)
          .createCardCover(
            code: widget.code ?? '',
            username: _nameController.text,
            fileContent: fileContent ?? '',
            title: _titleController.text,
            company: _companyController.text,
          );
      setState(() {
        _createCardCoverResponse = res;
      });

      if (res.paymentLink.isNotEmpty && _coverInfo?.price != 0) {
        setState(() {
          _viewType = CustomizeViewType.pay;
        });
      } else {
        setState(() {
          _viewType = CustomizeViewType.submitted;
        });
      }
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToCreateCardCover);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _handleBack() {
    if (_viewType == CustomizeViewType.main) {
      meNavigator.pop();
    } else if (_viewType == CustomizeViewType.pay) {
      setState(() {
        _viewType = CustomizeViewType.confirm;
      });
    } else {
      setState(() {
        _viewType = CustomizeViewType.main;
      });
    }
  }

  Map<String, dynamic>? _parseThirdPartyLink() {
    if (_coverInfo?.thirdPartyLink.isEmpty ?? true) {
      return null;
    }

    return jsonDecode(_coverInfo!.thirdPartyLink) as Map<String, dynamic>;
  }

  // 解析价格单位信息 (格式: "€/30/EURO")
  Map<String, String> _parsePriceUnit(String? priceUnit) {
    if (priceUnit == null || priceUnit.isEmpty) {
      return {'symbol': '€', 'amount': '30', 'currency': 'EURO'};
    }

    final parts = priceUnit.split('/');
    if (parts.length >= 3) {
      return {
        'symbol': parts[0],
        'amount': parts[1],
        'currency': parts[2],
      };
    }

    return {'symbol': '€', 'amount': '30', 'currency': 'EURO'};
  }

  // 解析运费单位信息 (格式: "€/0/EURO")
  Map<String, String> _parseShippingUnit(String? shippingUnit) {
    if (shippingUnit == null || shippingUnit.isEmpty) {
      return {'symbol': '€', 'amount': '0', 'currency': 'EURO'};
    }

    final parts = shippingUnit.split('/');
    if (parts.length >= 3) {
      return {
        'symbol': parts[0],
        'amount': parts[1],
        'currency': parts[2],
      };
    }

    return {'symbol': '€', 'amount': '0', 'currency': 'EURO'};
  }

  // 显示支付完成底部弹窗
  void _showPaymentCompleteBottomSheet() {
    launchUrl(Uri.parse(_createCardCoverResponse?.paymentLink ?? ''));
    showModalBottomSheet(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.4,
      builder: (context) => Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Complete Payment',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.close,
                    color: Colors.black54,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      // 支付链接容器
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: const Color(0xFFF5F5F5),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: const Color(0xFFE0E0E0)),
                          ),
                          child: Text(
                            _createCardCoverResponse?.paymentLink ?? '',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // 复制按钮
                      GestureDetector(
                        onTap: () async {
                          final paymentLink = _createCardCoverResponse?.paymentLink ?? '';
                          if (paymentLink.isNotEmpty) {
                            await Clipboard.setData(ClipboardData(text: paymentLink));
                            Card3ToastUtil.showToast(message: ToastMessages.copied);
                          }
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: ColorName.themeColorDark,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.copy,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Please copy and paste this link into your browser to complete the payment.',
                    style: TextStyle(fontSize: 14, color: Colors.black87),
                  ),
                  const Spacer(),
                ],
              ),
            ),
          ),

          // 底部按钮
          Padding(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  setState(() {
                    _viewType = CustomizeViewType.submitted;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorName.themeColorDark,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'I\'ve completed the payment',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_pageLoading) {
      return const Scaffold(
        backgroundColor: Color(0xFFf1f1f1),
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    switch (_viewType) {
      case CustomizeViewType.start:
        return _buildStartView();
      case CustomizeViewType.confirm:
        return _buildConfirmViewWrapper();
      case CustomizeViewType.pay:
        return _buildPayView();
      case CustomizeViewType.submitted:
        return _buildSubmittedView();
      default:
        return _buildMainViewWrapper();
    }
  }

  Widget _buildMainViewWrapper() {
    if (_isMetalType) {
      return MetalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: false,
      );
    } else {
      return NormalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        selectedImageBase64: _selectedImageBase64,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        avatarPickerKey: _avatarPickerKey,
        onImageSelected: _handleImageSelected,
        onRemoveImage: _removeImage,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: false,
      );
    }
  }

  Widget _buildConfirmViewWrapper() {
    if (_isMetalType) {
      return MetalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: true,
      );
    } else {
      return NormalCustomizeView(
        formKey: _formKey,
        nameController: _nameController,
        titleController: _titleController,
        companyController: _companyController,
        selectedImageBase64: _selectedImageBase64,
        coverInfo: _coverInfo,
        isLoading: _isLoading,
        avatarPickerKey: _avatarPickerKey,
        onImageSelected: _handleImageSelected,
        onRemoveImage: _removeImage,
        onNext: _handleNext,
        onConfirm: _handleConfirm,
        onBack: _handleBack,
        validateName: _validateName,
        validateTitle: _validateTitle,
        validateCompany: _validateCompany,
        calculateFontSize: _calculateFontSize,
        isConfirmView: true,
      );
    }
  }

  Widget _buildStartView() {
    return Scaffold(
      backgroundColor: const Color(0xFF0B0B0F),
      body: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                // 背景图片
                Positioned.fill(
                  top: -300,
                  child: Transform.rotate(
                    angle: 0.2,
                    child: MEImage(
                      Assets.icons.images.banner2.path,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                // 深色遮罩
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withValues(alpha: 0.4),
                          Colors.black.withValues(alpha: 0.7),
                        ],
                      ),
                    ),
                  ),
                ),
                // 文字内容
                Positioned(
                  bottom: 240,
                  left: 24,
                  right: 24,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: const TextSpan(
                          style: TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            height: 1.2,
                          ),
                          children: [
                            TextSpan(text: 'Customize\nyour '),
                            TextSpan(
                              text: 'Card3',
                              style: TextStyle(color: ColorName.themeColorDark),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'with NFT PFPs, portraits & any images!',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),

                Positioned.fill(
                  bottom: 40,
                  left: 24,
                  right: 24,
                  child: Column(
                    children: [
                      const Spacer(),
                      Text(
                        _coverInfo?.priceDescription.isNotEmpty == true
                            ? _coverInfo!.priceDescription
                            : '€30.00 per card',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 20),
                      SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _viewType = CustomizeViewType.main;
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorName.themeColorDark,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0,
                          ),
                          child: const Text(
                            'Customize Now',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 底部按钮区域
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmittedView() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 显示相应类型的卡片预览
                  const Icon(
                    Icons.check_circle,
                    color: Color(0xFF3EDD40),
                    size: 100,
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    'Submitted',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 48),
                  const Text(
                    'Your printing code:',
                    style: TextStyle(
                      fontSize: 24,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _createCardCoverResponse?.code ?? '',
                    style: const TextStyle(
                      fontSize: 50,
                      fontWeight: FontWeight.bold,
                      color: ColorName.themeColorDark,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16).copyWith(bottom: 40),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {
                  meNavigator.pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorName.themeColorDark,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: const Text(
                  'Done',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayView() {
    // 解析thirdPartyLink获取价格和运费信息
    final thirdPartyData = _parseThirdPartyLink();
    final priceInfo = _parsePriceUnit(thirdPartyData?['priceUnit']);
    final shippingInfo = _parseShippingUnit(thirdPartyData?['shippingUnit']);

    // 计算总价
    final subtotalAmount = double.tryParse(priceInfo['amount'] ?? '30') ?? 30.0;
    final shippingAmount = double.tryParse(shippingInfo['amount'] ?? '0') ?? 0.0;
    final totalAmount = subtotalAmount + shippingAmount;

    return AppScaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 卡片预览区域
                  Container(
                    height: 220,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: _isMetalType ? _buildMetalCardPreview() : _buildNormalCardPreview(),
                    ),
                  ),

                  const SizedBox(height: 40),

                  // 账单信息
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Bill',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Subtotal
                        _buildBillRow(
                          'Subtotal',
                          '${priceInfo['symbol']} ${priceInfo['amount']} ${priceInfo['currency']}',
                        ),
                        const SizedBox(height: 16),

                        // Shipping Fee
                        _buildBillRow(
                          'Shipping Fee',
                          '${shippingInfo['symbol']} ${shippingInfo['amount']} ${shippingInfo['currency']}',
                        ),
                        const SizedBox(height: 24),

                        // 分割线
                        Container(
                          height: 1,
                          color: const Color(0xFFE0E0E0),
                        ),
                        const SizedBox(height: 24),

                        // Total
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Total',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            Text(
                              '${priceInfo['symbol']} ${totalAmount.toInt()} ${priceInfo['currency']}',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          // 底部支付按钮
          Container(
            padding: const EdgeInsets.only(bottom: 40),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: _showPaymentCompleteBottomSheet,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorName.themeColorDark,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  'Proceed to checkout',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 18,
            color: Colors.black87,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  Widget _buildMetalCardPreview() {
    return Container(
      width: 280,
      height: 180,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Stack(
        children: [
          // 使用 metalFrontcover 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Assets.icons.images.metalFrontcover.svg(
                fit: BoxFit.contain,
              ),
            ),
          ),
          // 文字内容覆盖在背景上
          Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _nameController.text.isEmpty ? 'Your name' : _nameController.text,
                  style: TextStyle(
                    fontSize: _calculateFontSize(_nameController.text) * 1.3,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (_titleController.text.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    _titleController.text,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
                if (_companyController.text.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    _companyController.text,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNormalCardPreview() {
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 使用 normalFrontcover 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Assets.icons.images.normalFrontcover.image(
                fit: BoxFit.cover,
              ),
            ),
          ),
          // 头像区域
          Column(
            children: [
              Container(
                width: 140,
                height: 140,
                decoration: const BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  child: _selectedImageBase64 != null
                      ? (_selectedImageBase64!.startsWith('data:image')
                            ? _buildBase64Image(_selectedImageBase64!, width: 140, height: 140)
                            : MEImage(
                                _selectedImageBase64!,
                                fit: BoxFit.cover,
                                clipOval: false,
                                alternativeSVG: true,
                              ))
                      : const Icon(
                          Icons.add,
                          size: 56,
                          color: ColorName.themeColorDark,
                        ),
                ),
              ),
              // 文字内容
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _nameController.text.isEmpty ? 'Your name' : _nameController.text,
                          style: TextStyle(
                            fontSize: _calculateFontSize(_nameController.text),
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (_titleController.text.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            _titleController.text,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        if (_companyController.text.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            _companyController.text,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBase64Image(String base64String, {required double width, required double height}) {
    try {
      final imageData = base64String.split(',')[1];
      final bytes = base64Decode(imageData);
      return Image.memory(
        bytes,
        width: width,
        height: height,
        fit: BoxFit.cover,
      );
    } catch (e) {
      LogUtil.e('Failed to decode base64 image: $e');
      return Container(
        width: width,
        height: height,
        color: Colors.grey[300],
        child: const Icon(Icons.error, color: Colors.grey),
      );
    }
  }
}
