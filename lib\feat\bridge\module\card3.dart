import 'dart:async';
import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart' show InAppWebViewController;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '/feat/bridge/rpc_call.dart';
import '/internals/box.dart' show BoxService;
import '/models/user.dart' show UserInfo;

const WebViewBridge defaultCard3Bridge = DefaultWebViewBridge(
  method: 'bridge',
  subMethods: <JsonRPCMethod>{
    _XAuthorized(),
    _GetToken(),
    _GetUserInfo(),
  },
  adapter: DefaultJsonRPCAdapter(),
);

class _XAuthorized extends JsonRPCMethod {
  const _XAuthorized() : super('x_authorized');

  @override
  Future<void> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    final token = request.params.first as String;
    await BoxService.updateToken(token);
  }
}

class _GetToken extends JsonRPCMethod {
  const _GetToken() : super('get_token');

  @override
  Future<String> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    return BoxService.getToken() ?? '';
  }
}

class _GetUserInfo extends JsonRPCMethod {
  const _GetUserInfo() : super('get_user_info');

  @override
  Future<String> call(
    BuildContext context,
    WidgetRef ref,
    WebViewBridge bridge,
    InAppWebViewController controller,
    JsonRPCRequest request,
  ) async {
    try {
      final userInfo = BoxService.getUserInfo();
      if (userInfo == null) {
        const defaultUserInfo = UserInfo();
        final jsonString = jsonEncode(defaultUserInfo.toJson());
        return jsonString;
      }
      final jsonString = jsonEncode(userInfo.toJson());
      return jsonString;
    } catch (e) {
      const defaultUserInfo = UserInfo();
      final jsonString = jsonEncode(defaultUserInfo.toJson());
      return jsonString;
    }
  }
}
