import 'package:card3/exports.dart';

import '/internals/box.dart';
import '/models/business.dart';
import '/provider/business.dart';
import '/provider/chain.dart';

// Token缓存键前缀
const String _tokenCacheKeyPrefix = 'tokens_cache:';
// 缓存有效期 - 1天（毫秒）
const int _tokenCacheExpiration = 24 * 60 * 60 * 1000; // 1天

// 价格缓存键前缀
const String _priceCacheKeyPrefix = 'token_price_cache:';
// 价格缓存有效期 - 30分钟（毫秒）
const int _priceCacheExpiration = 30 * 60 * 1000; // 30分钟

// 过滤后的token列表Provider - 改为autoDispose
final filteredTokensProvider = FutureProvider.autoDispose<List<Token>>((ref) async {
  final currentNetwork = ref.watch(currentNetworkProvider);
  final config = ref.watch(configProvider);

  if (currentNetwork == null) {
    return [];
  }

  final chainId = currentNetwork.id.toString();

  final chainTokenFilters = config.chainTokenFilters;
  // 缓存键
  final cacheKey = '$_tokenCacheKeyPrefix$chainId';

  // 尝试从缓存中获取数据
  final cachedData = Boxes.cache.get(cacheKey);

  if (cachedData != null) {
    try {
      // 检查缓存是否有效
      final Map<String, dynamic> cacheMap = Map<String, dynamic>.from(cachedData);
      final int timestamp = cacheMap['timestamp'] ?? 0;
      final List<dynamic> cachedTokens = cacheMap['tokens'] ?? [];

      // 如果缓存未过期且有数据
      if (DateTime.now().millisecondsSinceEpoch - timestamp < _tokenCacheExpiration && cachedTokens.isNotEmpty) {

        // 将缓存的JSON数据转换为Token对象
        final List<Token> tokens = cachedTokens
            .map((tokenData) => Token.fromJson(Map<String, dynamic>.from(tokenData)))
            .toList();

        // 过滤并返回缓存的token
        return _filterTokensByChainId(tokens, chainId, chainTokenFilters);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
  }

  // 从API获取数据
  final tokensResponse = await ref.watch(
    fetchTokensProvider(
      pageNum: 1,
      pageSize: 10000,
      blockChain: currentNetwork.network,
    ).future,
  );

  final tokens = tokensResponse.list;

  // 缓存获取的数据
  _cacheTokens(tokens, chainId);

  // 过滤并返回token
  return _filterTokensByChainId(tokens, chainId, chainTokenFilters);
});

// 过滤token的辅助函数
List<Token> _filterTokensByChainId(List<Token> tokens, String chainId, Map<String, List<String>>? chainTokenFilters) {
  List<Token> filteredTokens;

  // 如果chainTokenFilters中没有该链的配置，返回所有token
  if (chainTokenFilters == null || !chainTokenFilters.containsKey(chainId)) {
    filteredTokens = List<Token>.from(tokens);
  } else {
    // 根据配置过滤token，并确保结果是一个新的可修改列表
    final allowedSymbols = chainTokenFilters[chainId]!;
    filteredTokens = tokens
        .where((token) => allowedSymbols.contains(token.symbol))
        .toList(); // 这已经创建了一个新的列表，但为了安全起见，我们在排序前再次确认它是可修改的
  }

  // 确保最终返回的结果按weight从低到高排序
  // 创建可修改列表的副本，再进行排序
  final List<Token> sortableList = List<Token>.from(filteredTokens);
  sortableList.sort((a, b) => a.weight.compareTo(b.weight));

  return sortableList;
}

// 缓存token的辅助函数
void _cacheTokens(List<Token> tokens, String chainId) {
  try {
    // 缓存键
    final cacheKey = '$_tokenCacheKeyPrefix$chainId';

    // 准备缓存数据
    final cacheData = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'tokens': tokens.map((token) => token.toJson()).toList(),
    };

    // 存储到缓存
    Boxes.cache.put(cacheKey, cacheData);
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
  }
}

// // Token余额Provider
// final tokenBalanceProvider =
//     FutureProvider.family<Decimal, ({Token token, String address})>(
//         (ref, params) async {
//   final chainManager = ref.watch(chainManagerProvider);
//   final currentNetwork = ref.watch(currentNetworkProvider);

//   if (currentNetwork == null) {
//     return Decimal.zero;
//   }

//   final ownerAddress = params.address;

//   // 判断是否为原生币
//   final isNativeToken = params.token.symbol.toUpperCase() ==
//       currentNetwork.nativeCurrency.symbol.toUpperCase();

//   try {
//     BigInt balance;
//     if (isNativeToken) {
//       // 查询原生币余额
//       balance = await chainManager.getNativeBalance(ownerAddress);
//     } else {
//       // 查询代币余额
//       final tokenAddress = params.token.contractAddress;
//       if (tokenAddress == '') {
//         return Decimal.zero;
//       }
//       balance = await chainManager.getTokenBalance(tokenAddress, ownerAddress);
//     }
//     return Decimal.fromBigInt(balance).shift(-params.token.digits);
//   } catch (e, s) {
//     handleExceptions(error: e, stackTrace: s);
//     return Decimal.zero;
//   }
// });

// 获取代币价格的辅助函数
Future<double> _getTokenPrice(Ref ref, String symbol) async {
  // 缓存键
  final cacheKey = '$_priceCacheKeyPrefix$symbol';

  // 尝试从缓存中获取价格
  final cachedData = Boxes.cache.get(cacheKey);

  if (cachedData != null) {
    try {
      // 检查缓存是否有效
      final Map<String, dynamic> cacheMap = Map<String, dynamic>.from(cachedData);
      final int timestamp = cacheMap['timestamp'] ?? 0;
      final double cachedPrice = cacheMap['price'] ?? 0.0;

      // 如果缓存未过期
      if (DateTime.now().millisecondsSinceEpoch - timestamp < _priceCacheExpiration) {
        return cachedPrice;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      // 缓存格式有误，继续获取新数据
    }
  }

  // 从API获取价格
  try {
    final ticker = await ref.watch(fetchTickersPriceProvider(symbol: symbol).future);
    final price = ticker.price;

    // 缓存价格
    _cacheTokenPrice(symbol, price);

    return price;
  } catch (e, s) {
    handleExceptions(error: e, stackTrace: s);
    return 0.0;
  }
}

// 缓存代币价格的辅助函数
void _cacheTokenPrice(String symbol, double price) {
  // 缓存键
  final cacheKey = '$_priceCacheKeyPrefix$symbol';

  // 准备缓存数据
  final cacheData = {
    'timestamp': DateTime.now().millisecondsSinceEpoch,
    'price': price,
  };
  // 存储到缓存
  Boxes.cache.put(cacheKey, cacheData);
}

// 所有Token余额的Provider - 改为autoDispose
final allTokenBalancesProvider = FutureProvider.autoDispose<List<TokenBalance>>((ref) async {
  // 监听网络切换状态
  final currentNetwork = ref.watch(currentNetworkProvider);
  if (currentNetwork == null) {
    return [];
  }

  // 使用 walletAddressProvider 代替直接从 privyClient 获取地址
  // 这样当地址变化时会自动重新计算
  final walletAddress = ref.watch(walletAddressProvider);
  if (walletAddress?.isEmpty ?? false) {
    return [];
  }

  // 等待 filteredTokens 异步加载完成
  final tokens = await ref.watch(filteredTokensProvider.future);
  final tokenBalances = <TokenBalance>[];

  // 使用批量请求并行获取余额和价格
  final results = await Future.wait(
    tokens.map((token) async {
      try {
        // 获取余额
        BigInt balanceRaw;
        Decimal balance;
        final chainManager = ref.read(chainManagerProvider);

        // 判断是否为原生币
        final isNativeToken = token.symbol.toUpperCase() == currentNetwork.nativeCurrency.symbol.toUpperCase();

        // 直接从链上读取最新余额，绕过Provider缓存
        if (isNativeToken) {
          balanceRaw = await chainManager.getNativeBalance(walletAddress);
        } else {
          balanceRaw = await chainManager.getTokenBalance(token.contractAddress, walletAddress);
        }

        // 转换余额为正确的小数位数
        balance = Decimal.fromBigInt(balanceRaw).shift(-token.digits);

        // 获取价格
        final price = await _getTokenPrice(ref, token.symbol);

        // 计算美元价值
        final usdValue = price != 0 ? (Decimal.parse(price.toString()) * balance).toStringAsFixed(2) : '--';

        return TokenBalance(token: token, balance: balance, price: price, usdValue: usdValue);
      } catch (e, s) {
        // 单个代币余额获取失败不应影响整体列表
        handleExceptions(error: e, stackTrace: s);
        return TokenBalance(token: token, balance: Decimal.zero, price: 0, usdValue: '--');
      }
    }),
  );

  tokenBalances.addAll(results);
  return tokenBalances;
});
