import 'package:flutter/material.dart';
import 'package:me_extensions/me_extensions.dart' show MENumExtension;
import 'package:me_ui/me_ui.dart' show BrightnessLayer;

import 'app_back_button.dart';

class AppScaffold extends StatelessWidget {
  const AppScaffold({
    super.key,
    required this.body,
    this.bodyPadding = const EdgeInsets.symmetric(horizontal: 24.0),
    this.title,
    this.titleStyle,
    this.titleBuilder,
    this.showBottomButtonDivider = false,
    this.bottomButtonBuilder,
    this.backgroundColor,
    this.defaultTextStyle,
  });

  final Widget body;
  final EdgeInsetsGeometry bodyPadding;
  final String? title;
  final TextStyle? titleStyle;
  final WidgetBuilder? titleBuilder;
  final bool showBottomButtonDivider;
  final WidgetBuilder? bottomButtonBuilder;
  final Color? backgroundColor;
  final TextStyle? defaultTextStyle;

  @override
  Widget build(BuildContext context) {
    final ModalRoute<dynamic>? parentRoute = ModalRoute.of(context);
    final impliesLeading = parentRoute?.impliesAppBarDismissal ?? false;
    final estimateBrightness = switch (backgroundColor) {
      final c? => ThemeData.estimateBrightnessForColor(c),
      _ => null,
    };
    final themeBrightness = Theme.of(context).brightness;
    return BrightnessLayer(
      brightness: estimateBrightness,
      child: Scaffold(
        backgroundColor: backgroundColor,
        body: DefaultTextStyle.merge(
          style: defaultTextStyle,
          child: Column(
            children: [
              Material(
                type: MaterialType.transparency,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6.0).copyWith(
                    top: MediaQuery.paddingOf(context).top,
                  ),
                  child: Row(
                    spacing: 4.0,
                    children: [
                      if (impliesLeading) const AppBackButton(),
                      Expanded(
                        child: DefaultTextStyle.merge(
                          style: TextStyle(
                            color: switch (estimateBrightness ?? themeBrightness) {
                              Brightness.dark => Colors.white,
                              Brightness.light => null,
                            },
                            fontSize: 24.0,
                            fontWeight: FontWeight.bold,
                          ).merge(titleStyle),
                          child: switch ((titleBuilder, title)) {
                            (final builder?, _) => builder(context),
                            (_, final title?) => Text(title),
                            _ => const SizedBox.shrink(),
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: MediaQuery.removePadding(
                  context: context,
                  removeTop: true,
                  child: Padding(
                    padding: bodyPadding,
                    child: body,
                  ),
                ),
              ),
              if (bottomButtonBuilder != null) const SizedBox(height: 24.0),
              if (bottomButtonBuilder case final builder?)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24.0).copyWith(
                    bottom: MediaQuery.paddingOf(context).bottom.max(24.0),
                  ),
                  child: builder(context),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
