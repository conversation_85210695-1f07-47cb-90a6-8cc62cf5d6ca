// GENERATED CODE - DO NOT MODIFY MANUALLY
// **************************************************************************
// Auto generated by https://github.com/fluttercandies/ff_annotation_route
// **************************************************************************
// fast mode: true
// version: 11.0.2
// **************************************************************************
// ignore_for_file: duplicate_import,implementation_imports,library_private_types_in_public_api,multiple_combinators,prefer_const_literals_to_create_immutables,unintended_html_in_doc_comment,unnecessary_import,unused_import,unused_local_variable,unused_shown_name,unnecessary_library_name,unnecessary_library_directive
import 'package:card3/models/business.dart';
import 'package:ff_annotation_route_library/ff_annotation_route_library.dart';
import 'package:flutter/widgets.dart';

import '/models/card.dart' show EthccProfile, Social, SocialPlatform;
import '/models/card.dart' show Social, SocialName, SocialPlatform;
import '/models/user.dart' show UserInfo;
import '../ui/biz/customize/customize.dart';
import '../ui/biz/fun/connection.dart';
import '../ui/biz/fun/point_record.dart';
import '../ui/biz/fun/referal.dart';
import '../ui/biz/home.dart';
import '../ui/biz/login.dart';
import '../ui/biz/login/login_with_email.dart';
import '../ui/biz/other/nfc.dart';
import '../ui/biz/other/notification.dart';
import '../ui/biz/other/share.dart';
import '../ui/biz/other/web_view.dart';
import '../ui/biz/setting/about.dart';
import '../ui/biz/setting/account.dart';
import '../ui/biz/setting/index.dart';
import '../ui/biz/setting/mode.dart';
import '../ui/biz/setting/push.dart';
import '../ui/biz/setting/wallets.dart';
import '../ui/biz/social.dart';
import '../ui/biz/social/profile.dart';
import '../ui/biz/wallet/send.dart';
import '../ui/splash.dart';

/// Get route settings base on route name, auto generated by https://github.com/fluttercandies/ff_annotation_route
FFRouteSettings getRouteSettings({
  required String name,
  Map<String, dynamic>? arguments,
  PageBuilder? notFoundPageBuilder,
}) {
  final Map<String, dynamic> safeArguments =
      arguments ?? const <String, dynamic>{};
  switch (name) {
    case '/':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SplashPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/customize':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => CustomizePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          code: asT<String?>(
            safeArguments['code'],
          ),
        ),
      );
    case '/fun/connection':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => ConnectionPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/fun/point_record':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => PointRecord(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/fun/referal':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => Referal(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/home':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => HomePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/login':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => Login(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/login/email':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => LoginWithEmail(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/nfc':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => NfcDemo(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/notification':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => NotificationPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SettingIndex(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting/about':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => AboutPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting/account':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => AccountSecurity(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting/mode':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => ModeSettingPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting/push':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => PushNotificationSettingPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/setting/wallets':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => WalletsPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
        ),
      );
    case '/share':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SharePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          cardCode: asT<String>(
            safeArguments['cardCode'],
          )!,
        ),
      );
    case '/social':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SocialPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          isSingleLink: asT<bool>(
            safeArguments['isSingleLink'],
            false,
          )!,
          action: asT<String?>(
            safeArguments['action'],
          ),
          social: asT<Social?>(
            safeArguments['social'],
          ),
          platform: asT<SocialPlatform?>(
            safeArguments['platform'],
          ),
          currentHandle: asT<String?>(
            safeArguments['currentHandle'],
          ),
        ),
      );
    case '/social/profile':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SocialProfilePage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          code: asT<String>(
            safeArguments['code'],
          )!,
          profile: asT<UserInfo?>(
            safeArguments['profile'],
          ),
        ),
      );
    case '/wallet/send':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => SendPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          tokenBalance: asT<TokenBalance?>(
            safeArguments['tokenBalance'],
          ),
        ),
      );
    case '/webview':
      return FFRouteSettings(
        name: name,
        arguments: arguments,
        builder: () => WebViewPage(
          key: asT<Key?>(
            safeArguments['key'],
          ),
          url: asT<String>(
            safeArguments['url'],
          )!,
          title: asT<String?>(
            safeArguments['title'],
          ),
        ),
      );
    default:
      return FFRouteSettings(
        name: FFRoute.notFoundName,
        routeName: FFRoute.notFoundRouteName,
        builder: notFoundPageBuilder ?? () => Container(),
      );
  }
}
