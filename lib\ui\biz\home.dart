import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '/feat/link/helper.dart';
import '/provider/settings.dart';
import 'home/card.dart';
import 'home/fun.dart';
import 'home/wallet.dart';

final drawerGlobalKeyProvider = Provider<GlobalKey<ScaffoldState>>((ref) {
  return GlobalKey<ScaffoldState>();
});

@FFRoute(name: '/home')
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with WidgetsBindingObserver {
  String? _clipboardText;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        _handleClipboardData();
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _handleClipboardData() async {
    if (!mounted) {
      return;
    }
    if (!ref.read(settingsProvider).autoParseFromClipboard) {
      return;
    }
    final data = await Clipboard.getData('text/plain');
    final text = data?.text?.trim();

    if (text == _clipboardText) {
      return;
    }
    _clipboardText = text;

    if (text == null || text.isEmpty) {
      return;
    }
    if (Uri.tryParse(text) case final uri?) {
      AppLinkHelper.handleUri(uri);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: ref.watch(drawerGlobalKeyProvider),
      body: const _MainBody(),
      bottomNavigationBar: const _BottomNavBar(),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(selectedIndexProvider);
    final children = [
      const Home(),
      const Fun(),
      const Wallet(),
    ];
    return IndexedStack(
      index: index,
      children: children,
    );
  }
}

class _NavItem {
  const _NavItem({
    required this.icon,
    required this.label,
    required this.index,
  });

  final SvgGenImage icon;
  final String label;
  final int index;
}

class _BottomNavBar extends ConsumerWidget {
  const _BottomNavBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selected = ref.watch(selectedIndexProvider);

    // 导航选项配置
    final navs = [
      _NavItem(
        icon: Assets.icons.navCard,
        label: context.l10n.labelNavDiscovery,
        index: 0,
      ),
      _NavItem(
        icon: Assets.icons.navFun,
        label: context.l10n.labelNavSocial,
        index: 1,
      ),
      _NavItem(
        icon: Assets.icons.navWallet,
        label: context.l10n.labelNavWallet,
        index: 2,
      ),
    ];

    final selectedMiddle = selected == 1;
    final bottomPadding = MediaQuery.paddingOf(context).bottom.max(4.0);
    return Material(
      elevation: selectedMiddle ? 5 : 1,
      shadowColor: selectedMiddle ? ColorName.cardColorDark : Colors.black.withValues(alpha: 0.5),
      color: selectedMiddle ? ColorName.cardColorDark : Colors.white,
      child: Container(
        height: 64 + bottomPadding,
        padding: EdgeInsets.only(bottom: bottomPadding),
        decoration: BoxDecoration(
          color: selectedMiddle ? ColorName.cardColorDark : Colors.white,
          boxShadow: [
            BoxShadow(
              color: selectedMiddle ? Colors.black : Colors.black.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: selectedMiddle ? 5 : 4,
              offset: const Offset(0, -1),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // 普通导航按钮 - 移除额外的Padding
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNavItem(context, ref, navs[0], 0, selected),
                const SizedBox(width: 90),
                _buildNavItem(context, ref, navs[2], 2, selected),
              ],
            ),
            // 中间凸起按钮
            Positioned(
              top: -20,
              left: 0,
              right: 0,
              child: Center(
                child: _buildCenterButton(context, ref, navs[1], 1, selected),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建普通导航项
  Widget _buildNavItem(
    BuildContext context,
    WidgetRef ref,
    _NavItem item,
    int index,
    int selected,
  ) {
    final isSelected = index == selected;
    return Tapper(
      onTap: () {
        ref.read(selectedIndexProvider.notifier).state = index;
      },
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 10),
            width: 80,
            height: 48,
            decoration: BoxDecoration(
              color: isSelected ? context.themeColor : Colors.transparent,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: item.icon.svg(
                width: 30,
                height: 30,
                colorFilter: ColorFilter.mode(
                  isSelected ? Colors.white : const Color(0xFF9C9CA4),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          const SizedBox(height: 4),
        ],
      ),
    );
  }

  // 构建中间凸起按钮
  Widget _buildCenterButton(
    BuildContext context,
    WidgetRef ref,
    _NavItem item,
    int index,
    int selected,
  ) {
    final isSelected = index == selected;
    return Tapper(
      onTap: () {
        ref.read(selectedIndexProvider.notifier).state = index;
      },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            width: 90.0,
            height: 90.0,
            decoration: BoxDecoration(
              borderRadius: RadiusConstants.max,
              boxShadow: [
                if (isSelected)
                  BoxShadow(
                    color: Colors.black,
                    spreadRadius: 1,
                    blurRadius: isSelected ? 5 : 4,
                    offset: const Offset(0, -1),
                  ),
              ],
              color: Colors.transparent,
            ),
          ),
          Positioned(
            top: 20,
            left: -5,
            width: 100,
            height: 90,
            child: ColoredBox(
              color: isSelected ? ColorName.cardColorDark : Colors.white,
            ),
          ),
          Container(
            width: 90,
            height: 90,
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: isSelected ? ColorName.cardColorDark : Colors.white,
              borderRadius: BorderRadius.circular(45),
            ),
            child: Container(
              padding: const EdgeInsets.all(10),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: isSelected ? context.meTheme.notificationColor : context.meTheme.captionTextColor,
                borderRadius: BorderRadius.circular(35),
              ),
              child: item.icon.svg(
                width: 40,
                height: 40,
                colorFilter: isSelected ? Colors.black.filter : Colors.white.filter,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
