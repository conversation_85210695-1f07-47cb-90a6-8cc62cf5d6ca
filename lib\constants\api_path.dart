/// API 路径常量
final class OpPath {
  const OpPath._();

  static const multichain = MultichainPath._();
  static const api = ApiPath._();
}

final class MultichainPath {
  const MultichainPath._();

  String get query => '/multichain/networkConfig';
}

final class ApiPath {
  const ApiPath._();

  String get tokenList => '/api/queryTokenList';

  String get chainBrowser => '/api/queryChainBrowserList';

  String get getNftForOwner => '/api/getNFTsForOwner';
}

final class GatewayApi {
  const GatewayApi._();

  String get gas => '/gas/v2/gasEstimate';

  String get prices => '/api/price/latestPrice';
}

final class Card3Api {
  const Card3Api._();

  String get login => '/active/privy/login';

  String delProfile({required String cardId}) => '/active/profile/$cardId';

  String delSocial({required String cardId}) => '/active/profile/$cardId/socials/clear';

  String get walletClear => '/active/wallet/clear';

  final PushPath push = const PushPath._();
  final UserPath user = const UserPath._();
  final WalletPath wallet = const WalletPath._();
  final ProfilePath profile = const ProfilePath._();
  final CheckinPath checkin = const CheckinPath._();
  final CoverPath cover = const CoverPath._();
  final MeetingPath meeting = const MeetingPath._();
  final BlindBoxPath blindBox = const BlindBoxPath._();
  final EthccPath ethcc = const EthccPath._();
  final StoryPath story = const StoryPath._();
  final LuckyDrawPath luckyDraw = const LuckyDrawPath._();
  final TwitterPath twitter = const TwitterPath._();
}

final class PushPath {
  const PushPath._();

  String get subscribe => '/active/push/subscribe';
}

final class UserPath {
  const UserPath._();

  String get info => '/active/user/info';

  String get delete => '/active/user/delete';

  String get settings => '/active/user/setting';

  String get refreshToken => '/active/user/refresh_token';

  String get tokens => '/active/user/tokens';

  String get cards => '/active/user/myCards';

  String get activate => '/active/user/active_card';

  String get referralLogs => '/active/user/myReferralLogs';

  String get points => '/active/user/myIntegralLogs';

  String get sayhiList => '/active/user/listSayHis';

  String get listMessages => '/active/user/listMessages';

  String get uploadAvatar => '/active/user/uploadAvatar';

  String get socialGet => '/active/user/socials';

  String get socialAdd => '/active/user/socials';

  String socialUpdate({required String socialId}) => '/active/user/socials/$socialId';

  String socialDel({required String socialId}) => '/active/user/socials/$socialId';

  String get socialSort => '/active/user/socials_sort';

  String get connections => '/active/user/sayhis';

  String follow({required String code}) => '/active/user/follow?referralCode=$code';

  String unfollow({required String code}) => '/active/user/unfollow?referralCode=$code';

  String relation({required String referralCode}) => '/active/user/relation?referralCode=$referralCode';

  String get followingList => '/active/user/followings';

  String get followerList => '/active/user/followers';
}

final class WalletPath {
  const WalletPath._();

  String get list => '/active/wallet/myCards';

  String get query => '/active/wallet/getCard';

  String get create => '/active/cover/create';
}

final class CheckinPath {
  const CheckinPath._();

  String get check => '/active/checkin/check';

  String records({required String code}) => '/active/checkin/$code/records';
}

final class MeetingPath {
  const MeetingPath._();

  String attend({required String uniqId}) => '/active/meeting/$uniqId/will_attend';

  String isAttend({required String uniqId}) => '/active/meeting/$uniqId/is_attend';
}

final class ProfilePath {
  const ProfilePath._();

  String queryPub({required String cardCode}) => '/active/profile/pub/$cardCode';

  String socialGetPub({required String cardCode}) => '/active/profile/pub/$cardCode/socials';
}

final class CoverPath {
  const CoverPath._();

  String getCoverByPrintCode({required String qrcode, required String printCode}) =>
      '/active/cover/$qrcode/covers/$printCode';

  String get updateCoverByPrintCode => '/active/cover/cover_to_profile';

  String get getInfo => '/cover/cover/getInfo';

  String get create => '/cover/cover/create';
}

final class BlindBoxPath {
  const BlindBoxPath._();

  String get nfts => '/active/blind_box/nfts';

  String get claim => '/active/blind_box/claim';

  String claimAct({required String uniqId}) => '/active/blind_box/$uniqId/claim';

  String checkAct({required String uniqId}) => '/active/blind_box/$uniqId/check';

  String storyClaim({required String uniqId}) => '/active/blind_box/$uniqId/story_claim';
}

final class EthccPath {
  const EthccPath._();

  String queryPub({required String code}) => '/active/ethcc/pub/$code';

  String get roles => '/active/ethcc/roles';

  String get topics => '/active/ethcc/topics';

  String get profile => '/active/ethcc/profile';

  String get profileRoles => '/active/ethcc/profile/role';

  String get profileTopics => '/active/ethcc/profile/topic';

  String get profileGithubHandle => '/active/ethcc/profile/github';
}

final class StoryPath {
  const StoryPath._();

  String get mint => '/active/story/mint';

  String get finish => '/active/story/finish';
}

final class LuckyDrawPath {
  const LuckyDrawPath._();

  String get luckyDraw => '/active/lucky_draw/lucky_draw';
}

final class TwitterPath {
  const TwitterPath._();

  String get login => '/active/twitter/login';

  String get callback => '/active/twitter/callback';

  String get me => '/active/twitter/me';

  String get reTweet => '/active/twitter/retweet';

  String get follow => '/active/twitter/follow';

  String getTweet({required String id}) => '/active/twitter/tweet/$id';
}

/// API 实例
const opPath = OpPath._();
const gatewayApi = GatewayApi._();
const card3Api = Card3Api._();
