import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/card.dart' show SocialPlatform;
import 'data.dart' show SocialSvgIcon;

/// 社交平台网格组件
class SocialGrid extends ConsumerWidget {
  const SocialGrid({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 这里应该从全局状态中获取卡片数据
    // 在实际实现中替换为正确的状态提供者
    final cards = ref.watch(cardsProvider);

    // 过滤社交平台
    final socialPlatforms = _filterSocialPlatforms(cards);

    return GridView.builder(
      shrinkWrap: true,
      // 启用滚动，移除NeverScrollableScrollics
      physics: const ClampingScrollPhysics(),
      padding: const EdgeInsets.only(bottom: 40),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 1.3, // 更新宽高比为1.1
        crossAxisSpacing: 12, // 增加横向间距
        mainAxisSpacing: 12, // 增加纵向间距
      ),
      itemCount: socialPlatforms.length,
      itemBuilder: (context, index) {
        final platform = socialPlatforms[index];
        return GestureDetector(
          onTap: () {
            context.navigator.pushReplacementNamed(
              Routes.social.name,
              arguments: Routes.social.d(platform: platform),
            );
          },
          child: Container(
            decoration: BoxDecoration(
              color: platform.backgroundColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: platform.borderColor ?? Colors.transparent,
              ),
              gradient: platform.gradient,
            ),
            child: Center(
              child: SocialSvgIcon(platform: platform),
            ),
          ),
        );
      },
    );
  }

  /// 过滤社交平台
  List<SocialPlatform> _filterSocialPlatforms(List<dynamic>? cards) {
    if (cards == null || cards.isEmpty) {
      return SocialPlatform.values;
    }

    return SocialPlatform.values.where((platform) {
      // 如果平台没有event限制，或者用户有匹配的卡片
      return platform.event == null ||
          platform.event!.any((eventId) => cards.any((card) => card.card3EventId == eventId));
    }).toList();
  }
}

// 假设的状态提供者 - 需要根据实际项目调整
final cardsProvider = StateProvider<List<dynamic>?>(
  (ref) => [],
);
