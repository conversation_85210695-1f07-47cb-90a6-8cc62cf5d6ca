import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;

import '/constants/envs.dart';
import '/internals/methods.dart' show handleExceptions;
import '/routes/card3_routes.dart' show Routes;

import 'handler.dart';

// 卡片激活事件
class CardActivationEvent {
  const CardActivationEvent({
    required this.cardCode,
    required this.activeCode,
  });

  final String cardCode;
  final String activeCode;
}

abstract class AppLinkHelper {
  static const _tag = '🔗 AppLinkHelper';

  static final _$ = AppLinks();

  static final _handlers = <AppLinkHandler>[
    const LiveAppLinkHandler(),
    const SocialLinkHandler(),
  ];

  static StreamSubscription<Uri>? _sub;
  static Uri? _pendingLink;
  static bool _startHandle = false;

  // 全局回调函数
  static Function(String cardCode, String activeCode)? _globalCardActivationCallback;

  // 缓存的激活事件（用于冷启动时回调函数还未设置的情况）
  static CardActivationEvent? _pendingCardActivationEvent;

  // 设置全局回调函数
  static void setCardActivationCallback(Function(String cardCode, String activeCode) callback) {
    _globalCardActivationCallback = callback;
    if (_pendingCardActivationEvent != null) {
      _globalCardActivationCallback!(_pendingCardActivationEvent!.cardCode, _pendingCardActivationEvent!.activeCode);
      _pendingCardActivationEvent = null;
    }
  }

  // 清除全局回调函数
  static void clearCardActivationCallback() {
    _globalCardActivationCallback = null;
  }

  static void register() {
    _sub?.cancel();
    _sub = _$.uriLinkStream.listen(handleUri);
    meNavigatorObserver.addListener(
      MERouteListener(
        actions: MERouteAction.values.toSet(),
        onRouteChanged: (_, newRoute, _) {
          final previousStartHandle = _startHandle;
          _startHandle =
              meNavigatorObserver.history.any((route) => route.settings.name == Routes.home.name) ||
              newRoute?.settings.name == Routes.home.name;
          if (_pendingLink case final link? when _startHandle && !previousStartHandle) {
            handleUri(link);
            _pendingLink = null;
          }
        },
      ),
    );
    _fetchInitialLink();
  }

  static void _fetchInitialLink() {
    _$.getInitialLink().then(
      (link) {
        if (link != null) {
          LogUtil.d('Initial link: $link', tag: _tag, tagWithTrace: false);
          _pendingLink ??= link;
        }
      },
      onError: (e, s) {
        handleExceptions(error: e, stackTrace: s);
      },
    );
  }

  static void addHandler(AppLinkHandler handler) {
    _handlers.add(handler);
  }

  static void removeHandler(AppLinkHandler handler) {
    _handlers.remove(handler);
  }

  static bool handleUri(Uri uri) {
    if (!_startHandle) {
      _pendingLink = uri;
      return false;
    }

    final allowedHosts = [
      envUrlCard3,
      envUrlShort,
      envUrlSocial,
    ].map((e) => e.replaceFirst('https://', '')).toList();

    if (uri.scheme != 'card3' && !allowedHosts.contains(uri.host)) {
      return false;
    }

    // 处理 card3:// 协议的深链接
    if (uri.scheme == 'card3') {
      return _handleCard3Scheme(uri);
    }

    // 原有的路径处理逻辑
    final paths = uri.pathSegments.toList();
    if (paths.isEmpty && uri.host == envUrlCard3.replaceFirst('https://', '')) {
      _handleActiveCard(uri);
      return false;
    }
    for (final handler in _handlers) {
      try {
        if (handler.onLink(uri)) {
          return true;
        }
      } catch (e, s) {
        handleExceptions(error: e, stackTrace: s);
      }
    }
    return false;
  }

  // 处理 card3:// 协议的深链接
  static bool _handleCard3Scheme(Uri uri) {
    try {
      final path = uri.path.replaceFirst('/', ''); // 移除开头的 /

      switch (path) {
        case 'active_card':
          return _handleActiveCard(uri);
        case 'webview':
          return _handleWebview(uri);
        case 'customize':
          return _handleCustomize(uri);
        default:
          return false;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return false;
    }
  }

  // 处理卡片激活深链接
  static bool _handleActiveCard(Uri uri) {
    final cardCode = uri.queryParameters['card_code'];
    final activeCode = uri.queryParameters['active_code'];

    if (cardCode == null || activeCode == null) {
      return false;
    }

    if (_globalCardActivationCallback != null) {
      _globalCardActivationCallback!(cardCode, activeCode);
    } else {
      _pendingCardActivationEvent = CardActivationEvent(
        cardCode: cardCode,
        activeCode: activeCode,
      );
      Timer(const Duration(seconds: 1), () {
        if (_pendingCardActivationEvent != null && _globalCardActivationCallback != null) {
          _globalCardActivationCallback!(
            _pendingCardActivationEvent!.cardCode,
            _pendingCardActivationEvent!.activeCode,
          );
          _pendingCardActivationEvent = null;
        }
      });

      Timer(const Duration(seconds: 3), () {
        if (_pendingCardActivationEvent != null && _globalCardActivationCallback != null) {
          _globalCardActivationCallback!(
            _pendingCardActivationEvent!.cardCode,
            _pendingCardActivationEvent!.activeCode,
          );
          _pendingCardActivationEvent = null;
        }
      });
    }

    return true;
  }

  static bool _handleCustomize(Uri uri) {
    final code = uri.queryParameters['code'];
    if (code == null) {
      return false;
    }
    meNavigator.pushNamed(
      Routes.customize.name,
      arguments: Routes.customize.d(code: code),
    );
    return true;
  }

  // 处理webview深链接
  static bool _handleWebview(Uri uri) {
    //_Uri (card3:///webview?url=https://test-v.card3.fun/checkin?code=1otrwkmqnam6rdue&uid=044C5FD2151990&ctr=00015A&cmac=B8684F600F74298E)

    //url https://test-v.card3.fun/checkin?code=1otrwkmqnam6rdue

    // 获取原始查询字符串，然后手动解析URL参数
    final queryString = uri.query;

    String? url;
    String? title;

    if (queryString.isNotEmpty) {
      final urlStart = queryString.indexOf('url=');
      if (urlStart != -1) {
        final urlValueStart = urlStart + 4; // 'url='.length
        final titleStart = queryString.indexOf('&title=');
        if (titleStart != -1 && titleStart > urlValueStart) {
          url = Uri.decodeComponent(queryString.substring(urlValueStart, titleStart));
          final titleValueStart = titleStart + 7;
          title = Uri.decodeComponent(queryString.substring(titleValueStart));
        } else {
          url = Uri.decodeComponent(queryString.substring(urlValueStart));
        }
      }
    }

    if (url == null) {
      url = uri.queryParameters['url'];
      title = uri.queryParameters['title'];
    }

    if (url == null) {
      return false;
    }

    final parsedUrl = Uri.tryParse(url);
    if (parsedUrl == null || !parsedUrl.hasAbsolutePath) {
      return false;
    }

    meNavigator.pushNamed(
      Routes.webview.name,
      arguments: Routes.webview.d(
        url: url, // 这里传递的是提取出的实际URL，如https://test-v.card3.fun/...
        title: title ?? 'Card3',
      ),
    );

    return true;
  }

  // 清理资源
  static void dispose() {
    _sub?.cancel();
  }
}
