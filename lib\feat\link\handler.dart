import 'package:me_misc/me_misc.dart' show meNavigator, MENavigatorExtension;

import '/constants/envs.dart';
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/app_loading.dart';

abstract class AppLinkHandler {
  bool onLink(Uri uri);
}

class LiveAppLinkHandler implements AppLinkHandler {
  const LiveAppLinkHandler();

  static bool _validateUri(Uri uri) {
    return uri.pathSegments.elementAtOrNull(0) == 'customize' ||
        uri.pathSegments.elementAtOrNull(0) == 'event' ||
        uri.pathSegments.elementAtOrNull(0) == 'checkin';
  }

  @override
  bool onLink(Uri uri) {
    if (!_validateUri(uri)) {
      return false;
    }

    AppLoading.run(() async {
      if (uri.pathSegments.elementAtOrNull(0) == 'customize') {
        final code = uri.queryParameters['code'];
        if (code == null) {
          return;
        }
        meNavigator.pushNamed(
          Routes.customize.name,
          arguments: Routes.customize.d(code: code),
        );
        return;
      }

      final title = uri.queryParameters['title'];
      meNavigator.pushNamed(
        Routes.webview.name,
        arguments: Routes.webview.d(
          url: uri.toString(), // 这里传递的是提取出的实际URL，如https://test-v.card3.fun/...
          title: title,
        ),
      );
    });
    return true;
  }
}

class SocialLinkHandler implements AppLinkHandler {
  const SocialLinkHandler();

  static const _codeRegexRaw = r'\w{6,20}';
  static final _codeRegex = RegExp(_codeRegexRaw);
  static final _shortRegex = RegExp('^$envUrlShort/($_codeRegexRaw)');

  String? _extractCodeFromUrl(Uri uri) {
    String? code;
    final url = uri.toString();
    if (_shortRegex.firstMatch(url)?.group(1) case final match?) {
      code = match;
    } else if (url.startsWith(envUrlSocial) && uri.pathSegments.elementAtOrNull(0) == 'profile') {
      code = _codeRegex.firstMatch(uri.queryParameters['card_code'] ?? '')?.group(0);
    }
    if (code?.trim().isEmpty ?? false) {
      code = null;
    }
    return code;
  }

  @override
  bool onLink(Uri uri) {
    final code = _extractCodeFromUrl(uri);
    if (code == null) {
      return false;
    }
    meNavigator.removeNamedAndPushAndRemoveUntil(
      Routes.socialProfile.name,
      arguments: Routes.socialProfile.d(code: code),
      predicate: (_) => true,
    );
    return true;
  }
}
