import 'dart:async';

import 'package:card3/exports.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:web3dart/web3dart.dart' as web3;

class EvmService {
  web3.Web3Client? _client;
  String? _walletAddress;
  int? _chainId;

  // 初始化 EVM 客户端
  Future<void> initialize(String rpcUrl, int chainId) async {
    _client = web3.Web3Client(
      rpcUrl,
      http.Client(),
    );
    _chainId = chainId;
    await privyClient.awaitReady();
    _walletAddress = privyClient.user?.embeddedEthereumWallets.firstOrNull?.address;
    LogUtil.d('EVM Service Initialized: rpcUrl=$rpcUrl, walletAddress=$_walletAddress');
  }

  // 获取钱包地址
  String? get walletAddress => _walletAddress;

  // 获取代币余额
  Future<BigInt> getTokenBalance(
    String tokenAddress,
    String walletAddress,
  ) async {
    try {
      final contract = web3.DeployedContract(
        web3.ContractAbi.fromJson(ERC20_ABI, 'ERC20'),
        web3.EthereumAddress.fromHex(tokenAddress),
      );

      final result = await _client!.call(
        contract: contract,
        function: contract.function('balanceOf'),
        params: [web3.EthereumAddress.fromHex(walletAddress)],
      );

      return result[0] as BigInt;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  // 获取原生代币余额
  Future<BigInt> getNativeBalance(String address) async {
    try {
      final balance = await _client!.getBalance(
        web3.EthereumAddress.fromHex(address),
      );
      return balance.getInWei;
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  // 封装交易参数获取方法
  Future<Map<String, dynamic>> _buildTransactionParams({
    required String to,
    required BigInt value,
    BigInt? gasLimit,
    String? data,
  }) async {
    // 基本参数
    final String valueHex = '0x${value.toRadixString(16)}';
    final String gasLimitHex = '0x${(gasLimit ?? BigInt.from(21000)).toRadixString(16)}';
    final nonce = await _client!.getTransactionCount(
      web3.EthereumAddress.fromHex(_walletAddress!),
    );
    final String nonceHex = '0x${nonce.toRadixString(16)}';

    // 检测网络是否支持EIP-1559
    bool supportsEIP1559 = false;
    try {
      // 调用eth_getBlockByNumber获取最新区块，检查是否包含baseFeePerGas字段
      final block = await _client!.getBlockInformation(blockNumber: 'latest');
      // 在EIP-1559中，baseFeePerGas是存在的
      supportsEIP1559 = block.baseFeePerGas != null;
      LogUtil.d('网络${supportsEIP1559 ? "支持" : "不支持"}EIP-1559');
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      supportsEIP1559 = false;
    }

    // 创建交易参数
    final Map<String, dynamic> txParams = {
      'from': _walletAddress,
      'to': to,
      'value': valueHex,
      'chainId': '0x${_chainId!.toRadixString(16)}',
      'nonce': nonceHex,
    };

    // 如果有data参数，添加到交易参数中
    if (data != null && data.isNotEmpty) {
      txParams['data'] = data;
    }

    // 根据EIP-1559支持情况设置gas参数
    if (supportsEIP1559) {
      // EIP-1559方式
      // 获取当前gas建议值
      Map<String, dynamic>? feeData;
      try {
        // 尝试使用eth_feeHistory RPC调用获取gas费用
        feeData = await _client!.getFeeHistory(1);
      } catch (e, s) {
        handleExceptions(error: e, stackTrace: s);
      }

      // 默认基础费用 (如果无法获取，使用合理的默认值)
      BigInt baseFee = BigInt.from(15000000000); // 15 Gwei
      if (feeData != null && feeData.containsKey('baseFeePerGas') && feeData['baseFeePerGas'].isNotEmpty) {
        final baseFeeValue = feeData['baseFeePerGas'][0];
        // 处理不同类型的返回值
        if (baseFeeValue is String) {
          // 处理十六进制字符串
          if (baseFeeValue.startsWith('0x')) {
            baseFee = BigInt.parse(baseFeeValue.substring(2), radix: 16);
          } else {
            // 处理十进制字符串
            baseFee = BigInt.parse(baseFeeValue);
          }
        } else if (baseFeeValue is BigInt) {
          // 已经是BigInt类型，直接使用
          baseFee = baseFeeValue;
        } else if (baseFeeValue is int) {
          // 是整数类型，转换为BigInt
          baseFee = BigInt.from(baseFeeValue);
        }
      }

      // 设置优先费用(小费)和最大总费用
      final minPriorityFee = BigInt.from(25000000000); // 25 Gwei
      final maxFeePerGas = baseFee * BigInt.from(2) + minPriorityFee; // 确保在拥堵时也能通过

      txParams['gas'] = gasLimitHex; // 在EIP-1559中使用'gas'
      txParams['maxFeePerGas'] = '0x${maxFeePerGas.toRadixString(16)}';
      txParams['maxPriorityFeePerGas'] = '0x${minPriorityFee.toRadixString(16)}';
    } else {
      // 传统方式
      final gasPrice = await _client!.getGasPrice();
      // 增加20%作为安全边际
      final adjustedGasPrice = gasPrice.getInWei * BigInt.from(120) ~/ BigInt.from(100);

      txParams['gasLimit'] = gasLimitHex;
      txParams['gasPrice'] = '0x${adjustedGasPrice.toRadixString(16)}';
    }

    return txParams;
  }

  // 发送原生代币交易（ETH、BNB等）
  Future<String> sendNativeTransaction(
    String to,
    BigInt value, {
    BigInt? gasLimit,
  }) async {
    LogUtil.d('发送原生代币交易: to=$to, value=$value');

    // 获取钱包
    final wallet = privyClient.user?.embeddedEthereumWallets.firstOrNull;
    if (wallet == null) {
      throw Exception('无法获取钱包');
    }
    final txPayload = await _buildTransactionParams(
      to: to,
      value: value,
      gasLimit: gasLimit,
    );
    LogUtil.d('sendNativeTransaction txPayload: ${jsonEncode(txPayload)}');
    final signRequest = EthereumRpcRequest(
      method: 'eth_signTransaction',
      params: [jsonEncode(txPayload)],
    );
    final signResult = await wallet.provider.request(signRequest);
    final completer = Completer<String>();
    signResult.fold(
      onSuccess: (response) async {
        LogUtil.d('sendNativeTransaction onSuccess: $response');
        try {
          final txHash = await _sendRawTransaction(response.data, wallet);
          completer.complete(txHash);
        } catch (e, s) {
          completer.completeError(e, s);
        }
      },
      onFailure: (error) {
        handleExceptions(error: error, stackTrace: StackTrace.current);
        completer.completeError(error);
      },
    );

    return completer.future;
  }

  Future<String> _sendRawTransaction(
    String signedTx,
    EmbeddedEthereumWallet wallet,
  ) async {
    signedTx = signedTx.startsWith('0x') ? signedTx.substring(2) : signedTx;
    LogUtil.d('sendRawTransaction: $signedTx');
    final result = await _client!.sendRawTransaction(hexToBytes(signedTx));
    LogUtil.d('sendRawTransaction result: $result');
    return result;
  }

  // 发送 ERC20 代币交易
  Future<String> sendTokenTransaction(
    String tokenAddress,
    String to,
    BigInt value, {
    BigInt? gasLimit,
  }) async {
    LogUtil.d(
      '发送 ERC20 代币交易: '
      'tokenAddress=$tokenAddress, to=$to, value=$value',
    );

    // 获取钱包
    final wallet = privyClient.user?.embeddedEthereumWallets.firstOrNull;
    if (wallet == null) {
      throw StateError('No wallet found');
    }

    // 创建 ERC20 合约实例
    final contract = web3.DeployedContract(
      web3.ContractAbi.fromJson(ERC20_TRANSFER_ABI, 'ERC20'),
      web3.EthereumAddress.fromHex(tokenAddress),
    );

    // 获取转账函数
    final transferFunction = contract.function('transfer');

    // 编码转账函数调用
    final data = transferFunction.encodeCall([
      web3.EthereumAddress.fromHex(to),
      value,
    ]);

    // 转换数据为十六进制
    final String dataHex = '0x${_bytesToHex(data)}';

    // 使用封装的交易参数构建函数
    final txPayload = await _buildTransactionParams(
      to: tokenAddress,
      value: BigInt.zero, // ERC20 转账不需要发送 ETH
      gasLimit: gasLimit ?? BigInt.from(100000),
      data: dataHex,
    );

    LogUtil.d('ERC20 交易创建完成，准备签名: ${jsonEncode(txPayload)}');

    // 签名交易
    final signRequest = EthereumRpcRequest(
      method: 'eth_signTransaction',
      params: [jsonEncode(txPayload)],
    );

    final signResult = await wallet.provider.request(signRequest);
    final completer = Completer<String>();

    signResult.fold(
      onSuccess: (response) async {
        LogUtil.d('签名交易结果: $response');
        try {
          // 发送签名后的交易
          final txHash = await _sendRawTransaction(response.data, wallet);
          completer.complete(txHash);
        } catch (e) {
          completer.completeError(e);
        }
      },
      onFailure: (error) {
        LogUtil.e('签名交易失败: $error');
        completer.completeError(error);
      },
    );

    return completer.future;
  }

  // 辅助函数：字节数组转十六进制字符串
  String _bytesToHex(Uint8List bytes) {
    return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
  }

  // 向后兼容的交易方法
  Future<String> sendTransaction(
    String to,
    BigInt value, {
    BigInt? gasLimit,
  }) {
    return sendNativeTransaction(to, value, gasLimit: gasLimit);
  }

  // 关闭连接
  Future<void> dispose() async {
    await _client?.dispose();
    _client = null;
  }
}

// ERC20代币ABI
const String ERC20_ABI = '''
[
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "type": "function"
  }
]
''';

// ERC20 转账 ABI
const String ERC20_TRANSFER_ABI = '''
[
  {
    "constant": false,
    "inputs": [
      {"name": "_to", "type": "address"},
      {"name": "_value", "type": "uint256"}
    ],
    "name": "transfer",
    "outputs": [{"name": "success", "type": "bool"}],
    "type": "function"
  },
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "type": "function"
  }
]
''';

// 辅助函数：十六进制字符串转字节数组
Uint8List hexToBytes(String hex) {
  final result = Uint8List(hex.length ~/ 2);
  for (var i = 0; i < hex.length; i += 2) {
    final num = int.parse(hex.substring(i, i + 2), radix: 16);
    result[i ~/ 2] = num;
  }
  return result;
}
