import 'dart:io' as io show Platform;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '/models/business.dart';
import '/provider/business.dart' show fetchNetworksProvider;
import '/provider/chain.dart';
import '/provider/token.dart';
import '/services/select_token.dart';
import '/ui/biz/wallet/select_network.dart';
import '/ui/biz/wallet/send.dart';

class Wallet extends ConsumerStatefulWidget {
  const Wallet({super.key});

  @override
  ConsumerState<Wallet> createState() => _WalletState();
}

class _WalletState extends ConsumerState<Wallet> {
  @override
  void initState() {
    super.initState();
    _initializeNetwork();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 确保在依赖变化时（比如用户重新登录）重新初始化网络
    _initializeNetwork();
  }

  Future<void> _initializeNetwork() async {
    try {
      await ref.read(currentNetworkProvider.notifier).initializeLastUsedOrDefaultNetwork();
    } catch (e, s) {
      LogUtil.e('初始化网络失败: $e', stackTrace: s);
    }
  }

  Future<void> _refreshData() async {
    ref.invalidate(allTokenBalancesProvider);
    await ref.read(allTokenBalancesProvider.future);
  }

  @override
  Widget build(BuildContext context) {
    final networksResult = ref.watch(fetchNetworksProvider);
    final currentNetwork = ref.watch(currentNetworkProvider);
    final walletAddress = ref.watch(walletAddressProvider);
    final allTokenBalances = ref.watch(allTokenBalancesProvider);
    return SafeArea(
      bottom: false,
      child: RefreshIndicator(
        onRefresh: _refreshData,
        color: Colors.deepPurpleAccent,
        child: SingleChildScrollView(
          // 添加物理滚动设置，确保可以触发下拉刷新
          physics: const AlwaysScrollableScrollPhysics(),
          child: ConstrainedBox(
            // 确保内容至少占满整个屏幕高度，以便可以滚动
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Column(
                // 设置为垂直方向填充可用空间
                mainAxisSize: MainAxisSize.max,
                children: [
                  // 添加一个空白区域，确保下拉刷新触发区域足够
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: () {
                          showModalBottomSheet(
                            context: context,
                            scrollControlDisabledMaxHeightRatio: 0.7,
                            backgroundColor: Colors.white,
                            clipBehavior: Clip.antiAlias,
                            shape: RoundedRectangleBorder(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(20.0),
                              ),
                              side: BorderSide(
                                color: context.theme.dividerColor,
                              ),
                            ),
                            builder: (context) => const SelectNetwork(),
                          );
                        },
                        child: Container(
                          width: 100,
                          height: 40,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              // 网络图标
                              if (currentNetwork != null && currentNetwork.iconUrl.isNotEmpty)
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: MEImage(
                                    currentNetwork.iconUrl,
                                    clipOval: true,
                                    fit: BoxFit.cover,
                                    alternativeSVG: true,
                                  ),
                                )
                              else if (networksResult.isLoading)
                                const AppLoading(size: 24.0)
                              else
                                Container(
                                  width: 24,
                                  height: 24,
                                  decoration: BoxDecoration(
                                    color: Colors.deepPurple[200],
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(
                                      color: Colors.black,
                                      width: 2,
                                    ),
                                  ),
                                  child: const Center(
                                    child: Text(
                                      'E',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ),
                              const SizedBox(width: 4),
                              // 网络名称
                              if (currentNetwork != null)
                                Expanded(
                                  child: Text(
                                    currentNetwork.shortName,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              const Icon(
                                Icons.keyboard_arrow_down,
                                color: Colors.black54,
                                size: 24,
                              ),
                            ],
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          meNavigator.pushNamed(Routes.setting.name);
                        },
                        child: Assets.icons.setting.index.svg(
                          width: 30,
                          height: 30,
                          colorFilter: Colors.black.filter,
                        ),
                      ),
                    ],
                  ),
                  // 钱包卡片
                  _buildWalletCard(
                    address: walletAddress,
                    tokenBalances: allTokenBalances,
                  ),
                  const SizedBox(height: 20),

                  // 操作按钮行
                  _buildActionButtons(),
                  const SizedBox(height: 20),

                  // 代币列表
                  _buildTokenCard(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 钱包卡片
  Widget _buildWalletCard({
    required String? address,
    required AsyncValue<List<TokenBalance>> tokenBalances,
  }) {
    // 格式化钱包地址显示：前6位 + ... + 后4位
    String? displayAddress;
    if (address != null && address.isNotEmpty) {
      displayAddress = address.length > 10
          ? '${address.substring(0, 6)}...${address.substring(address.length - 4)}'
          : address;
    }

    // 计算总价值
    final Widget balanceWidget = tokenBalances.when(
      data: (balances) {
        if (balances.isEmpty) {
          return const Text(
            '\$0.00',
            style: TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          );
        }

        Decimal total = Decimal.zero;
        for (final balance in balances) {
          if (balance.usdValue != '--') {
            total += Decimal.parse(balance.usdValue);
          }
        }
        return Text(
          '\$${total.toStringAsFixed(2)}',
          style: const TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        );
      },
      loading: () => const Text(
        '\$---',
        style: TextStyle(
          fontSize: 36,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
      error: (_, _) => const Text(
        '\$---',
        style: TextStyle(
          fontSize: 36,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );

    return Container(
      margin: const EdgeInsets.only(top: 20),
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
      child: Column(
        children: [
          // 余额标题
          const Text(
            'Balance',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),

          // 余额金额
          balanceWidget,
          const SizedBox(height: 16),

          // 钱包地址
          GestureDetector(
            onTap: () {
              if (address == null || address.isEmpty) {
                return;
              }
              // 复制钱包地址
              Clipboard.setData(ClipboardData(text: address));
              Card3ToastUtil.showToast(message: ToastMessages.copied);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  displayAddress ?? '--',
                  style: const TextStyle(
                    color: ColorName.themeColorDark,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(width: 5),
                const Icon(
                  Icons.copy_outlined,
                  color: ColorName.themeColorDark,
                  size: 20,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 操作按钮行
  Widget _buildActionButtons() {
    final walletAddress = ref.watch(walletAddressProvider);
    final tokenBalances = ref.watch(allTokenBalancesProvider);
    final currentNetwork = ref.watch(currentNetworkProvider);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton(
          icon: Assets.icons.receive.svg(width: 30, height: 30),
          label: 'Receive',
          onTap: () {
            if (walletAddress == null || walletAddress.isEmpty) {
              return;
            }
            // 显示接收地址二维码
            _showReceiveDialog(context, walletAddress);
          },
        ),
        // _buildActionButton(
        //   icon: Assets.icons.swap.svg(width: 30, height: 30),
        //   label: 'Swap',
        //   onTap: () async {
        //     // 处理交换操作
        //   },
        // ),
        _buildActionButton(
          icon: Assets.icons.send.svg(width: 30, height: 30),
          label: 'Send',
          onTap: () => _handleSendButtonTap(currentNetwork, tokenBalances),
        ),
      ],
    );
  }

  // 处理发送按钮点击事件
  void _handleSendButtonTap(
    Network? currentNetwork,
    AsyncValue<List<TokenBalance>> tokenBalances,
  ) {
    // 判断是否为 Solana 链
    if (_isSolanaNetwork(currentNetwork)) {
      _showSolanaWarningDialog();
    } else {
      // 原有的发送流程
      showTokenSelection(context, tokenBalances.valueOrNull ?? [], (tokenBalance) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => SendPage(tokenBalance: tokenBalance),
          ),
        );
      });
    }
  }

  // 判断是否为 Solana 网络
  bool _isSolanaNetwork(Network? network) {
    if (network == null) {
      return false;
    }

    // 根据网络名称或其他标识判断是否为 Solana
    // 这里可能需要根据实际的网络对象结构调整判断条件
    final networkName = network.name.toLowerCase();
    return networkName.contains('solana') || networkName.contains('sol');
  }

  // 显示 Solana 警告弹窗
  void _showSolanaWarningDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Solana Network',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  icon: const Icon(Icons.close, color: Colors.black, size: 28),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.info_outline,
                size: 40,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Solana token transfers are currently only supported on the web version.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Please use the web version to send Solana tokens.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
                height: 1.4,
              ),
            ),
            if (io.Platform.isAndroid)
              Text(
                envUrlCard3,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 14,
                  color: ColorName.themeColorDark,
                  height: 1.4,
                ),
              ),
            const SizedBox(height: 32),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.grey.shade300,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Center(
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                      if (io.Platform.isAndroid) {
                        Clipboard.setData(ClipboardData(text: envUrlCard3));
                        Card3ToastUtil.showToast(message: ToastMessages.copied);
                      } else {
                        _openWebVersion();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: ColorName.themeColorDark,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          io.Platform.isAndroid ? 'Copy Link' : 'Go to Web',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  String getBypassUrl(String originalUrl) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uri = Uri.parse(originalUrl);

    // 添加随机参数绕过深度链接匹配
    return uri
        .replace(
          queryParameters: {
            ...uri.queryParameters,
            'bypass_${timestamp.toString()}': '1',
            'source': 'external_launch',
            'ref': 'direct_open',
          },
        )
        .toString();
  }

  // 打开网页版
  Future<void> _openWebVersion() {
    // 这里可以跳转到网页版的钱包页面
    // 假设网页版的URL，需要根据实际情况调整
    return launchUrlString(
      envUrlCard3,
      mode: LaunchMode.externalApplication,
    );
  }

  // 显示地址接收二维码对话框
  void _showReceiveDialog(BuildContext context, String address) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        spacing: 30.0,
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Receive',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  icon: const Icon(Icons.close, color: Colors.black, size: 28),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          Expanded(
            child: FittedBox(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.grey.shade200, width: 1),
                ),
                child: QrImageView(
                  data: address,
                  version: QrVersions.auto,
                  size: 250,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 10),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    address,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
                Tapper(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: address));
                    Card3ToastUtil.showToast(message: ToastMessages.copied);
                  },
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: ColorName.themeColorDark,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Icon(
                      Icons.copy_outlined,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Gap.v(50.0),
        ],
      ),
    );
  }

  // 单个操作按钮
  Widget _buildActionButton({
    required Widget icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 5),
          padding: const EdgeInsets.symmetric(vertical: 14),
          decoration: BoxDecoration(
            color: ColorName.themeColorDark,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              icon,
              const SizedBox(height: 5),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 代币列表
  Widget _buildTokenCard() {
    final tokenBalancesAsync = ref.watch(allTokenBalancesProvider);

    return tokenBalancesAsync.when(
      loading: () => const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.deepPurpleAccent),
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Load token failed',
              style: TextStyle(
                color: Colors.red[400],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                ref.invalidate(allTokenBalancesProvider);
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (tokenBalances) {
        if (tokenBalances.isEmpty) {
          return const Center(
            child: Text(
              'No tokens',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          );
        }

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: tokenBalances.length,
              itemBuilder: (context, index) {
                final tokenBalance = tokenBalances[index];
                final token = tokenBalance.token;
                final balance = tokenBalance.balance;

                return GestureDetector(
                  onTap: () {
                    final currentNetwork = ref.read(currentNetworkProvider);
                    if (_isSolanaNetwork(currentNetwork)) {
                      _showSolanaWarningDialog();
                    } else {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => SendPage(tokenBalance: tokenBalance),
                        ),
                      );
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: index < tokenBalances.length - 1
                          ? Border(
                              bottom: BorderSide(
                                color: Colors.grey.withValues(alpha: 0.1),
                                width: 1,
                              ),
                            )
                          : null,
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 20,
                    ),
                    margin: index == tokenBalances.length - 1
                        ? const EdgeInsets.only(bottom: 0)
                        : const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        // 代币图标
                        if (token.icon.isNotEmpty)
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: MEImage(
                              token.icon,
                              clipOval: true,
                              fit: BoxFit.cover,
                              alternativeSVG: true,
                            ),
                          )
                        else
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.deepPurple[200],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Center(
                              child: Text(
                                token.symbol.substring(0, 1).toUpperCase(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        const SizedBox(width: 12),

                        // 代币名称和符号
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                token.symbol,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // 代币数量及价值
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              balance.toStringAsFixed(4),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '\$${tokenBalance.usdValue}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[400],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
