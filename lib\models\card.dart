import 'package:collection/collection.dart';
import 'package:flutter/material.dart' show Alignment, Color, Colors, LinearGradient;
import 'package:freezed_annotation/freezed_annotation.dart';

part 'card.freezed.dart';

part 'card.g.dart';

const groupPreviewLogoSize = 100.0;

const eventIdAllowAll = 0;

enum NfcType {
  @JsonValue('NFC215')
  NFC215,
  @JsonValue('NFC424')
  NFC424,
}

enum CardType {
  @JsonValue('STICKER')
  STICKER,
  @JsonValue('CARD')
  CARD,
  @JsonValue('WRISTBAND')
  WRISTBAND,
}

enum PrintType {
  @JsonValue('METAL')
  METAL,
  @JsonValue('NORMAL')
  NORMAL,
}

@freezed
sealed class Social with _$Social {
  const factory Social({
    @JsonKey(name: 'handleName') required String handleName,
    @JsonK<PERSON>(name: 'id') required int id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'platformName') required String platformName,
    @<PERSON>son<PERSON>ey(name: 'platformUrl') required String platformUrl,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'isVerify') @Default(false) bool isVerify,
  }) = _Social;

  factory Social.fromJson(Map<String, dynamic> json) => _$SocialFromJson(json);
}

/// 社交平台名称枚举
enum SocialName {
  twitter,
  telegram,
  linkedIn,
  farcaster,
  instagram,
  linktree,
  github,
  snapchat,
  youtube,
  tiktok,
  reddit,
  phone,
  email,
  discord,
  ord,
  calendly,
  whatsapp,
  memex;

  String get displayName {
    switch (this) {
      case SocialName.twitter:
        return 'Twitter';
      case SocialName.telegram:
        return 'Telegram';
      case SocialName.linkedIn:
        return 'LinkedIn';
      case SocialName.farcaster:
        return 'Farcaster';
      case SocialName.instagram:
        return 'Instagram';
      case SocialName.linktree:
        return 'Linktree';
      case SocialName.github:
        return 'Github';
      case SocialName.snapchat:
        return 'Snapchat';
      case SocialName.youtube:
        return 'Youtube';
      case SocialName.tiktok:
        return 'TikTok';
      case SocialName.reddit:
        return 'Reddit';
      case SocialName.phone:
        return 'Phone';
      case SocialName.email:
        return 'Email';
      case SocialName.discord:
        return 'Discord';
      case SocialName.ord:
        return 'Ord';
      case SocialName.calendly:
        return 'Calendly';
      case SocialName.whatsapp:
        return 'Whatsapp';
      case SocialName.memex:
        return 'MemeX';
    }
  }
}

/// 社交平台配置
enum SocialPlatform {
  twitter(
    name: SocialName.twitter,
    alias: 'X (Twitter)',
    url: 'https://x.com/',
    backgroundColor: Colors.black,
    addon: '@',
    placeholder: 'Handle',
    demoImageKey: 'X-Twitter',
    iconImageKey: 'twitter',
  ),
  telegram(
    name: SocialName.telegram,
    url: 'https://t.me/',
    backgroundColor: Color(0xFF23A9EA),
    addon: '@',
    placeholder: 'Username',
    demoImageKey: 'Telegram',
    iconImageKey: 'telegram',
  ),
  instagram(
    name: SocialName.instagram,
    backgroundColor: Color(0xFF8438DB),
    // 渐变色在Flutter中需要特殊处理
    gradient: LinearGradient(
      colors: [
        Color(0xFF8438DB),
        Color(0xFFFF3E9B),
        Color(0xFFFF7B50),
        Color(0xFFFFE941),
      ],
      stops: [0.11, 0.35, 0.67, 0.87],
      begin: Alignment.topRight,
      end: Alignment.bottomLeft,
    ),
    url: 'https://www.instagram.com/',
    placeholder: 'Username',
    demoImageKey: 'Instagram',
    iconImageKey: 'instagram',
  ),
  linkedIn(
    name: SocialName.linkedIn,
    url: 'https://www.linkedin.com/in/',
    backgroundColor: Color(0xFF0C60BE),
    placeholder: 'Profile Username',
    demoImageKey: 'LinkedIn',
    iconImageKey: 'linkedIn',
  ),
  farcaster(
    name: SocialName.farcaster,
    url: 'https://farcaster.xyz/',
    backgroundColor: Color(0xFF7C65C1),
    placeholder: 'Username',
    demoImageKey: 'Farcaster',
    iconImageKey: 'farcaster',
    iconSize: 70.0,
  ),
  memex(
    name: SocialName.memex,
    url: 'https://app.memex.xyz/profile/',
    backgroundColor: Colors.white,
    borderColor: Color(0xFFAAAAAA),
    placeholder: 'Link to your MemeX profile',
    demoImageKey: 'MemeX',
    iconImageKey: 'memex',
    iconColor: Color(0xFF333333),
  ),
  whatsapp(
    name: SocialName.whatsapp,
    url: 'https://wa.me/',
    backgroundColor: Color(0xFF25D366),
    placeholder: 'Phone Number',
    iconImageKey: 'whatsapp',
  ),
  github(
    name: SocialName.github,
    url: 'https://github.com/',
    backgroundColor: Color(0xFF1A1E22),
    placeholder: 'Username',
    demoImageKey: 'GitHub',
    iconImageKey: 'github',
  ),
  discord(
    name: SocialName.discord,
    url: 'https://discord.com/users/',
    backgroundColor: Color(0xFF5865F2),
    placeholder: 'Username',
    iconImageKey: 'discord',
  ),
  tiktok(
    name: SocialName.tiktok,
    url: 'https://www.tiktok.com/@',
    backgroundColor: Color(0xFF000000),
    placeholder: 'Username',
    demoImageKey: 'TikTok',
    iconImageKey: 'titok',
  ),
  linktree(
    name: SocialName.linktree,
    url: 'https://linktr.ee/',
    backgroundColor: Color(0xFF00BA4C),
    placeholder: 'Username',
    demoImageKey: 'Linktree',
    iconImageKey: 'linkTree',
  ),
  youtube(
    name: SocialName.youtube,
    url: 'https://www.youtube.com/@',
    backgroundColor: Color(0xFFF70000),
    placeholder: 'Username',
    demoImageKey: 'YouTube',
    iconImageKey: 'youtube',
  ),
  calendly(
    name: SocialName.calendly,
    url: 'https://calendly.com/',
    backgroundColor: Color(0xFF006BFF),
    placeholder: 'Username',
    iconImageKey: 'calendly',
  ),
  ord(
    name: SocialName.ord,
    url: 'https://ordinals.com/',
    backgroundColor: Color(0xFF8858FF),
    placeholder: 'Name',
    iconImageKey: 'ord',
  ),
  phone(
    name: SocialName.phone,
    url: 'tel:+',
    backgroundColor: Color(0xFF8858FF),
    placeholder: 'Your phone number',
    isProfile: true,
    iconImageKey: 'phone',
  ),
  email(
    name: SocialName.email,
    url: 'mailto:',
    backgroundColor: Color(0xFF8858FF),
    placeholder: 'Your email',
    isProfile: true,
    iconImageKey: 'email',
  );

  const SocialPlatform({
    required this.name,
    this.alias,
    required this.url,
    required this.backgroundColor,
    this.borderColor,
    this.doneUrl,
    this.placeholder,
    this.isProfile = false,
    this.addon,
    this.event,
    this.gradient,
    this.demoImageKey,
    required this.iconImageKey,
    this.iconSize = 50.0,
    this.iconColor,
  });

  final SocialName name;
  final String? alias;
  final String url;
  final Color backgroundColor;
  final Color? borderColor;
  final String? doneUrl;
  final String? placeholder;
  final bool isProfile;
  final String? addon;
  final List<int>? event;
  final LinearGradient? gradient;
  final String? demoImageKey;
  final String iconImageKey;
  final double iconSize;
  final Color? iconColor;

  static SocialPlatform? fromName(String? name) {
    if (name == null || name.isEmpty) {
      return null;
    }
    return SocialPlatform.values.firstWhereOrNull((e) => e.name.displayName == name);
  }
}

@freezed
sealed class CardInfo with _$CardInfo {
  const factory CardInfo({
    @JsonKey(name: 'active') required bool active,
    @JsonKey(name: 'activeTime') @Default('') String activeTime,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'card3EventId') @Default(0) int card3EventId,
    @JsonKey(name: 'cardCode') @Default('') String cardCode,
    @JsonKey(name: 'cardType') @Default(CardType.CARD) CardType cardType,
    @JsonKey(name: 'chainId') @Default(0) int chainId,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'id') @Default(0) int id,
    @JsonKey(name: 'isActive') @Default(false) bool isActive,
    @JsonKey(name: 'referralCode') @Default('') String referralCode,
    @JsonKey(name: 'virtualCard') @Default(false) bool virtualCard,
    @JsonKey(name: 'nfcType') @Default(NfcType.NFC215) NfcType nfcType,
  }) = _CardInfo;

  factory CardInfo.fromJson(Map<String, dynamic> json) => _$CardInfoFromJson(json);
}

@freezed
sealed class CoverInfo with _$CoverInfo {
  const factory CoverInfo({
    @JsonKey(name: 'activeMode') @Default('') String activeMode,
    @JsonKey(name: 'backCover') @Default('') String backCover,
    @JsonKey(name: 'eventId') @Default('') String eventId,
    @JsonKey(name: 'eventName') @Default('') String eventName,
    @JsonKey(name: 'paymentLink') @Default('') String paymentLink,
    @JsonKey(name: 'price') @Default(0) int price,
    @JsonKey(name: 'priceDescription') @Default('') String priceDescription,
    @JsonKey(name: 'printType') @Default(PrintType.NORMAL) PrintType printType,
    @JsonKey(name: 'thirdPartyLink') @Default('') String thirdPartyLink,
  }) = _CoverInfo;

  factory CoverInfo.fromJson(Map<String, dynamic> json) => _$CoverInfoFromJson(json);
}

@freezed
sealed class CreateCardCoverResponse with _$CreateCardCoverResponse {
  const factory CreateCardCoverResponse({
    @JsonKey(name: 'code') required String code,
    @JsonKey(name: 'paymentLink') @Default('') String paymentLink,
  }) = _CreateCardCoverResponse;

  factory CreateCardCoverResponse.fromJson(Map<String, dynamic> json) => _$CreateCardCoverResponseFromJson(json);
}

@freezed
sealed class EthccProfile with _$EthccProfile {
  const factory EthccProfile({
    @JsonKey(name: 'githubHandle') @Default('') String githubHandle,
    @JsonKey(
      name: 'topics',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @Default([])
    List<String> topics,
    @JsonKey(
      name: 'role',
      fromJson: EthccProfile.fromJoinedString,
      toJson: EthccProfile.toJoinedString,
    )
    @JsonKey(name: 'role')
    @Default([])
    List<String> roles,
  }) = _EthccProfile;

  const EthccProfile._();

  factory EthccProfile.fromJson(Map<String, dynamic> json) => _$EthccProfileFromJson(json);

  static List<String> fromJoinedString(String value) {
    value = value.trim();
    if (value.isEmpty) {
      return [];
    }
    return value.split(',').map((e) => e.trim()).where((e) => e.isNotEmpty).toList();
  }

  static String toJoinedString(List<String> value) => value.map((e) => e.trim()).where((e) => e.isNotEmpty).join(',');

  bool get isEmpty => githubHandle.isEmpty && topics.isEmpty && roles.isEmpty;
}

@freezed
sealed class UpdateEthccTopicsRequest with _$UpdateEthccTopicsRequest {
  const factory UpdateEthccTopicsRequest({
    @JsonKey(name: 'topics') required String topics,
  }) = _UpdateEthccTopicsRequest;

  factory UpdateEthccTopicsRequest.fromJson(Map<String, dynamic> json) => _$UpdateEthccTopicsRequestFromJson(json);
}

@freezed
sealed class UpdateEthccGithubRequest with _$UpdateEthccGithubRequest {
  const factory UpdateEthccGithubRequest({
    @JsonKey(name: 'githubHandle') required String githubHandle,
  }) = _UpdateEthccGithubRequest;

  factory UpdateEthccGithubRequest.fromJson(Map<String, dynamic> json) => _$UpdateEthccGithubRequestFromJson(json);
}
