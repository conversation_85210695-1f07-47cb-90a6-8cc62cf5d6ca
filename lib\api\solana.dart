import 'package:flutter/widgets.dart' show BuildContext;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:solana/dto.dart';
import 'package:solana/metaplex.dart';
import 'package:solana/solana.dart';

import '/provider/api.dart' hide apiSolanaProvider;

enum SolanaNetwork {
  mainnet(
    101,
    'mainnet',
    'api.mainnet-beta.solana.com',
  ),
  testnet(
    102,
    'testnet',
    'api.testnet.solana.com',
  ),
  devnet(
    103,
    'devnet',
    'api.devnet.solana.com',
  );

  const SolanaNetwork(this.id, this.name, this.baseUrl);

  factory SolanaNetwork.fromName(String? name) {
    return values.firstWhere(
      (e) => e.name == name,
      orElse: () => SolanaNetwork.mainnet,
    );
  }

  // static String storageKeyFromWallet(BaseWallet wallet) {
  //   return 'solana:network:selected:wallet:${wallet.id}';
  // }
  //
  // static SolanaNetwork? fromBox(BaseWallet wallet) {
  //   final key = storageKeyFromWallet(wallet);
  //   return SolanaNetwork.fromName(HiveBoxes.settings.get(key));
  // }

  final int id;
  final String name;
  final String baseUrl;

  Uri get rpcUri => Uri(scheme: 'https', host: baseUrl);

  Uri get websocketUri => Uri(scheme: 'wss', host: baseUrl);

  String displayName(BuildContext context) {
    switch (this) {
      case SolanaNetwork.mainnet:
        return 'Mainnet';
      case SolanaNetwork.testnet:
        return 'Testnet';
      case SolanaNetwork.devnet:
        return 'Devnet';
    }
  }
}

final solanaSelectedNetworkProvider = StateProvider<SolanaNetwork>((ref) {
  // final wallet = ref.watch(selectedWalletProvider);
  // final network = SolanaNetwork.fromBox(wallet) ?? SolanaNetwork.mainnet;
  final network = SolanaNetwork.mainnet;
  return network;
});

final solanaClientProvider = Provider.autoDispose<SolanaClient>((ref) {
  final network = ref.watch(solanaSelectedNetworkProvider);
  final client = SolanaClient(
    rpcUrl: network.rpcUri,
    websocketUrl: network.websocketUri,
    timeout: const Duration(seconds: 15),
  );
  return client;
});

const _splProgramId = 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA';

class SolanaApi {
  SolanaApi(this.ref);

  final Ref ref;

  Http get _http => ref.read(httpProvider);

  Future<BigInt> getBalance(String address) async {
    final client = ref.read(solanaClientProvider);
    final result = await client.rpcClient.getBalance(address);
    return BigInt.from(result.value);
  }

  Future<Mint> getMintAccountByAddress(String address) async {
    final client = ref.read(solanaClientProvider);
    final result = await client.getMint(
      address: Ed25519HDPublicKey.fromBase58(address),
    );
    return result;
  }

  Future<List<ProgramAccount>> getTokens(String address) async {
    final client = ref.read(solanaClientProvider);
    final result = await client.rpcClient.getTokenAccountsByOwner(
      address,
      const TokenAccountsFilter.byProgramId(_splProgramId),
      encoding: Encoding.jsonParsed,
    );
    return result.value;
  }

  Future<(Metadata?, String?)> getMetadata(
    String mintAddress, {
    CancelToken? cancelToken,
  }) async {
    Metadata? result;
    String? logo;

    final network = ref.read(solanaSelectedNetworkProvider);
    final client = ref.read(solanaClientProvider);
    result = await client.rpcClient.getMetadata(
      mint: Ed25519HDPublicKey.fromBase58(mintAddress),
    );
    if (result != null) {
      return (result, null);
    }
    final listRes = await _http.post<Map<String, dynamic>>(
      'https://token-list-api.solana.cloud/v1/mints',
      queryParameters: {'chainId': network.id.toString()},
      data: {
        'addresses': [mintAddress],
      },
      cancelToken: cancelToken,
    );
    final item = (listRes.data?['content'] as List?)?.cast<Map>().firstOrNull;
    if (item != null) {
      result = Metadata(
        name: item['name'] as String,
        symbol: item['symbol'] as String,
        mint: item['address'] as String,
        uri: '',
        updateAuthority: '',
      );
      if (item['logoURI'] case final String uri) {
        logo = uri;
      }
    }
    return (result, logo);
  }
}
