import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_utils/me_utils.dart';

import '/internals/box.dart';
import '/internals/methods.dart' show handleExceptions;
import '/models/business.dart';
import '/services/chain_manager.dart';
import 'business.dart' show configStateProvider, fetchNetworksProvider;

// 网络切换状态监听器，在网络切换期间显示加载状态
final networkSwitchingProvider = StateProvider<bool>((ref) => false);

// ChainManager 单例提供者 - 改为autoDispose以支持用户切换
final chainManagerProvider = Provider.autoDispose<ChainManager>((ref) {
  // 确保在provider销毁时重置单例实例
  ref.onDispose(() {
    ChainManager.resetInstance();
  });
  return ChainManager.instance;
});

// 当前钱包地址提供者，依赖于当前网络
final walletAddressProvider = StateProvider.autoDispose<String?>((ref) {
  final chainManager = ref.watch(chainManagerProvider);
  final _ = ref.watch(currentNetworkProvider); // 确保网络变更时重新计算
  return chainManager.walletAddress;
});

// 当前网络提供者 - 改为autoDispose以支持用户切换
final currentNetworkProvider = StateNotifierProvider.autoDispose<CurrentNetworkNotifier, Network?>((ref) {
  return CurrentNetworkNotifier(ref);
});

class CurrentNetworkNotifier extends StateNotifier<Network?> {
  CurrentNetworkNotifier(this.ref) : super(null);

  final Ref ref;

  // 缓存网络ID的键
  static const String CACHE_KEY_NETWORK_ID = 'selected_network_id';

  // 获取上次使用的网络ID
  static int? getLastUsedNetworkId() {
    try {
      final cachedNetworkId = Boxes.cache.get(CACHE_KEY_NETWORK_ID);
      if (cachedNetworkId != null && cachedNetworkId is int) {
        return cachedNetworkId;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
    }
    return null;
  }

  // 自动初始化上次使用的网络或默认网络
  Future<void> initializeLastUsedOrDefaultNetwork() async {
    // 获取网络列表
    await ref.read(configStateProvider.notifier).loadConfig();
    final networks = await ref.read(fetchNetworksProvider.future);
    if (networks.isEmpty) {
      return;
    }

    // 获取可用网络列表
    final networksList = networks;

    // 获取缓存的网络ID
    final cachedNetworkId = getLastUsedNetworkId();

    // 如果有缓存的网络ID，尝试在可用网络中查找
    if (cachedNetworkId != null) {
      final cachedNetwork = networksList.firstWhere(
        (network) => network.id == cachedNetworkId,
        orElse: () => networksList.first,
      );

      await initializeNetwork(cachedNetwork);
    } else {
      // 没有缓存或找不到对应网络，使用第一个网络
      await initializeNetwork(networksList.first);
    }
  }

  // 初始化网络
  Future<void> initializeNetwork(Network network) async {
    try {
      ref.read(networkSwitchingProvider.notifier).state = true;
      await ref.read(chainManagerProvider).initializeChain(network);
      state = network;
      await Boxes.cache.put(CACHE_KEY_NETWORK_ID, network.id);
    } finally {
      ref.read(networkSwitchingProvider.notifier).state = false;
    }
  }

  // 切换网络
  Future<void> switchNetwork(Network network) async {
    if (state?.id == network.id) {
      return;
    }

    try {
      // 设置网络切换状态
      ref.read(networkSwitchingProvider.notifier).state = true;

      // 切换链
      await ref.read(chainManagerProvider).switchChain(network);

      // 更新当前网络状态
      state = network;

      // 保存网络ID到缓存
      await Boxes.cache.put(CACHE_KEY_NETWORK_ID, network.id);

      // 不要在监听了 networkSwitchingProvider 的 provider 内部直接 invalidate
      // 延迟一帧后执行清除
      // Future.microtask(() {
      //   ref.invalidate(filteredTokensProvider);
      //   ref.invalidate(allTokenBalancesProvider);
      // });
    } finally {
      // 延迟状态更新，避免在一个事件循环内同时修改和读取状态
      Future.microtask(() {
        ref.read(networkSwitchingProvider.notifier).state = false;
      });
    }
  }
}

// Token余额Provider - 自动销毁
final tokenBalanceProvider = FutureProvider.autoDispose.family<BigInt, ({String tokenAddress, String walletAddress})>((
  ref,
  params,
) async {
  final chainManager = ref.watch(chainManagerProvider);
  final _ = ref.watch(currentNetworkProvider); // 确保网络变更时重新计算
  return chainManager.getTokenBalance(params.tokenAddress, params.walletAddress);
});

// 原生代币余额Provider - 自动销毁
final nativeBalanceProvider = FutureProvider.autoDispose.family<BigInt, String>((ref, address) async {
  final chainManager = ref.watch(chainManagerProvider);
  final _ = ref.watch(currentNetworkProvider); // 确保网络变更时重新计算
  return chainManager.getNativeBalance(address);
});

// 全局方法：清理所有钱包相关的provider状态
void clearAllWalletProviders(WidgetRef ref) {
  // 清理链管理相关provider
  ref.invalidate(chainManagerProvider);
  ref.invalidate(currentNetworkProvider);
  ref.invalidate(walletAddressProvider);
  ref.invalidate(tokenBalanceProvider);
  ref.invalidate(nativeBalanceProvider);

  // 重置ChainManager单例
  ChainManager.resetInstance();

  LogUtil.d('已清理所有钱包相关的provider状态');
}
