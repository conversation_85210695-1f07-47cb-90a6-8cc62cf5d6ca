import 'dart:io';

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:ndef_record/ndef_record.dart';
import 'package:nfc_manager/nfc_manager.dart';
import 'package:nfc_manager_ndef/nfc_manager_ndef.dart';

@FFRoute(name: '/nfc')
class NfcDemo extends StatefulWidget {
  const NfcDemo({super.key});

  @override
  State<NfcDemo> createState() => _NfcDemoState();
}

enum _CheckState {
  pending,
  invalid,
  // claiming,
  succeed,
  failed,
  duplicated,
}

class _NfcDemoState extends State<NfcDemo> with WidgetsBindingObserver {
  String _nfcStatus = 'Scanning...';
  String _tagData = '';
  bool _isNfcAvailable = false;
  bool _isSessionRunning = false;
  _CheckState _checkState = _CheckState.pending;
  bool _isCheckingRecord = false;
  // Object? _exception;

  final nfc = NfcManager.instance;

  @override
  void initState() {
    super.initState();
    _checkNfcAvailability();
    WidgetsBinding.instance.addObserver(this);
    _polling();
  }

  //  @override
  //  void didChangeAppLifecycleState(AppLifecycleState state) {
  //    switch (state) {
  //      case AppLifecycleState.resumed:
  //        // 添加短延迟确保系统已准备好
  //        Future.delayed(const Duration(milliseconds: 500), () {
  //          _polling();
  //        });
  //      case AppLifecycleState.paused:
  //        _stopPolling();
  //      case AppLifecycleState.inactive:
  //        // 处理短暂中断
  //        _stopPolling();
  //      default:
  //        return;
  //    }
  //  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _stopPolling();
    super.dispose();
  }

  Future<void> _polling() async {
    if (_isSessionRunning) {
      return;
    }

    // 先检查NFC是否可用
    final bool isAvailable = await NfcManager.instance.isAvailable();
    if (!isAvailable) {
      setState(() {
        _isNfcAvailable = false;
        _nfcStatus = 'NFC不可用，请检查设备NFC是否开启';
      });
      return;
    }

    safeSetState(() {
      _isSessionRunning = true;
      _nfcStatus = '正在扫描NFC设备...';
    });

    try {
      // 先尝试停止任何可能正在运行的会话
      await nfc.stopSession().catchError((_) {});

      // 延迟一小段时间确保之前的会话已完全关闭
      await Future.delayed(const Duration(milliseconds: 300));

      await nfc.startSession(
        onDiscovered: _resolveNfcTag,
        pollingOptions: NfcPollingOption.values.toSet(),
        onSessionErrorIos: (error) async {
          LogUtil.e('${error.code} ${error.message}', stackTrace: StackTrace.current);
          safeSetState(() {
            _isSessionRunning = false;
            _nfcStatus = '扫描错误: ${error.message}';
          });

          // 尝试停止会话
          await nfc.stopSession().catchError((_) {});

          if (error.message.contains('Session invalidated by user')) {
            return;
          }
          // 根据错误类型决定是否自动重启会话
          if (error.message.contains('Session invalidated unexpectedly')) {
            await Future.delayed(const Duration(seconds: 1));
            _polling(); // 自动重启会话
          }
          handleExceptions(error: error);
        },
      );

      safeSetState(() {
        _nfcStatus = 'NFC扫描已启动，请靠近NFC卡片';
      });
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      safeSetState(() {
        _isSessionRunning = false;
        _nfcStatus = '启动NFC扫描失败: ${e.toString()}';
      });

      // 确保会话被正确关闭
      await nfc.stopSession().catchError((_) {});
    }
  }

  void _stopPolling() {
    if (!_isSessionRunning) {
      return;
    }
    nfc.stopSession();
    safeSetState(() {
      _isSessionRunning = false;
    });
  }

  // 检查NFC可用性
  Future<void> _checkNfcAvailability() async {
    final isAvailable = await NfcManager.instance.isAvailable();
    setState(() {
      _isNfcAvailable = isAvailable;
      _nfcStatus = isAvailable ? 'NFC available, click to start scanning' : 'Device does not support NFC';
    });
  }

  Future<void> _resolveNfcTag(NfcTag tag) async {
    // ignore: invalid_use_of_protected_member
    LogUtil.d('检测到NFC标签: ${tag.data}');
    if (_isCheckingRecord) {
      return;
    }
    if (_checkState != _CheckState.pending) {
      _changeCheckState(_CheckState.pending);
    }
    safeSetState(() {
      _isCheckingRecord = true;
      _nfcStatus = '正在读取NFC标签...';
    });
    try {
      final ndef = Ndef.from(tag);

      if (ndef == null) {
        LogUtil.w('该NFC标签不支持NDEF格式');
        _changeCheckState(_CheckState.invalid);
        safeSetState(() {
          _tagData = '不支持的NFC标签格式';
          _nfcStatus = '读取失败: 不支持的NFC标签格式';
        });
        return;
      }

      final records = ndef.cachedMessage?.records;

      if (records == null || records.isEmpty) {
        LogUtil.w('NFC标签没有NDEF记录');
        _changeCheckState(_CheckState.invalid);
        safeSetState(() {
          _tagData = '未找到NFC记录数据';
          _nfcStatus = '读取失败: 未找到NFC记录数据';
        });
        return;
      }

      // 停止当前会话
      if (Platform.isIOS || Platform.isMacOS) {
        await nfc.stopSession().catchError((e) {
          LogUtil.d('停止NFC会话出错: $e');
        });
      }

      // 处理读取的数据
      String tagContent = '';
      for (final record in records) {
        if (record.typeNameFormat == TypeNameFormat.wellKnown && String.fromCharCodes(record.type) == 'T') {
          // 文本记录
          final payload = record.payload;
          final languageCodeLength = payload[0] & 0x3f;
          final isUTF16 = (payload[0] & 0x80) != 0;

          final languageCode = String.fromCharCodes(
            payload.sublist(1, 1 + languageCodeLength),
          );

          final textBytes = payload.sublist(1 + languageCodeLength);
          final text = isUTF16 ? String.fromCharCodes(textBytes) : utf8.decode(textBytes);

          tagContent += 'Text: $text (Language: $languageCode)\n';
        } else if (record.typeNameFormat == TypeNameFormat.wellKnown && String.fromCharCodes(record.type) == 'U') {
          // URI记录
          final payload = record.payload;
          final uri = String.fromCharCodes(payload.sublist(1));

          tagContent += 'URI: $uri\n';
        } else {
          // 其他类型记录
          tagContent += '其他数据: ${record.payload}\n';
        }
      }

      if (tagContent.isNotEmpty) {
        _changeCheckState(_CheckState.succeed);
        safeSetState(() {
          _tagData = tagContent;
          _nfcStatus = 'NFC读取成功';
        });
      } else {
        _changeCheckState(_CheckState.invalid);
        safeSetState(() {
          _tagData = '无法解析NFC数据';
          _nfcStatus = '读取失败: 无法解析NFC数据';
        });
      }

      await Future.delayed(const Duration(seconds: 1));
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      _changeCheckState(_CheckState.failed, exception: e);
      safeSetState(() {
        _tagData = '读取NFC标签出错: ${e.toString()}';
        _nfcStatus = '读取失败';
      });
    } finally {
      safeSetState(() {
        _isSessionRunning = false;
        _isCheckingRecord = false;
      });

      // 延迟后自动重新开始扫描
      await Future.delayed(const Duration(seconds: 2));
      _polling();
    }
  }

  void _changeCheckState(_CheckState state, {Object? exception}) {
    if (state == _CheckState.invalid || state == _CheckState.failed) {
      HapticUtil.notifyFailure();
    } else if (state == _CheckState.duplicated) {
      HapticUtil.notifyWarning();
    } else if (state == _CheckState.succeed) {
      HapticUtil.notifySuccess();
    }
    safeSetState(() {
      _checkState = state;
      // _exception = exception;
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      title: 'NFC Card Read/Write',
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _isNfcAvailable ? Icons.nfc : Icons.nfc_outlined,
              size: 80,
              color: _isNfcAvailable ? Colors.green : Colors.grey,
            ),
            const SizedBox(height: 20),
            Text(
              _nfcStatus,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            if (_tagData.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(_tagData, style: const TextStyle(fontSize: 16)),
              ),
            const SizedBox(height: 30),
            // ElevatedButton(
            //   onPressed: _isNfcAvailable ? _startNfcSession : null,
            //   style: ElevatedButton.styleFrom(
            //     backgroundColor: Colors.deepPurple,
            //     padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 15),
            //   ),
            //   child: const Text(
            //     '开始扫描',
            //     style: TextStyle(fontSize: 18, color: Colors.white),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
