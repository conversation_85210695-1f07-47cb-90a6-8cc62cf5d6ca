import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/business.dart';
import '/provider/business.dart';
import '/provider/chain.dart';

class SelectNetwork extends ConsumerWidget {
  const SelectNetwork({super.key});

  Future<void> _switchNetwork(
    BuildContext context,
    WidgetRef ref,
    Network network,
  ) async {
    try {
      // 切换网络 (provider内部会设置 networkSwitchingProvider 状态)
      await AppLoading.run(
        () => ref.read(currentNetworkProvider.notifier).switchNetwork(network),
      );
      Navigator.pop(context, network);
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToSwitchNetwork);
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networks = ref.watch(fetchNetworksProvider);
    final currentNetwork = ref.watch(currentNetworkProvider);
    final isSwitching = ref.watch(networkSwitchingProvider);

    return Padding(
      padding: const EdgeInsets.all(16).copyWith(bottom: 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题与关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Network',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 28),
                onPressed: isSwitching ? null : () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 网络列表
          Expanded(
            child: networks.when(
              loading: () => Center(
                child: CircularProgressIndicator(
                  valueColor: context.themeColor.alwaysStopped,
                ),
              ),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Failed to load networks',
                      style: TextStyle(
                        color: Colors.red[400],
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () {
                        ref.invalidate(fetchNetworksProvider);
                      },
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
              data: (data) => ListView.builder(
                itemCount: data.length,
                itemBuilder: (context, index) {
                  final network = data[index];
                  final isSelected = currentNetwork?.id == network.id;
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: isSwitching ? null : () => _switchNetwork(context, ref, network),
                        borderRadius: BorderRadius.circular(16),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          decoration: BoxDecoration(
                            color: isSelected ? ColorName.themeColorDark.withValues(alpha: 0.1) : Colors.grey[100],
                            borderRadius: BorderRadius.circular(16),
                            border: isSelected ? Border.all(color: ColorName.themeColorDark, width: 1) : null,
                          ),
                          child: Row(
                            children: [
                              // 网络图标
                              Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Stack(
                                  children: [
                                    if (network.iconUrl.isNotEmpty)
                                      MEImage(
                                        network.iconUrl,
                                        clipOval: true,
                                        fit: BoxFit.scaleDown,
                                        alternativeSVG: true,
                                      )
                                    else
                                      const Icon(
                                        Icons.account_balance_wallet,
                                        size: 24,
                                      ),
                                    if (isSwitching && isSelected)
                                      Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: Colors.black.withValues(alpha: 0.3),
                                          borderRadius: BorderRadius.circular(20),
                                        ),
                                        child: const Center(
                                          child: SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                            ),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 12),

                              // 网络名称和信息
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      network.name,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    if (network.testnet)
                                      Text(
                                        'Testnet',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                  ],
                                ),
                              ),

                              // 选中标记
                              if (isSelected && !isSwitching)
                                const Icon(
                                  Icons.check_circle,
                                  color: ColorName.themeColorDark,
                                  size: 30,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
