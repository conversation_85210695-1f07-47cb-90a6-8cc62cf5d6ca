import 'dart:io' show HttpOverrides, Platform, exit;
import 'dart:isolate' show Isolate, RawReceivePort;

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_misc/me_misc.dart' as misc;
import 'package:me_ui/me_ui.dart';
import 'package:me_utils/me_utils.dart';
import 'package:stack_trace/stack_trace.dart' as stack_trace;
import 'app.dart';
import 'constants/envs.dart' as envs;
import 'constants/release.dart';
import 'extensions/riverpod_extension.dart';
import 'feat/link/helper.dart';
import 'firebase_options.dart';
import 'internals/box.dart';
import 'internals/methods.dart' as methods;
import 'internals/riverpod.dart';

void main() async {
  // Handling errors.
  FlutterError.onError = (details) {
    if (kDebugMode) {
      FlutterError.presentError(details);
    } else {
      methods.handleExceptions(details: details);
    }
  };
  if (Release.sealed) {
    ErrorWidget.builder = (_) => const SizedBox.shrink();
  } else {
    MEErrorWidget.takeOver();
  }
  PlatformDispatcher.instance.onError = (e, s) {
    methods.handleExceptions(error: e, stackTrace: s);
    return true;
  };
  Isolate.current.addErrorListener(
    RawReceivePort((pair) async {
      if (pair is! List) {
        return;
      }
      final List<String?> errorAndStacktrace = pair.cast<String?>();
      methods.handleExceptions(
        error: errorAndStacktrace.first,
        stackTrace: StackTrace.fromString(errorAndStacktrace.last ?? ''),
      );
    }).sendPort,
  );
  FlutterError.demangleStackTrace = (StackTrace stack) {
    if (stack is stack_trace.Trace) {
      return stack.vmTrace;
    }
    if (stack is stack_trace.Chain) {
      return stack.toTrace().vmTrace;
    }
    return stack;
  };

  WidgetsFlutterBinding.ensureInitialized();
  misc.hideKeyboard();
  Object? preparingException;
  StackTrace? preparingStackTrace;
  try {
    HttpOverrides.global = misc.MEHttpOverrides();
    ToastUtil.init();
    _configureBotUtil();
    LogUtil.enable = !Release.sealed;
    await Future.wait([
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge),
      DeviceUtil.initDeviceInfo(),
      PackageUtil.initInfo(
        buildTime: Release.buildTime,
        appVersionName: Release.versionName,
        appVersionCode: Release.versionCode,
      ),
      Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform),
      _configureWebView(),
    ]);
    await Boxes.init();
    AppLinkHelper.register();
    // 推送服务将通过provider自动初始化
  } catch (e, s) {
    preparingException = e;
    preparingStackTrace = s;
    if (e is RefCanceledException) {
      return;
    }
    await BotUtil.reportToBot(message: e, stackTrace: s);
  }
  if (preparingException != null) {
    if (kDebugMode) {
      final details = FlutterErrorDetails(
        exception: preparingException,
        stack: preparingStackTrace,
      );
      FlutterError.reportError(details);
      runApp(ProviderScope(child: MEErrorWidget(details)));
      return;
    }
    exit(1);
  }

  runApp(
    const ProviderScope(
      observers: kDebugMode ? [_riverpodLogger] : null,
      child: Card3App(),
    ),
  );
}

const _riverpodLogger = RiverpodLogger(
  loggingWhen: {
    RiverpodLoggingWhen.error,
  },
);

void _configureBotUtil() {
  final env = envs.envActive;
  BotUtil.url = env.BOT_UTIL_URL;
  BotUtil.key = env.BOT_UTIL_KEY;
  BotUtil.buildLogInfo = (buffer) {
    buffer.writeln(
      '[🧊]: ${Release.commitRef} '
      '(${env.env}${envs.isAuditing ? '-auditing' : ''})',
    );
    if (BoxService.getUserInfo() case final user?) {
      buffer.writeln('[🧑‍💻]: ${user.userEmail} (${user.name})');
    }
  };
}

Future<void> _configureWebView() async {
  PlatformInAppWebViewController.debugLoggingSettings.enabled = !Release.sealed;
  await InAppWebViewController.setJavaScriptBridgeName('card3_bridge');
  if (!Platform.isAndroid) {
    return;
  }
  await InAppWebViewController.setWebContentsDebuggingEnabled(!Release.sealed);
  final swAvailable = await WebViewFeature.isFeatureSupported(
    WebViewFeature.SERVICE_WORKER_BASIC_USAGE,
  );
  final swInterceptAvailable = await WebViewFeature.isFeatureSupported(
    WebViewFeature.SERVICE_WORKER_SHOULD_INTERCEPT_REQUEST,
  );
  if (swAvailable && swInterceptAvailable) {
    ServiceWorkerController.instance().setServiceWorkerClient(
      ServiceWorkerClient(
        shouldInterceptRequest: (request) async {
          LogUtil.d(
            'SW request: $request',
            tag: '🌐 WebView',
            tagWithTrace: false,
          );
          return null;
        },
      ),
    );
  }
}
