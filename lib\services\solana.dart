import 'dart:convert';

import 'package:me_utils/me_utils.dart';
import 'package:solana/dto.dart';
import 'package:solana/solana.dart';

import '../internals/methods.dart' show handleExceptions;
import '../internals/privy.dart' show privyClient;

class SolanaService {
  RpcClient? _client;
  String? _walletAddress;

  // 初始化 Solana 客户端
  Future<void> initialize(String rpcUrl) async {
    _client = RpcClient(rpcUrl);
    await privyClient.awaitReady();
    _walletAddress = privyClient.user?.embeddedSolanaWallets.firstOrNull?.address;
    LogUtil.d('Solana Service Initialized: rpcUrl=$rpcUrl, walletAddress=$_walletAddress');
  }

  // 获取钱包地址
  String? get walletAddress => _walletAddress;

  // 获取代币余额
  Future<BigInt> getTokenBalance(String tokenAddress, String walletAddress) async {
    try {
      LogUtil.d(
        '获取 Solana 代币余额: '
        'tokenAddress=$tokenAddress, '
        'walletAddress=$walletAddress',
      );

      // 检查钱包地址是否为空
      if (walletAddress.isEmpty) {
        return BigInt.zero;
      }

      // 使用 getTokenAccountsByOwner 方法获取代币账户，使用 Base64 编码
      final accounts = await _client!.getTokenAccountsByOwner(
        walletAddress,
        TokenAccountsFilter.byMint(tokenAddress),
        encoding: Encoding.base64,
      );

      if (accounts.value.isEmpty) {
        return BigInt.zero;
      }

      final balance = await _client!.getTokenAccountBalance(accounts.value.first.pubkey);
      return BigInt.parse(balance.value.amount);
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  // 获取原生代币余额
  Future<BigInt> getNativeBalance(String address) async {
    try {
      final pubKey = Ed25519HDPublicKey.fromBase58(address);
      final balance = await _client!.getBalance(
        pubKey.toBase58(),
      );
      return BigInt.parse(balance.value.toString());
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  Future<String> sendNativeTransaction(String to, BigInt value) async {
    LogUtil.d('Send Solana Native Token Transaction: to=$to, value=$value');

    if (_walletAddress == null || _walletAddress!.isEmpty) {
      throw StateError('Wallet address is empty');
    }

    final solanaWallet = privyClient.user?.embeddedSolanaWallets.firstOrNull;
    if (solanaWallet == null) {
      throw StateError('Failed to get Solana wallet');
    }

    final fromPublicKey = Ed25519HDPublicKey.fromBase58(_walletAddress!);
    final toPublicKey = Ed25519HDPublicKey.fromBase58(to);

    final instruction = SystemInstruction.transfer(
      fundingAccount: fromPublicKey,
      recipientAccount: toPublicKey,
      lamports: value.toInt(),
    );

    final recentBlockhash = await _client!.getLatestBlockhash();
    final message = Message.only(instruction);
    final compiledMessage = message.compile(
      recentBlockhash: recentBlockhash.value.blockhash,
      feePayer: fromPublicKey,
    );

    final serializedMessage = compiledMessage.toByteArray().toList();
    final serializedTransaction = base64Encode(serializedMessage);

    final result = await solanaWallet.provider.signMessage(serializedTransaction);

    // Handle the result
    result.fold(
      onSuccess: (signature) async {
        try {
          final txHash = await _client!.sendTransaction(signature);
          LogUtil.d('Transaction sent with hash: $txHash');
        } catch (e, s) {
          LogUtil.e(e, stackTrace: s);
        }
      },
      onFailure: (error) {
        handleExceptions(error: error, stackTrace: StackTrace.current);
      },
    );
    return '';
  }

  // 发送 SPL 代币交易
  Future<String> sendTokenTransaction(String tokenAddress, String to, BigInt value) async {
    LogUtil.d(
      '发送 Solana SPL 代币交易: '
      'tokenAddress=$tokenAddress, to=$to, value=$value',
    );

    // 检查钱包地址是否为空
    if (_walletAddress == null || _walletAddress!.isEmpty) {
      throw StateError('Empty wallet address');
    }

    // 获取用户的 Solana 钱包
    final solanaWallet = privyClient.user?.embeddedSolanaWallets.firstOrNull;
    if (solanaWallet == null) {
      throw StateError('No Solana wallet found');
    }

    // 创建发送方和接收方的公钥
    final fromPublicKey = Ed25519HDPublicKey.fromBase58(_walletAddress!);
    final toPublicKey = Ed25519HDPublicKey.fromBase58(to);
    // final tokenPublicKey = Ed25519HDPublicKey.fromBase58(tokenAddress);

    // 查找发送者的代币账户
    final fromTokenAccounts = await _client!.getTokenAccountsByOwner(
      fromPublicKey.toBase58(),
      TokenAccountsFilter.byMint(tokenAddress),
      encoding: Encoding.base64,
    );

    if (fromTokenAccounts.value.isEmpty) {
      throw StateError('No token account found for sender');
    }

    // 查找或创建接收者的代币账户
    // final toTokenAccounts = await _client!.getTokenAccountsByOwner(
    //   toPublicKey.toBase58(),
    //   TokenAccountsFilter.byMint(tokenAddress),
    //   encoding: Encoding.base64,
    // );

    // 如果接收者没有代币账户，需要先创建一个（这部分需要在实际应用中实现）

    // SPL 代币转账指令创建（简化版本，实际应用中需要根据 spl-token 库实现）
    // 参考 Solana 库中的 SPL Token 指令

    // 实际实现需要根据 Privy SDK 调整
    LogUtil.d(
      '模拟发送 SPL 代币: '
      '${fromPublicKey.toBase58()} -> ${toPublicKey.toBase58()}, '
      '代币: $tokenAddress, '
      '金额: ${value.toString()}',
    );
    return '模拟 SPL 代币交易哈希';
  }

  // 向后兼容的交易方法
  Future<String> sendTransaction(String to, BigInt value) {
    return sendNativeTransaction(to, value);
  }

  // 关闭连接
  Future<void> dispose() async {
    _client = null;
  }
}
