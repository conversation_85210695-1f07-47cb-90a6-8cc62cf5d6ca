// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ApiServiceUserToken _$ApiServiceUserTokenFromJson(Map json) =>
    _ApiServiceUserToken(
      address: json['tokenAddress'] as String,
      totalQuantity: Decimal.fromJson(json['totalQuantity'] as String),
      costAverage: Decimal.fromJson(json['averageCost'] as String),
      costTotal: Decimal.fromJson(json['totalCost'] as String),
      costAccumulate: Decimal.fromJson(json['accuCost'] as String),
      costQuoteAccumulate: Decimal.fromJson(json['accuQuoteCost'] as String),
      pnlRealized: Decimal.fromJson(json['realizedPnl'] as String),
      pnlUnrealized: Decimal.fromJson(json['unrealizedPnl'] as String),
      pnlTotal: Decimal.fromJson(json['totalPnl'] as String),
      pnlQuoteTotal: Decimal.fromJson(json['quoteTotalPnl'] as String),
    );

Map<String, dynamic> _$ApiServiceUserTokenToJson(
  _ApiServiceUserToken instance,
) => <String, dynamic>{
  'tokenAddress': instance.address,
  'totalQuantity': instance.totalQuantity.toJson(),
  'averageCost': instance.costAverage.toJson(),
  'totalCost': instance.costTotal.toJson(),
  'accuCost': instance.costAccumulate.toJson(),
  'accuQuoteCost': instance.costQuoteAccumulate.toJson(),
  'realizedPnl': instance.pnlRealized.toJson(),
  'unrealizedPnl': instance.pnlUnrealized.toJson(),
  'totalPnl': instance.pnlTotal.toJson(),
  'quoteTotalPnl': instance.pnlQuoteTotal.toJson(),
};
