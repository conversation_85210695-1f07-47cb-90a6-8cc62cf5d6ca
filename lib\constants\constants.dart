import 'package:collection/collection.dart';

const tokenSOLAddress = 'So11111111111111111111111111111111111111111';
const tokenSOLWrappedAddress = 'So11111111111111111111111111111111111111112';

const globalDeepCollectionEquality = DeepCollectionEquality();

enum CustomizeViewType { start, main, confirm, pay, submitted }
// // 链特定的token过滤配置
// const chainTokenFilters = {
//   // 以太坊 (chainId: 1)
//   '1': ['ETH', 'USDC', 'USDT'],
//   // Base Mainnet (chainId: 8453)

//   '8453': ['ETH', 'USDT', 'USDC', 'DEGEN'],

//   // Polygon (chainId: 137)
//   '137': ['POL', 'USDC', 'USDT'],

//   // BSC (chainId: 56)
//   '56': ['BNB', 'BUSD', 'WBNB'],

//   // Arbitrum (chainId: 42161)
//   '42161': ['ETH', 'USDC', 'USDT'],

//   // Optimism (chainId: 10)
//   '10': ['ETH', 'USDC', 'USDT'],

//   // Avalanche (chainId: 43114)
//   '43114': ['ETH', 'USDC',  'USDT'],

//   // Linea (chainId: 59144)
//   '59144': ['ETH', 'USDC',  'USDT'],

//   // 其他链不过滤，显示所有token

// };

/// Toast消息管理类
/// 统一管理应用中所有的Toast消息
class ToastMessages {
  // 登录相关
  static const String loginTermsRequired = 'Please agree to the Terms and Privacy Policy';
  static const String creatingWallet = 'Creating your wallet...';
  static const String creatingWalletFailed = 'Failed to create wallet. Please try again later.';
  static const String failedToSendEmailCode = 'Failed to send login code. Please try again.';
  static const String invalidOrExpiredLoginCode = 'Invalid or expired login code';
  static const String emailCodeResent = 'The login code has been sent';
  static const String failedToResendEmailCode = 'Failed to send login code. Please try again.';
  
  // 卡片相关
  static const String cardAlreadyActivated = 'Oops! It is already activated';
  static const String invalidCardFormat = 'Invalid data format';
  static const String cardActivatedSuccessfully = 'Activation complete';
  
  // 定制相关
  static const String failedToGetCoverInfo = 'Data error. Please try again.';
  static const String pleaseUploadYourPicture = 'Upload your profile picture';
  static const String failedToProcessImage = 'Failed to process image. Please check the format (JPG or PNG) and size (<10 MB).';
  static const String failedToCropImage = 'Crop failed';
  
  // 社交相关
  static const String failedToSubmit = 'Failed to submit. Please try again later.';
  static const String failedToClear = 'Failed to clear data. Please try again later.';
  static const String roleUpdatedSuccessfully = 'Updated';
  static const String topicsUpdatedSuccessfully = 'Updated';
  
  // 个人资料相关
  static const String profileUpdatedSuccessfully = 'Updated';
  static const String failedToUpdateProfile = 'Failed to update. Please try again.';
  static const String profileCleared = 'Profile data cleared';
  static const String failedToClearProfile = 'Failed to clear profile. Please try again.';
  static const String uploadAvatarSuccess = 'Upload successful';
  static const String uploadAvatarError = 'Failed to upload avatar. Please try again.';
  static const String failedToOpenImagePicker = 'Unable to select image';
  
  // 账户相关
  static const String deleted = 'Account deleted';
  static const String deleteAccountFailed = 'Failed to delete account. Please try again.';
  static const String modeChangedSuccessfully = 'Updated';
  static const String failedToChangeMode = 'Failed to change mode. Please try again.';
  
  // 网络相关
  static const String failedToSwitchNetwork = 'Failed to switch network';
  static const String failedToLoadNotifications = 'Failed to load notifications';
  static const String failedToLoadMore = 'Failed to load more';
  
  // 分享相关
  static const String urlCopied = 'Copied';
  static const String referralLinkCopied = 'Copied';
  static const String copied = 'Copied';

  // ethcc
  static const String failedToUpdateIdentities = 'Failed to update. Please try again.';
  static const String failedToUpdateTopics = 'Failed to update. Please try again.';
  static const String failedToCreateCardCover = 'Failed to upload avatar. Please try again.';
  
  // 通知相关（中文）
  static const String tokenCopied = 'Token已复制';
  static const String notificationPermissionDenied = '通知权限未授权，请前往设置开启';
  static const String localNotificationSent = '本地通知已发送 📱';
  static const String delayedNotificationSet = '延迟通知已设置，5秒后将收到通知 ⏰';
  static const String notificationPermissionDeniedManual = '❌ 通知权限未授权，请手动前往设置开启';
  static const String notificationPermissionGranted = '✅ 通知权限已授权，可以正常接收通知';
  static const String iosNotificationPermissionDenied = '❌ iOS通知权限未授权';
  static const String iosDebugCompleted = '✅ iOS调试完成，请检查是否收到通知';
  static const String syncingTokenToServer = '🔄 正在同步Token到服务器...';
  static const String tokenSyncSuccess = '✅ Token同步成功';
  static const String debugInfoCopied = '调试信息已复制';
  
  // 动态错误消息生成方法
  static String creatingWalletFailedWithDetails(String error) {
    final shortError = error.length > 50 ? '${error.substring(0, 50)}...' : error;
    return 'Creating wallet failed: $shortError';
  }
  
  static String loginFailed(String error) => 'Login failed: $error';
  static String activationError(String error) => 'Activation error: $error';
  static String couldNotLaunch(String url) => 'Could not launch $url';
  static String setDelayedNotificationFailed(String error) => '设置延迟通知失败: $error';
  static String debugFailed(String error) => '调试失败: $error';
  static String tokenDebugFailed(String error) => 'Token调试失败: $error';
  static String tokenSyncFailed(String error) => '❌ Token同步失败: $error';
  static String errorMessage(String error) => error;
}
