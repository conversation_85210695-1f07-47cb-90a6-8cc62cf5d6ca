import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

class Card3ToastUtil {
  static void showToast({
    required String message,
    Duration? duration,
    SmartToastType? toastType = SmartToastType.last,
  }) {
    SmartDialog.showToast(
      message,
      displayTime: duration ?? const Duration(seconds: 2),
      displayType: toastType,
      alignment: Alignment.center,
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        decoration: BoxDecoration(
          color: const Color(0xFF222222),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          message,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
