import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/models/card.dart';
import '/provider/api.dart';
import '/provider/card.dart';
import '/ui/widgets/common/error_widget.dart';
import '/ui/widgets/social/data.dart';

// 推荐主题列表
const List<String> RECOMMENDED_TOPICS = ['ZK', 'DeFi', 'Layer2'];
// 推荐身份列表
const List<String> RECOMMENDED_IDENTITIES = [
  'Developer',
  'Investor',
  'Founder',
  'CEO',
  'Engineer',
  'Designer',
  'Product Manager',
  'Entrepreneur',
];

class TopicWidget extends ConsumerStatefulWidget {
  const TopicWidget({super.key});

  @override
  ConsumerState<TopicWidget> createState() => _TopicWidgetState();
}

class _TopicWidgetState extends ConsumerState<TopicWidget> {
  List<String> selectedTopics = [];
  List<String> customTopics = [];
  List<String> selectedIdentities = [];
  List<String> customIdentities = [];
  final TextEditingController _customTopicController = TextEditingController();

  // 添加标记来防止在用户操作后被服务器数据覆盖
  bool _hasUserModifiedTopics = false;
  bool _hasUserModifiedIdentities = false;

  @override
  void dispose() {
    _customTopicController.dispose();
    super.dispose();
  }

  // 重置修改标记的方法，用于重新从服务器加载数据
  // void _resetModificationFlags() {
  //   _hasUserModifiedTopics = false;
  //   _hasUserModifiedIdentities = false;
  // }

  @override
  Widget build(BuildContext context) {
    final result = ref.watch(fetchEthccProfileProvider());
    return AnimatedSize(
      duration: kThemeAnimationDuration,
      curve: Curves.easeInOutCubic,
      child: result.when(
        loading: () => const SizedBox(height: 8.0),
        error: (error, stack) => NetworkErrorWidget(
          onRefresh: () => ref.invalidate(fetchEthccProfileProvider),
        ),
        data: (profile) {
          // 初始化选中的主题，处理空值情况 - 只有在用户没有手动修改过的情况下才从服务器加载
          if (!_hasUserModifiedTopics && selectedTopics.isEmpty && profile != null) {
            final topics = profile.topics;
            if (topics.isNotEmpty) {
              selectedTopics = topics.toList();
            }
          }

          // 初始化选中的身份，处理空值情况 - 只有在用户没有手动修改过的情况下才从服务器加载
          if (!_hasUserModifiedIdentities && selectedIdentities.isEmpty && profile != null) {
            final roles = profile.roles;
            if (roles.isNotEmpty) {
              selectedIdentities = roles.toList();
            }
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildIdentitySection(profile),
                const SizedBox(height: 8),
                _buildTalkToMeAboutSection(profile),
                const SizedBox(height: 8),
                _buildGithubSection(profile),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildIdentitySection(EthccProfile? profile) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        color: context.theme.cardColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Text(
                  'I am a/an...',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () => _showIdentitiesBottomSheet(),
                child: Container(
                  width: 36,
                  height: 36,
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: ColorName.primaryTextColorLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Icon(Icons.add, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (selectedIdentities.isNotEmpty)
            GestureDetector(
              onTap: () => _showIdentitiesBottomSheet(),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: selectedIdentities
                    .map(
                      (identity) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: ColorName.themeColorDark,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          identity,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            )
          else
            GestureDetector(
              onTap: () => _showIdentitiesBottomSheet(),
              child: Text(
                'Developer, Investor, Founder...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTalkToMeAboutSection(EthccProfile? profile) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        color: context.theme.cardColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: Text(
                  'Talk to Me About',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                ),
              ),
              GestureDetector(
                onTap: _showTopicsBottomSheet,
                child: Container(
                  width: 36,
                  height: 36,
                  padding: const EdgeInsets.all(6.0),
                  decoration: BoxDecoration(
                    color: ColorName.primaryTextColorLight,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Icon(Icons.add, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (selectedTopics.isNotEmpty)
            GestureDetector(
              onTap: _showTopicsBottomSheet,
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children: selectedTopics
                    .map(
                      (topic) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: ColorName.themeColorDark,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          topic,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            )
          else
            GestureDetector(
              onTap: _showTopicsBottomSheet,
              child: Text(
                'ZK, DeFi, Layer2...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildGithubSection(EthccProfile? profile) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
        color: context.theme.cardColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Github Contribution',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF333333),
                ),
              ),
              if (profile?.githubHandle.isNotEmpty != true)
                GestureDetector(
                  onTap: _navigateToGithubEdit,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: ColorName.primaryTextColorLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          GestureDetector(
            onTap: _navigateToGithubEdit,
            child: Container(
              height: 50,
              padding: const EdgeInsets.symmetric(vertical: 2),
              decoration: BoxDecoration(
                color: profile?.githubHandle.isNotEmpty == true ? ColorName.primaryTextColorLight : Colors.white,
                borderRadius: BorderRadius.circular(16),
                // border: profile?.githubHandle.isEmpty == true ? Border.all(color: Colors.grey[300]!, width: 1) : null,
              ),
              child: Row(
                children: [
                  if (profile?.githubHandle.isNotEmpty == true)
                    Container(
                      height: 32,
                      width: 32,
                      margin: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: ColorName.listColorDark,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Transform.scale(
                        scale: 0.6,
                        child: SocialPlatform.values
                            .where((e) => e.name == SocialName.github)
                            .first
                            .run((it) => SocialSvgIcon(platform: it)),
                      ),
                    )
                  else
                    SocialSvgIcon(
                      platform: SocialPlatform.github,
                      size: 20,
                      color: Colors.grey[600],
                    ),
                  Expanded(
                    child: Text(
                      profile?.githubHandle.isNotEmpty == true ? profile!.githubHandle : ' Add GitHub username',
                      style: TextStyle(
                        color: profile?.githubHandle.isNotEmpty == true ? Colors.white : Colors.grey[600],
                        fontSize: profile?.githubHandle.isNotEmpty == true ? 18 : 16,
                        fontWeight: profile?.githubHandle.isNotEmpty == true ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showIdentitiesBottomSheet() {
    // 创建临时选择列表，从当前实际状态开始（不是从空开始）
    final List<String> tempSelectedIdentities = List.from(selectedIdentities);
    final List<String> tempCustomIdentities = List.from(customIdentities);

    bool isSubmitting = false;

    showModalBottomSheet(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.8,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) {
          return Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'I am a/an...',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, size: 28),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: Consumer(
                    builder: (context, ref, child) {
                      final ethccRolesAsync = ref.watch(fetchEthccRolesProvider);
                      return ethccRolesAsync.when(
                        loading: () => const Center(child: CircularProgressIndicator()),
                        error: (error, stack) => NetworkErrorWidget(
                          onRefresh: () => ref.invalidate(fetchEthccRolesProvider),
                        ),
                        data: (roles) {
                          final allIdentities = {
                            ...roles.map((r) => r),
                            ...selectedIdentities,
                            ...tempCustomIdentities,
                          }.toList();

                          return _buildSelectionGrid(
                            items: allIdentities,
                            selectedItems: tempSelectedIdentities,
                            customButtonText: '+ Custom',
                            onItemTap: (item) {
                              setModalState(() {
                                if (tempSelectedIdentities.contains(item)) {
                                  tempSelectedIdentities.remove(item);
                                } else {
                                  tempSelectedIdentities.add(item);
                                }
                              });
                            },
                            onCustomTap: () => _showCustomItemDialog(
                              title: 'Custom Role',
                              hintText: 'Enter custom role',
                              onCustomItemAdded: (customIdentity) {
                                setModalState(() {
                                  tempCustomIdentities.add(customIdentity);
                                  tempSelectedIdentities.add(customIdentity);
                                });
                              },
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
                const SizedBox(height: 20),
                _buildSubmitButton(
                  isSubmitting: isSubmitting,
                  onSubmit: () async {
                    setModalState(() {
                      isSubmitting = true;
                    });

                    setState(() {
                      selectedIdentities = tempSelectedIdentities;
                      customIdentities = tempCustomIdentities;
                      _hasUserModifiedIdentities = true; // 标记用户已修改
                    });

                    try {
                      final serviceApi = ref.read(apiServiceProvider);

                      // 提交身份数据
                      final roleToSubmit = tempSelectedIdentities.join(',');
                      await serviceApi.updateEthccRole(role: roleToSubmit);

                      // 立即更新本地状态，确保UI同步
                      setState(() {
                        selectedIdentities = List.from(tempSelectedIdentities);
                        customIdentities = List.from(tempCustomIdentities);
                        _hasUserModifiedIdentities = true; // 标记用户已修改
                      });

                      // 等待服务器数据同步
                      await Future.delayed(const Duration(milliseconds: 300));

                      // 强制清除所有相关的provider缓存
                      ref.invalidate(fetchEthccProfileProvider);
                      ref.invalidate(fetchEthccRolesProvider);

                      if (mounted) {
                        Navigator.pop(context);
                        Card3ToastUtil.showToast(
                          message: ToastMessages.roleUpdatedSuccessfully,
                        );
                      }
                    } catch (e) {
                      // 如果API调用失败，恢复原来的状态
                      setModalState(() {
                        isSubmitting = false;
                      });
                      if (mounted) {
                        Card3ToastUtil.showToast(
                          message: ToastMessages.failedToUpdateIdentities,
                        );
                      }
                    }
                  },
                ),
              ],
            ),
          );
        },
      ),
    ).then((_) {
      // 弹窗关闭时重置自定义身份列表
      setState(() {
        customIdentities.clear();
      });
    });
  }

  void _showTopicsBottomSheet() {
    // 创建临时选择列表，从当前实际状态开始（不是从空开始）
    final List<String> tempSelectedTopics = List.from(selectedTopics);
    final List<String> tempCustomTopics = List.from(customTopics);

    // 在StatefulBuilder外部定义loading状态
    bool isSubmitting = false;

    showModalBottomSheet(
      context: context,
      scrollControlDisabledMaxHeightRatio: 0.8,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) {
          return Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Select topics',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close, size: 28),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: Consumer(
                    builder: (context, ref, child) {
                      final ethccTopicsAsync = ref.watch(fetchEthccTopicsProvider);
                      return ethccTopicsAsync.when(
                        loading: () => const Center(child: CircularProgressIndicator()),
                        error: (error, stack) => NetworkErrorWidget(
                          onRefresh: () => ref.invalidate(fetchEthccTopicsProvider),
                        ),
                        data: (topics) {
                          final allTopics = {
                            ...topics.map((t) => t),
                            ...selectedTopics,
                            ...tempCustomTopics,
                          }.toList();

                          return _buildSelectionGrid(
                            items: allTopics,
                            selectedItems: tempSelectedTopics,
                            customButtonText: '+ Custom Topic',
                            onItemTap: (item) {
                              setModalState(() {
                                if (tempSelectedTopics.contains(item)) {
                                  tempSelectedTopics.remove(item);
                                } else {
                                  tempSelectedTopics.add(item);
                                }
                              });
                            },
                            onCustomTap: () => _showCustomItemDialog(
                              title: 'Custom Topic',
                              hintText: 'Enter custom topic',
                              onCustomItemAdded: (customTopic) {
                                setModalState(() {
                                  tempCustomTopics.add(customTopic);
                                  tempSelectedTopics.add(customTopic);
                                });
                              },
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
                const SizedBox(height: 20),
                _buildSubmitButton(
                  isSubmitting: isSubmitting,
                  onSubmit: () async {
                    setModalState(() {
                      isSubmitting = true;
                    });

                    // 只有点击提交时才更新真正的 selectedTopics 和 customTopics
                    setState(() {
                      selectedTopics = tempSelectedTopics;
                      customTopics = tempCustomTopics;
                      _hasUserModifiedTopics = true; // 标记用户已修改
                    });

                    try {
                      final serviceApi = ref.read(apiServiceProvider);

                      // 提交主题数据
                      final topicsToSubmit = tempSelectedTopics.join(',');
                      await serviceApi.updateEthccTopics(topics: topicsToSubmit);

                      // 立即更新本地状态，确保UI同步
                      setState(() {
                        selectedTopics = List.from(tempSelectedTopics);
                        customTopics = List.from(tempCustomTopics);
                        _hasUserModifiedTopics = true; // 标记用户已修改
                      });

                      // 等待服务器数据同步
                      await Future.delayed(const Duration(milliseconds: 300));

                      // 强制清除所有相关的provider缓存
                      ref.invalidate(fetchEthccProfileProvider);
                      ref.invalidate(fetchEthccTopicsProvider);

                      if (mounted) {
                        Navigator.pop(context);
                        Card3ToastUtil.showToast(
                          message: ToastMessages.topicsUpdatedSuccessfully,
                        );
                      }
                    } catch (e) {
                      // 如果API调用失败，恢复原来的状态
                      setModalState(() {
                        isSubmitting = false;
                      });
                      if (mounted) {
                        Card3ToastUtil.showToast(
                          message: ToastMessages.failedToUpdateTopics,
                        );
                      }
                    }
                  },
                ),
              ],
            ),
          );
        },
      ),
    ).then((_) {
      // 弹窗关闭时重置自定义主题列表
      setState(() {
        customTopics.clear();
      });
    });
  }

  // 通用的选择网格组件
  Widget _buildSelectionGrid({
    required List<String> items,
    required List<String> selectedItems,
    required String customButtonText,
    required Function(String) onItemTap,
    required VoidCallback onCustomTap,
  }) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 2.7,
      ),
      itemCount: items.length + 1, // +1 for custom button
      itemBuilder: (context, index) {
        if (index == items.length) {
          return _buildCustomButton(
            text: customButtonText,
            onTap: onCustomTap,
          );
        }

        final item = items[index];
        final isSelected = selectedItems.contains(item);

        return RippleTap(
          onTap: () => onItemTap(item),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          alignment: Alignment.center,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: isSelected ? const Color(0xFF8858FF) : Colors.grey[100]!,
              width: 2,
            ),
          ),
          color: isSelected ? const Color(0xFF8858FF).withValues(alpha: 0.5) : Colors.grey[100],
          child: Text(
            item,
            style: const TextStyle(
              color: Color(0xFF333333),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  // 通用的自定义按钮组件
  Widget _buildCustomButton({
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.grey[100]!,
            width: 2,
          ),
        ),
        child: Center(
          child: Text(
            text,
            style: const TextStyle(
              color: Color(0xFF333333),
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // 通用的提交按钮组件
  Widget _buildSubmitButton({
    required bool isSubmitting,
    required VoidCallback onSubmit,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isSubmitting ? null : onSubmit,
        style: ElevatedButton.styleFrom(
          backgroundColor: isSubmitting ? Colors.grey[300] : ColorName.themeColorDark,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: isSubmitting
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2.0,
                ),
              )
            : const Text(
                'Submit',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  // 通用的自定义项目对话框
  void _showCustomItemDialog({
    required String title,
    required String hintText,
    required Function(String) onCustomItemAdded,
  }) {
    final customItemController = TextEditingController();
    bool isValid = false;
    String errorText = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          return AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: Text(title),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: customItemController,
                  maxLength: 36,
                  onChanged: (value) {
                    setDialogState(() {
                      final trimmedValue = value.trim();
                      if (trimmedValue.isEmpty) {
                        isValid = false;
                        errorText = 'Required';
                      } else if (trimmedValue.length > 36) {
                        isValid = false;
                        errorText = 'Max 36 characters';
                      } else {
                        isValid = true;
                        errorText = '';
                      }
                    });
                  },
                  decoration: InputDecoration(
                    hintText: hintText,
                    filled: true,
                    fillColor: Colors.grey[100],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide.none,
                    ),
                    errorText: errorText.isNotEmpty ? errorText : null,
                    counterText: '${customItemController.text.length}/36',
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: isValid
                    ? () {
                        final customItem = customItemController.text.trim();
                        onCustomItemAdded(customItem);
                        Navigator.pop(context);
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isValid ? ColorName.themeColorDark : Colors.grey[300],
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Add',
                  style: TextStyle(color: isValid ? Colors.white : Colors.grey[600]),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _navigateToGithubEdit() {
    // 跳转到social页面，传递ethcc_github参数和平台信息
    final profile = ref.read(fetchEthccProfileProvider()).valueOrNull;
    Navigator.of(context).pushNamed(
      Routes.social.name,
      arguments: Routes.social.d(
        action: 'ethcc_github',
        platform: SocialPlatform.github,
        currentHandle: profile?.githubHandle,
      ),
    );
  }
}
