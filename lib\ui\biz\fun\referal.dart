import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '/internals/date_time.dart';
import '/models/business.dart';
import '/provider/api.dart';
import '/provider/user.dart';

// 推荐记录的状态管理
final referralLogsProvider = StateNotifierProvider<ReferralLogsNotifier, AsyncValue<List<ReferralLog>>>((ref) {
  return ReferralLogsNotifier(ref);
});

class ReferralLogsNotifier extends StateNotifier<AsyncValue<List<ReferralLog>>> {
  ReferralLogsNotifier(this.ref) : super(const AsyncValue.loading()) {
    loadReferralLogs();
  }

  final Ref ref;
  int _currentPage = 1;
  final int _pageSize = 20;
  bool _hasMoreData = true;

  Future<void> loadReferralLogs({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _hasMoreData = true;
    }

    if (!_hasMoreData) {
      return;
    }

    if (_currentPage == 1) {
      state = const AsyncValue.loading();
    } else {
      state = AsyncValue.data([...state.valueOrNull ?? []]);
    }

    try {
      final apiService = ref.read(apiServiceProvider);
      final logsPage = await apiService.getReferralLogs(
        pageNum: _currentPage,
        pageSize: _pageSize,
      );

      final newLogs = logsPage.list;

      if (_currentPage == 1) {
        state = AsyncValue.data(newLogs);
      } else {
        // 合并现有数据和新数据
        final currentLogs = state.valueOrNull ?? [];
        state = AsyncValue.data([...currentLogs, ...newLogs]);
      }

      _hasMoreData = newLogs.length >= _pageSize;
      _currentPage++;
    } catch (e, stackTrace) {
      if (_currentPage == 1) {
        state = AsyncValue.error(e, stackTrace);
      }
    }
  }
}

@FFRoute(name: '/fun/referal')
class Referal extends ConsumerStatefulWidget {
  const Referal({super.key});

  @override
  ConsumerState<Referal> createState() => _ReferalState();
}

class _ReferalState extends ConsumerState<Referal> {
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      // 滚动到底部前200像素时加载更多
      ref.read(referralLogsProvider.notifier).loadReferralLogs();
    }
  }

  // 显示二维码弹窗
  void _showQRCodeDialog(String link) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorName.cardColorDark,
      clipBehavior: Clip.antiAlias,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (context) => Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 40, 20, 40),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'My Referral Link',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 32),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.all(16),
                  child: QrImageView(
                    data: link,
                    version: QrVersions.auto,
                    size: 200.0,
                    backgroundColor: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        link,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: link));
                        Card3ToastUtil.showToast(message: ToastMessages.urlCopied);
                      },
                      icon: const Icon(
                        Icons.copy_rounded,
                        color: Colors.amber,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 关闭按钮
          Positioned(
            right: 8,
            top: 8,
            child: IconButton(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(
                Icons.close,
                color: Colors.white,
                size: 28,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final referralLogsState = ref.watch(referralLogsProvider);
    final userInfo = ref.watch(fetchUserInfoProvider());
    final link = '$envUrlCard3?referral=${userInfo.valueOrNull?.referralCode ?? ''}';

    return AppScaffold(
      backgroundColor: Colors.black,
      body: RefreshIndicator(
        onRefresh: () => ref.read(referralLogsProvider.notifier).loadReferralLogs(refresh: true),
        child: ListView(
          controller: _scrollController,
          padding: const EdgeInsets.symmetric(horizontal: 4.0),
          children: [
            // 推荐链接部分
            const Text(
              'My Referral Link',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 24,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              userInfo.valueOrNull?.referralCode ?? '',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 36,
                fontWeight: FontWeight.bold,
                letterSpacing: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // 复制链接和二维码按钮
            Row(
              children: [
                // 复制链接按钮
                Expanded(
                  child: InkWell(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: link));
                      Card3ToastUtil.showToast(message: ToastMessages.referralLinkCopied);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1E1E1E),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Copy Link',
                            style: TextStyle(
                              color: Colors.amber,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(width: 6),
                          Icon(
                            Icons.copy_rounded,
                            color: Colors.amber,
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // 二维码按钮
                Expanded(
                  child: InkWell(
                    onTap: () => _showQRCodeDialog(link),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1E1E1E),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'QR Code',
                            style: TextStyle(
                              color: Colors.amber,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(width: 6),
                          Icon(
                            Icons.qr_code,
                            color: Colors.amber,
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 48),

            // 推荐记录标题
            const Text(
              'Referred',
              style: TextStyle(
                color: Colors.white,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // 推荐记录列表
            referralLogsState.when(
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(24.0),
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
                  ),
                ),
              ),
              error: (error, stack) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Text(
                    'Error loading referral logs: $error',
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ),
              data: (logs) {
                if (logs.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(24.0),
                      child: Text(
                        'No referrals yet',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: logs.length,
                  itemBuilder: (context, index) {
                    final log = logs[index];
                    return _buildReferralItem(log);
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReferralItem(ReferralLog log) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorName.cardColorDark,
      ),
      child: Row(
        children: [
          // 邮箱和日期
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  log.inviteeEmail.isEmpty ? 'Unknown User' : log.inviteeEmail,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  formatDateTime(log.createTime),
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // 积分奖励
          Text(
            '+${log.integral}',
            style: const TextStyle(
              color: Colors.amber,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
