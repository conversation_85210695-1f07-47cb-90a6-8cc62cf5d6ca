import 'package:flutter/material.dart';
import 'package:me_ui/me_ui.dart' show MEImage;

import '/models/business.dart';
import '/routes/card3_routes.dart' show Routes;
import '/ui/widgets/toast.dart' show Card3ToastUtil;

Future<void> showTokenSelection(
  BuildContext context,
  List<TokenBalance> tokenBalances,
  Function(TokenBalance) callback,
) async {
  if (tokenBalances.isEmpty) {
    Card3ToastUtil.showToast(message: 'No available tokens');
    return;
  }

  final selectedToken = await showModalBottomSheet<TokenBalance>(
    context: context,
    builder: (context) => Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Select Token',
                style: TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.black, size: 28),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: tokenBalances.length,
              itemBuilder: (context, index) {
                final tokenBalance = tokenBalances[index];
                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).pushNamed(
                      Routes.walletSend.name,
                      arguments: Routes.walletSend.d(
                        tokenBalance: tokenBalance,
                      ),
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      border: index < tokenBalances.length - 1
                          ? Border(
                              bottom: BorderSide(
                                color: Colors.grey.withValues(alpha: 0.1),
                                width: 1,
                              ),
                            )
                          : null,
                    ),
                    padding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 20,
                    ),
                    margin: index == tokenBalances.length - 1
                        ? const EdgeInsets.only(bottom: 0)
                        : const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        // 代币图标
                        if (tokenBalance.token.icon.isNotEmpty)
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: MEImage(
                              tokenBalance.token.icon,
                              clipOval: true,
                              fit: BoxFit.cover,
                              alternativeSVG: true,
                            ),
                          )
                        else
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: Colors.deepPurple[200],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Center(
                              child: Text(
                                tokenBalance.token.symbol.substring(0, 1).toUpperCase(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                            ),
                          ),
                        const SizedBox(width: 12),

                        // 代币名称和符号
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                tokenBalance.token.symbol,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // 代币数量及价值
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              tokenBalance.balance.toStringAsFixed(4),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '\$${tokenBalance.usdValue}',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[400],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    ),
  );

  if (selectedToken != null) {
    callback(selectedToken);
  }
}
