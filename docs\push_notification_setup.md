# 推送通知完整接入指南（简化版）

## 📱 已完成的工作

### 1. 依赖配置
已添加以下依赖：
- `firebase_messaging: ^15.2.2` - Firebase云消息服务
- `flutter_local_notifications: ^18.0.0` - 本地通知服务

### 2. 平台配置

#### Android配置
- ✅ 添加了基本推送权限到 `AndroidManifest.xml`
- ✅ 使用Flutter插件处理所有推送逻辑，无需原生代码

#### iOS配置
- ✅ 更新了 `AppDelegate.swift` 支持推送通知
- ✅ 添加了通知权限说明到 `Info.plist`
- ✅ 配置Firebase初始化

### 3. Flutter代码结构
```
lib/feat/push/
├── push_notification_service.dart    # 推送服务核心类
├── push_notification_provider.dart   # Riverpod状态管理
├── push_notification_page.dart       # 设置页面UI
└── helper.dart                       # 导出所有推送功能
```

### 4. 功能特性
- ✅ 跨平台权限请求和管理
- ✅ FCM Token获取和管理
- ✅ 前台、后台、应用关闭状态的消息处理
- ✅ 本地通知显示
- ✅ 主题订阅管理
- ✅ 消息分类处理（钱包、社交、系统）
- ✅ 完整的设置UI界面

## 🚀 如何使用

### 1. 初始化推送服务
推送服务已在应用启动时自动初始化，无需手动调用。

### 2. 在页面中使用
```dart
import 'package:card3/exports.dart'; // 包含所有推送功能

class MyPage extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听推送状态
    final pushState = ref.watch(pushNotificationProvider);
    
    // 获取FCM Token
    final fcmTokenAsync = ref.watch(fcmTokenProvider);
    
    return Scaffold(
      body: Column(
        children: [
          Text('权限状态: ${pushState.hasPermission ? "已授权" : "未授权"}'),
          
          fcmTokenAsync.when(
            data: (token) => Text('Token: $token'),
            loading: () => CircularProgressIndicator(),
            error: (error, stack) => Text('错误: $error'),
          ),
          
          ElevatedButton(
            onPressed: () {
              // 订阅主题
              ref.read(pushNotificationProvider.notifier)
                 .subscribeToTopic('wallet_updates');
            },
            child: Text('订阅钱包更新'),
          ),
        ],
      ),
    );
  }
}
```

### 3. 访问推送设置页面
```dart
Navigator.pushNamed(context, '/setting/push_notification');
```

## 📋 待完成的工作

### 1. Firebase项目配置
- [x] Android: `google-services.json` 已在项目中
- [x] iOS: `GoogleService-Info.plist` 已在项目中
- [ ] 确认Firebase控制台中的应用配置正确

### 2. 服务端集成
- [ ] 实现Token上传API
- [ ] 实现推送发送API
- [ ] 配置消息分类和路由逻辑

### 3. 测试验证
- [ ] 测试权限请求流程
- [ ] 测试前台消息接收
- [ ] 测试后台消息接收
- [ ] 测试应用关闭状态消息处理
- [ ] 测试主题订阅功能

## 🔧 消息格式

### 推送消息数据结构
```json
{
  "notification": {
    "title": "消息标题",
    "body": "消息内容"
  },
  "data": {
    "type": "wallet|social|system",
    "action": "具体操作",
    "targetId": "目标ID",
    "extra": "额外数据"
  }
}
```

### 消息类型处理
- `wallet`: 钱包相关通知（转账、余额变化等）
- `social`: 社交相关通知（好友请求、评论等）  
- `system`: 系统相关通知（更新、公告等）

## 📂 项目结构

### 推送功能目录
```
lib/feat/push/
├── push_notification_service.dart    # 核心服务类
├── push_notification_provider.dart   # 状态管理
├── push_notification_page.dart       # 设置页面
└── helper.dart                       # 功能导出
```

### 配置文件
- `android/app/src/main/AndroidManifest.xml` - Android权限配置
- `ios/Runner/AppDelegate.swift` - iOS推送配置
- `ios/Runner/Info.plist` - iOS权限说明
- `android/app/google-services.json` - Android Firebase配置
- `ios/Runner/GoogleService-Info.plist` - iOS Firebase配置

## 🛠️ 故障排除

### 常见问题
1. **Token获取失败**
   - 检查Firebase配置文件是否正确放置
   - 确认网络连接正常
   - 查看Firebase控制台是否有错误

2. **权限被拒绝**
   - Android: 引导用户到应用设置页面手动开启
   - iOS: 检查系统通知设置

3. **消息收不到**
   - 检查Token是否正确上传到服务器
   - 确认消息格式是否正确
   - 查看设备网络状态

### 调试方法
- 查看控制台日志，搜索 `📱` 标记的推送相关日志
- 使用Firebase控制台的测试消息功能
- 检查 `lib/feat/push/push_notification_service.dart` 中的处理逻辑

## 🎯 优势

### 简化的架构
- ✅ 纯Flutter实现，无需复杂的原生代码
- ✅ 统一的跨平台API
- ✅ 模块化的feat目录结构

### 开发友好
- ✅ 完整的类型定义
- ✅ 详细的调试日志
- ✅ 清晰的状态管理

### 维护性
- ✅ 单一职责的文件组织
- ✅ 统一的导出管理
- ✅ 易于扩展的业务逻辑

## 📚 相关文档
- [Firebase Cloud Messaging文档](https://firebase.google.com/docs/cloud-messaging)
- [Flutter Local Notifications文档](https://pub.dev/packages/flutter_local_notifications)
- [Permission Handler文档](https://pub.dev/packages/permission_handler) 