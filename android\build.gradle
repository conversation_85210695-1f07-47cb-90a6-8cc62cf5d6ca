allprojects {
    repositories {
        google()
        mavenCentral()
    }
}


rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    afterEvaluate {
        if (it.hasProperty('android')) {
            def javaVersion = JavaVersion.VERSION_17
            android {
                compileOptions {
                    sourceCompatibility javaVersion
                    targetCompatibility javaVersion
                }
                tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                    kotlinOptions {
                        jvmTarget = javaVersion.toString()
                    }
                }
                buildFeatures {
                    buildConfig = true
                }
            }
        }
    }
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
