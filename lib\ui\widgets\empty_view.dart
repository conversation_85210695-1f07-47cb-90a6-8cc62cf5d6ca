import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_ui/me_ui.dart';

import '/res/assets.gen.dart';
import '/res/fonts.gen.dart';

class EmptyView extends StatelessWidget {
  const EmptyView({
    super.key,
    this.icon,
    this.message,
    this.onTap,
  });

  final Widget? icon;
  final String? message;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return Tapper(
      onTap: onTap,
      child: Align(
        alignment: const AlignmentDirectional(0.0, -0.5),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon ?? Assets.icons.placeholderCat.svg(width: 84.0),
            if (message case final String message)
              Text(
                message,
                style: TextStyle(
                  color: context.meTheme.blueGreyIconColor,
                  fontSize: 20.0,
                  fontFamily: FontFamily.harmonyOSSans,
                  fontWeight: FontWeight.w700,
                  height: 24.0 / 20.0,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }
}

class SliverEmptyView extends StatelessWidget {
  const SliverEmptyView({
    super.key,
    this.icon,
    this.message,
    this.onTap,
    this.fillRemaining = true,
  });

  final Widget? icon;
  final String? message;
  final VoidCallback? onTap;
  final bool fillRemaining;

  @override
  Widget build(BuildContext context) {
    if (fillRemaining) {
      return SliverFillRemaining(
        child: EmptyView(icon: icon, message: message, onTap: onTap),
      );
    }
    return SliverToBoxAdapter(
      child: EmptyView(icon: icon, message: message, onTap: onTap),
    );
  }
}

class RefreshableEmptyView extends StatelessWidget {
  const RefreshableEmptyView({
    super.key,
    this.icon,
    this.message,
    this.onTap,
  });

  final Widget? icon;
  final String? message;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      slivers: [
        SliverEmptyView(
          icon: icon,
          message: message,
          onTap: onTap,
          fillRemaining: true,
        ),
      ],
    );
  }
}
