import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/internals/date_time.dart';
import '/models/business.dart';
import '/models/user.dart' show UserInfo;
import '/provider/business.dart';
import '/provider/user.dart';

@FFRoute(name: '/fun/point_record')
class PointRecord extends ConsumerStatefulWidget {
  const PointRecord({super.key});

  @override
  ConsumerState<PointRecord> createState() => _PointRecordState();
}

class _PointRecordState extends ConsumerState<PointRecord> {
  final int _pageSize = 20;
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasMore = true;
  final List<Point> _allPoints = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.9) {
      _loadMorePoints();
    }
  }

  Future<void> _loadMorePoints() async {
    if (_isLoading || !_hasMore) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final result = await ref.read(
        fetchPointsProvider(pageNum: nextPage, pageSize: _pageSize).future,
      );

      // 检查是否是最后一页
      if (result.list.isEmpty || nextPage >= result.pages) {
        setState(() {
          _hasMore = false;
          _isLoading = false;
        });
        return;
      }

      safeSetState(() {
        _allPoints.addAll(result.list);
        _currentPage = nextPage;
        _isLoading = false;
      });
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshPoints() async {
    setState(() {
      _currentPage = 1;
      _hasMore = true;
      _allPoints.clear();
      _isLoading = true;
    });

    try {
      final result = await ref.read(fetchPointsProvider(pageNum: 1, pageSize: _pageSize).future);
      safeSetState(() {
        _allPoints.addAll(result.list);
        _isLoading = false;

        // 检查是否有更多页 - 修复只有一页数据时的loading问题
        if (result.list.length < _pageSize || result.pages <= 1) {
          _hasMore = false;
        }
      });
    } catch (e, s) {
      LogUtil.e(e, stackTrace: s);
      safeSetState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final userInfo = ref.watch(fetchUserInfoProvider());
    final initialPoints = ref.watch(fetchPointsProvider(pageNum: 1, pageSize: _pageSize));

    // 初始加载时，将数据合并到_allPoints
    if (initialPoints.valueOrNull case final value? when _allPoints.isEmpty && !_isLoading) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _allPoints.addAll(value.list);
        // 检查是否有更多页 - 修复只有一页数据时的loading问题
        if (value.list.length < _pageSize || value.pages <= 1) {
          _hasMore = false;
        }
        safeSetState(() {});
      });
    }

    return Scaffold(
      backgroundColor: ColorName.backgroundColorDark,
      appBar: AppBar(
        backgroundColor: ColorName.cardColorDark,
        elevation: 0,
        leading: const AppBackButton(),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline, color: Color(0XFF9c9ca4), size: 30),
            onPressed: () {
              showModalBottomSheet(
                context: context,
                backgroundColor: ColorName.cardColorDark,
                clipBehavior: Clip.antiAlias,
                scrollControlDisabledMaxHeightRatio: 0.7,
                builder: (context) => const _PointHelpDialog(),
              );
            },
          ),
          const SizedBox(width: 16),
        ],
        title: const Text(''),
      ),
      // 使用RefreshIndicator包裹整个页面内容
      body: RefreshIndicator(
        onRefresh: _refreshPoints,
        color: Colors.amber,
        backgroundColor: Colors.grey[800],
        // 使用单一的ListView来滚动整个页面
        child: initialPoints.when(
          data: (_) {
            if (_allPoints.isEmpty) {
              return ListView(
                controller: _scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  _buildPointsHeader(userInfo),
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.5,
                    child: _buildEmptyState(),
                  ),
                ],
              );
            }

            // 按日期分组积分记录
            final groupedPoints = <String, List<Point>>{};
            for (final point in _allPoints) {
              final date = formatDate(point.createTime);
              if (!groupedPoints.containsKey(date)) {
                groupedPoints[date] = [];
              }
              groupedPoints[date]!.add(point);
            }

            final dateKeys = groupedPoints.keys.toList();

            // 使用ListView.builder构建整个页面内容
            return ListView.builder(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              itemCount: dateKeys.length + 2, // +1为顶部，+1为底部
              itemBuilder: (context, index) {
                // 第一个项目是积分头部
                if (index == 0) {
                  return _buildPointsHeader(userInfo);
                }

                // 减去1得到实际的日期索引
                final dateIndex = index - 1;

                // 底部加载更多指示器或"没有更多"状态
                if (dateIndex == dateKeys.length) {
                  return _buildLoadingIndicator();
                }

                final date = dateKeys[dateIndex];
                final pointsForDate = groupedPoints[date]!;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 日期标题
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Text(
                        date,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    // 该日期下的积分记录
                    ...pointsForDate.map((point) => _buildPointItem(point)),
                  ],
                );
              },
            );
          },
          loading: () => ListView(
            children: [
              _buildPointsHeader(userInfo),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.5,
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
                  ),
                ),
              ),
            ],
          ),
          error: (error, stack) => ListView(
            children: [
              _buildPointsHeader(userInfo),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.5,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Load failed: ${error.toString()}',
                        style: const TextStyle(color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: _refreshPoints,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amber,
                        ),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 提取积分标题和数量部分为单独的方法
  Widget _buildPointsHeader(AsyncValue<UserInfo> userInfo) {
    return ColoredBox(
      color: ColorName.cardColorDark,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 24),
        child: Column(
          children: [
            const Text(
              'Points',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 24,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              userInfo.valueOrNull?.integral.toString() ?? '0',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 48,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      alignment: Alignment.center,
      child: _hasMore
          ? const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.amber),
              strokeWidth: 2.0,
            )
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'No more records',
                style: TextStyle(color: Colors.grey[400]),
              ),
            ),
    );
  }

  // 积分记录项目
  Widget _buildPointItem(Point point) {
    final bool isPositive = point.integral > 0;
    final Color valueColor = isPositive ? const Color(0xFFFFD700) : Colors.red;
    final String valuePrefix = isPositive ? '+' : '';

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2D3A),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          // 左侧：积分项目名称和时间
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  point.description,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  formatTime(point.createTime),
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),

          // 右侧：积分值
          Text(
            '$valuePrefix${point.integral}',
            style: TextStyle(
              color: valueColor,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No point record',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[400],
            ),
          ),
        ],
      ),
    );
  }
}

class _PointHelpDialog extends StatelessWidget {
  const _PointHelpDialog();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16).copyWith(bottom: 0.0),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 不要占用全部空间
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题与关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Introducing Card3 Points',
                style: TextStyle(
                  fontSize: 24,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                icon: const Icon(Icons.close, color: Colors.white, size: 24),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 社交平台网格 - 使用Expanded并包装在滚动容器中
          const Text(
            'Card3 Points is an innovative incentive program designed to reward your engagement and participation. Each Card3 user is associated with a unique points account, allowing you to track and accumulate points independently. Whether you\'re tapping through content or completing tasks, every action you take earns you valuable points. Start engaging today and watch your rewards grow with Card3 Points!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              height: 1.5,
            ),
          ),
          const Gap.v(50.0),
        ],
      ),
    );
  }
}
