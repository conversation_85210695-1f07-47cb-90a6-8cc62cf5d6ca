import 'dart:convert';
import 'dart:ui' as ui;

import 'package:card3/exports.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_editor/image_editor.dart' as editor;
import 'package:image_picker/image_picker.dart' as picker;

class AvatarImgPicker extends StatefulWidget {
  const AvatarImgPicker({
    super.key,
    this.avatarUrl,
    required this.onImageSelected,
    this.size = 240,
    this.maxWidth = 650,
    this.maxHeight = 650,
  });

  /// 用于显示的头像URL (可以是远程URL或base64数据)
  final String? avatarUrl;

  /// 裁剪后的图片回调，返回base64格式的图片数据和图片字节
  final Function(String base64Image, Uint8List imageBytes) onImageSelected;

  /// 图片选择器的尺寸
  final double size;

  /// 压缩图片的最大宽度
  final int maxWidth;

  /// 压缩图片的最大高度
  final int maxHeight;

  @override
  State<AvatarImgPicker> createState() => AvatarImgPickerState();
}

class AvatarImgPickerState extends State<AvatarImgPicker> {
  Uint8List? _imageBytes;
  String? _avatarUrl;
  bool _hasBase64Error = false;
  bool _isProcessing = false;
  final picker.ImagePicker _picker = picker.ImagePicker();
  final GlobalKey<ExtendedImageEditorState> editorKey = GlobalKey<ExtendedImageEditorState>();

  @override
  void initState() {
    super.initState();
    _initializeAvatar();
  }

  @override
  void didUpdateWidget(AvatarImgPicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.avatarUrl != oldWidget.avatarUrl) {
      _initializeAvatar();
    }
  }

  void _initializeAvatar() {
    if (widget.avatarUrl != null && widget.avatarUrl!.isNotEmpty) {
      setState(() {
        _avatarUrl = widget.avatarUrl;
        _hasBase64Error = false;
      });

      // 如果是base64格式，转换为图片字节
      if (widget.avatarUrl!.startsWith('data:image')) {
        try {
          final base64String = widget.avatarUrl!.split(',').last;
          final decoded = base64Decode(base64String);
          safeSetState(() {
            _imageBytes = decoded;
          });
        } catch (e, s) {
          LogUtil.e(e, stackTrace: s);
          safeSetState(() {
            _hasBase64Error = true;
          });
        }
      }
    }
  }

  /// 从相册选择图片 - 公开方法，允许外部调用
  Future<void> pickImage() async {
    if (_isProcessing) {
      return;
    }
    setState(() => _isProcessing = true);
    try {
      final pickedFile = await _picker.pickImage(source: picker.ImageSource.gallery);
      if (pickedFile == null) {
        return;
      }
      final bytes = await pickedFile.readAsBytes();
      final croppedBytes = await _showCropDialog(bytes);
      if (croppedBytes == null) {
        return;
      }
      final compressedBytes = await AppLoading.run(
        () => _compressImage(croppedBytes, widget.maxWidth, widget.maxHeight),
      );
      final base64String = base64Encode(compressedBytes);
      final fullBase64 = 'data:image/jpeg;base64,$base64String';
      safeSetState(() {
        _imageBytes = compressedBytes;
        _avatarUrl = fullBase64;
        _hasBase64Error = false;
      });
      widget.onImageSelected(fullBase64, compressedBytes);
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.failedToProcessImage);
      rethrow;
    } finally {
      safeSetState(() => _isProcessing = false);
    }
  }

  Future<Uint8List?> _showCropDialog(Uint8List imageBytes) async {
    return showModalBottomSheet<Uint8List>(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true, // 允许弹窗更高
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.85,
          decoration: const BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
          ),
          child: Column(
            children: [
              // 顶部拖动条
              Container(
                margin: const EdgeInsets.only(top: 10),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[400],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // 标题栏
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Color(0xFF8560FA),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Crop Image',
                      style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
              // 裁剪区域
              Expanded(
                child: ExtendedImage.memory(
                  imageBytes,
                  fit: BoxFit.contain,
                  mode: ExtendedImageMode.editor,
                  extendedImageEditorKey: editorKey,
                  initEditorConfigHandler: (state) {
                    return EditorConfig(
                      maxScale: 8.0,
                      cropRectPadding: const EdgeInsets.all(20.0),
                      hitTestSize: 20.0,
                      cropAspectRatio: 1.0,
                    );
                  },
                ),
              ),
              // 底部按钮
              Container(
                padding: const EdgeInsets.all(16).copyWith(bottom: 32), // 增加底部内边距，避免被底部安全区覆盖
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel', style: TextStyle(color: Colors.white, fontSize: 16)),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        final rect = editorKey.currentState?.getCropRect();
                        if (rect == null) {
                          return;
                        }
                        AppLoading.run(() async {
                          try {
                            // 简化裁剪选项
                            final option = editor.ImageEditorOption();
                            option.addOption(
                              editor.ClipOption(
                                x: rect.left.toInt(),
                                y: rect.top.toInt(),
                                width: rect.width.toInt(),
                                height: rect.height.toInt(),
                              ),
                            );

                            // 执行裁剪
                            final result = await editor.ImageEditor.editImage(
                              image: imageBytes,
                              imageEditorOption: option,
                            );
                            if (result != null) {
                              Navigator.pop(context, result);
                            }
                          } catch (e) {
                            Card3ToastUtil.showToast(message: ToastMessages.failedToCropImage);
                            rethrow;
                          }
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF8560FA),
                        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      ),
                      child: const Text('Crop', style: TextStyle(fontSize: 16)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 压缩图片到指定大小
  Future<Uint8List> _compressImage(Uint8List imageBytes, int maxWidth, int maxHeight) async {
    try {
      // 解码图片
      final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
      final ui.FrameInfo frameInfo = await codec.getNextFrame();
      final ui.Image image = frameInfo.image;

      // 获取原始尺寸
      final int originalWidth = image.width;
      final int originalHeight = image.height;

      LogUtil.d('原始图片尺寸: $originalWidth x $originalHeight');

      // 判断是否需要压缩
      if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
        // 图片已经很小了，不需要压缩
        image.dispose();
        return imageBytes;
      }

      // 计算调整后的尺寸，保持纵横比
      int targetWidth = originalWidth;
      int targetHeight = originalHeight;

      // 计算缩放比例
      final double widthRatio = maxWidth / originalWidth;
      final double heightRatio = maxHeight / originalHeight;
      final double ratio = widthRatio < heightRatio ? widthRatio : heightRatio;

      targetWidth = (originalWidth * ratio).round();
      targetHeight = (originalHeight * ratio).round();

      LogUtil.d('压缩后图片尺寸: $targetWidth x $targetHeight');

      // 创建缩放后的图片
      final ui.Image resizedImage = await _resizeImage(image, targetWidth, targetHeight);

      // 将图片编码为PNG格式
      final ByteData? byteData = await resizedImage.toByteData(format: ui.ImageByteFormat.png);

      // 释放资源
      image.dispose();
      resizedImage.dispose();

      if (byteData != null) {
        return byteData.buffer.asUint8List();
      } else {
        throw Exception('Failed to encode image');
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      // 如果压缩失败，返回原始图片
      return imageBytes;
    }
  }

  // 调整图片大小
  Future<ui.Image> _resizeImage(ui.Image image, int width, int height) async {
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);

    // 使用高质量滤镜绘制调整大小的图片
    canvas.drawImageRect(
      image,
      Rect.fromLTRB(0, 0, image.width.toDouble(), image.height.toDouble()),
      Rect.fromLTRB(0, 0, width.toDouble(), height.toDouble()),
      Paint()..filterQuality = FilterQuality.high,
    );

    final ui.Picture picture = pictureRecorder.endRecording();
    return picture.toImage(width, height);
  }

  Widget _buildBase64Image() {
    final avatarUrl = _avatarUrl;
    if (avatarUrl == null || !avatarUrl.startsWith('data:image')) {
      return const SizedBox.shrink();
    }

    final imageBytes = base64Decode(avatarUrl.split(',').last);
    return Image.memory(
      imageBytes,
      fit: BoxFit.cover,
      errorBuilder: (context, e, s) {
        handleExceptions(error: e, stackTrace: s);
        return const Icon(Icons.broken_image, size: 50, color: Colors.grey);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _isProcessing ? null : pickImage,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.grey[200],
          border: Border.all(color: Colors.grey[300]!, width: 1),
        ),
        child: ClipOval(
          child: Stack(
            fit: StackFit.expand,
            children: [
              // 1. 显示选择的本地图片
              if (_imageBytes != null)
                Image.memory(
                  _imageBytes!,
                  fit: BoxFit.cover,
                )
              // 2. 显示远程URL图片
              else if (_avatarUrl != null && _avatarUrl!.startsWith('http'))
                MEImage(
                  _avatarUrl!,
                  fit: BoxFit.cover,
                  clipOval: true,
                  alternativeSVG: true,
                )
              // 3. 显示base64图片(如果不是选择的本地图片)
              else if (_avatarUrl != null && _avatarUrl!.startsWith('data:image') && !_hasBase64Error)
                _buildBase64Image()
              // 4. 显示添加图片图标
              else
                const Icon(
                  Icons.add_a_photo,
                  size: 50,
                  color: Colors.grey,
                ),

              // 显示处理指示器
              if (_isProcessing)
                Container(
                  color: Colors.black.withValues(alpha: 0.5),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
