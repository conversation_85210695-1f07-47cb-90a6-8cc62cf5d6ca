// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'okx.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_OkxTicker _$OkxTickerFromJson(Map json) => _OkxTicker(
  instId: json['instId'] as String,
  last: (json['last'] as num).toDouble(),
  askPx: (json['askPx'] as num).toDouble(),
  askSz: (json['askSz'] as num).toDouble(),
  bidPx: (json['bidPx'] as num).toDouble(),
  bidSz: (json['bidSz'] as num).toDouble(),
);

Map<String, dynamic> _$OkxTickerToJson(_OkxTicker instance) =>
    <String, dynamic>{
      'instId': instance.instId,
      'last': instance.last,
      'askPx': instance.askPx,
      'askSz': instance.askSz,
      'bidPx': instance.bidPx,
      'bidSz': instance.bidSz,
    };
