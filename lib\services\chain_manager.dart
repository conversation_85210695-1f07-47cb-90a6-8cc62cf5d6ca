import 'dart:typed_data' show Uint8List;

import '/internals/methods.dart' show handleExceptions;
import '/internals/privy.dart' show privyClient;
import '/models/business.dart';
import 'evm.dart';
import 'solana.dart';

class ChainManager {
  ChainManager._();

  static ChainManager? _instance;

  static ChainManager get instance {
    _instance ??= ChainManager._();
    return _instance!;
  }

  // 重置单例实例（用于用户退出登录时）
  static void resetInstance() {
    _instance?.dispose();
    _instance = null;
  }

  Network? _currentNetwork;
  SolanaService? _solanaService;
  EvmService? _evmService;
  String? _walletAddress;

  Network get currentNetwork => _currentNetwork!;

  // 获取当前钱包地址
  String? get walletAddress => _walletAddress;

  Future<void> initializeChain(Network network) async {
    _currentNetwork = network;
    await _initializeClient();
  }

  Future<void> switchChain(Network network) async {
    _currentNetwork = network;
    await _initializeClient();
  }

  Future<void> _initializeClient() async {
    if (_currentNetwork == null) {
      throw Exception('Network not initialized');
    }

    // 关闭现有连接
    await _solanaService?.dispose();
    await _evmService?.dispose();
    _solanaService = null;
    _evmService = null;
    _walletAddress = null; // 清空旧的钱包地址

    // 等待Privy客户端准备就绪
    await privyClient.awaitReady();

    // 根据网络类型初始化相应的客户端
    if (_isSolanaNetwork(_currentNetwork!)) {
      final s = _solanaService = SolanaService();
      await s.initialize(_currentNetwork!.rpcProviders.first.url);
      _walletAddress = s.walletAddress;
    } else {
      final s = _evmService = EvmService();
      await s.initialize(_currentNetwork!.rpcProviders.first.url, _currentNetwork!.id);
      _walletAddress = s.walletAddress;
    }
  }

  bool _isSolanaNetwork(Network network) {
    return network.name.toLowerCase() == 'solana';
  }

  // 获取代币余额
  Future<BigInt> getTokenBalance(String tokenAddress, String? walletAddress) async {
    if (_currentNetwork == null) {
      throw Exception('Network not initialized');
    }

    if (walletAddress == null || walletAddress.isEmpty) {
      return BigInt.zero;
    }

    try {
      if (_isSolanaNetwork(_currentNetwork!)) {
        return await _solanaService!.getTokenBalance(tokenAddress, walletAddress);
      } else {
        return await _evmService!.getTokenBalance(tokenAddress, walletAddress);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  // 获取原生代币余额
  Future<BigInt> getNativeBalance(String? address) async {
    if (_currentNetwork == null) {
      throw Exception('Network not initialized');
    }

    if (address == null || address.isEmpty) {
      return BigInt.zero;
    }

    try {
      if (_isSolanaNetwork(_currentNetwork!)) {
        return await _solanaService!.getNativeBalance(address);
      } else {
        return await _evmService!.getNativeBalance(address);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return BigInt.zero;
    }
  }

  // 发送原生代币交易
  Future<String> sendNativeTransaction({
    required String to,
    required BigInt value,
    BigInt? gasLimit,
  }) async {
    if (_currentNetwork == null) {
      throw Exception('Network not initialized');
    }

    try {
      if (_isSolanaNetwork(_currentNetwork!)) {
        return await _solanaService!.sendNativeTransaction(to, value);
      } else {
        return await _evmService!.sendNativeTransaction(to, value, gasLimit: gasLimit);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      rethrow;
    }
  }

  // 发送代币交易
  Future<String> sendTokenTransaction({
    required String tokenAddress,
    required String to,
    required BigInt value,
    BigInt? gasLimit,
  }) async {
    if (_currentNetwork == null) {
      throw Exception('Network not initialized');
    }

    try {
      if (_isSolanaNetwork(_currentNetwork!)) {
        return await _solanaService!.sendTokenTransaction(tokenAddress, to, value);
      } else {
        return await _evmService!.sendTokenTransaction(tokenAddress, to, value, gasLimit: gasLimit);
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      rethrow;
    }
  }

  // 向后兼容的通用交易发送方法
  Future<String> sendTransaction({
    required Token token,
    required String to,
    required BigInt value,
  }) async {
    if (token.symbol == currentNetwork.nativeCurrency.symbol) {
      return sendNativeTransaction(to: to, value: value);
    } else {
      return sendTokenTransaction(tokenAddress: token.contractAddress, to: to, value: value);
    }
  }

  // 关闭连接并清理状态
  Future<void> dispose() async {
    await _solanaService?.dispose();
    await _evmService?.dispose();
    _solanaService = null;
    _evmService = null;
    _currentNetwork = null;
    _walletAddress = null;
  }
}

// ERC20代币ABI
const String ERC20_ABI = '''
[
  {
    "constant": true,
    "inputs": [{"name": "_owner", "type": "address"}],
    "name": "balanceOf",
    "outputs": [{"name": "balance", "type": "uint256"}],
    "type": "function"
  }
]
''';

// 辅助函数: 将十六进制字符串转换为Uint8List
Uint8List hexToBytes(String hex) {
  hex = hex.startsWith('0x') ? hex.substring(2) : hex;
  if (hex.length % 2 != 0) {
    hex = '0$hex';
  }
  final result = Uint8List(hex.length ~/ 2);
  for (var i = 0; i < hex.length; i += 2) {
    final num = int.parse(hex.substring(i, i + 2), radix: 16);
    result[i ~/ 2] = num;
  }
  return result;
}
