plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def dartDefines = [
    mePlatformArchitecture: 'arm64-v8a'
]
if (project.hasProperty('dart-defines')) {
    dartDefines = dartDefines + project.property('dart-defines')
        .split(',')
        .collectEntries { entry ->
            def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
            [(pair.first()): pair.last()]
        }
}

def getCurrentEnvironment() {
    def defaultEnv = 'prod'
    def envFile = new File("$project.rootDir/../lib/constants/env.dart")

    if (!envFile.exists()) {
        throw new GradleException("ERROR: The environment configuration file was not found at ${envFile.path}.")
    }

    def pattern = 'static Env ' + '\\$active' + ' = (\\w+);'
    def activeEnv = defaultEnv

    envFile.eachLine { line ->
        def matcher = (line =~ pattern)
        if (matcher.find()) {
            activeEnv = matcher.group(1)
        }
    }

    println "build.gradle: Detected active environment -> [${activeEnv}]"
    return activeEnv
}

def envConfigs = [
    dev: [
        mainHost: "test-v.card3.fun",
        shortHost: "t.card3.co",
        socialHost: "test-social.card3.co"
    ],
    prod: [
        mainHost: "card3.fun",
        shortHost: "card3.co",
        socialHost: "social.card3.co"
    ]
]

def activeEnv = getCurrentEnvironment()
def currentConfig = envConfigs[activeEnv] ?: envConfigs.prod

android {
    namespace = "fun.card3"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true
        // Sets Java compatibility to Java 17
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17

    }

    defaultConfig {
        applicationId = "fun.card3"
        minSdk 27
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        manifestPlaceholders += [
            LinkMainHost: currentConfig.mainHost,
            LinkShortHost: currentConfig.shortHost,
            LinkSocialHost: currentConfig.socialHost,
        ]
    }

    packaging {
        dex {
            useLegacyPackaging true
        }
        jniLibs {
            useLegacyPackaging true
        }
        packaging {
            pickFirst 'lib/x86/libaosl.so'
            pickFirst 'lib/x86_64/libaosl.so'
            pickFirst 'lib/arm64-v8a/libaosl.so'
            pickFirst 'lib/armeabi-v7a/libaosl.so'
        }
    }

    signingConfigs {
        forAll {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
            v1SigningEnabled true
            v2SigningEnabled true
            enableV3Signing true
            enableV4Signing true
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.forAll
            ndk {
                abiFilters "arm64-v8a", "x86_64"
            }
            firebaseCrashlytics {
                nativeSymbolUploadEnabled false
            }
            manifestPlaceholders += [
                EnableImpeller: "true",
                DisableSurfaceControl: "false",
            ]
            minifyEnabled false
            shrinkResources false
        }
        profile {
            signingConfig signingConfigs.forAll
            ndk {
                abiFilters "arm64-v8a", "x86_64"
            }
            firebaseCrashlytics {
                nativeSymbolUploadEnabled false
            }
            manifestPlaceholders += [
                EnableImpeller: "true",
                DisableSurfaceControl: "false",
            ]
        }
        release {
            signingConfig signingConfigs.forAll
            ndk {
                abiFilters dartDefines.mePlatformArchitecture
            }
            manifestPlaceholders += [
                EnableImpeller: "false",
                DisableSurfaceControl: "true",
            ]
            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
            }
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "androidx.core:core-splashscreen:1.0.1"
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
}
