// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'service.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ApiServiceUserToken {
  @JsonKey(name: 'tokenAddress')
  String get address;
  @JsonKey(name: 'totalQuantity')
  Decimal get totalQuantity;
  @JsonKey(name: 'averageCost')
  Decimal get costAverage;
  @JsonKey(name: 'totalCost')
  Decimal get costTotal;
  @JsonKey(name: 'accuCost')
  Decimal get costAccumulate;
  @JsonKey(name: 'accuQuoteCost')
  Decimal get costQuoteAccumulate;
  @JsonKey(name: 'realizedPnl')
  Decimal get pnlRealized;
  @JsonKey(name: 'unrealizedPnl')
  Decimal get pnlUnrealized;
  @JsonKey(name: 'totalPnl')
  Decimal get pnlTotal;
  @JsonKey(name: 'quoteTotalPnl')
  Decimal get pnlQuoteTotal;

  /// Create a copy of ApiServiceUserToken
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ApiServiceUserTokenCopyWith<ApiServiceUserToken> get copyWith =>
      _$ApiServiceUserTokenCopyWithImpl<ApiServiceUserToken>(
        this as ApiServiceUserToken,
        _$identity,
      );

  /// Serializes this ApiServiceUserToken to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ApiServiceUserToken &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.totalQuantity, totalQuantity) ||
                other.totalQuantity == totalQuantity) &&
            (identical(other.costAverage, costAverage) ||
                other.costAverage == costAverage) &&
            (identical(other.costTotal, costTotal) ||
                other.costTotal == costTotal) &&
            (identical(other.costAccumulate, costAccumulate) ||
                other.costAccumulate == costAccumulate) &&
            (identical(other.costQuoteAccumulate, costQuoteAccumulate) ||
                other.costQuoteAccumulate == costQuoteAccumulate) &&
            (identical(other.pnlRealized, pnlRealized) ||
                other.pnlRealized == pnlRealized) &&
            (identical(other.pnlUnrealized, pnlUnrealized) ||
                other.pnlUnrealized == pnlUnrealized) &&
            (identical(other.pnlTotal, pnlTotal) ||
                other.pnlTotal == pnlTotal) &&
            (identical(other.pnlQuoteTotal, pnlQuoteTotal) ||
                other.pnlQuoteTotal == pnlQuoteTotal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    address,
    totalQuantity,
    costAverage,
    costTotal,
    costAccumulate,
    costQuoteAccumulate,
    pnlRealized,
    pnlUnrealized,
    pnlTotal,
    pnlQuoteTotal,
  );

  @override
  String toString() {
    return 'ApiServiceUserToken(address: $address, totalQuantity: $totalQuantity, costAverage: $costAverage, costTotal: $costTotal, costAccumulate: $costAccumulate, costQuoteAccumulate: $costQuoteAccumulate, pnlRealized: $pnlRealized, pnlUnrealized: $pnlUnrealized, pnlTotal: $pnlTotal, pnlQuoteTotal: $pnlQuoteTotal)';
  }
}

/// @nodoc
abstract mixin class $ApiServiceUserTokenCopyWith<$Res> {
  factory $ApiServiceUserTokenCopyWith(
    ApiServiceUserToken value,
    $Res Function(ApiServiceUserToken) _then,
  ) = _$ApiServiceUserTokenCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'tokenAddress') String address,
    @JsonKey(name: 'totalQuantity') Decimal totalQuantity,
    @JsonKey(name: 'averageCost') Decimal costAverage,
    @JsonKey(name: 'totalCost') Decimal costTotal,
    @JsonKey(name: 'accuCost') Decimal costAccumulate,
    @JsonKey(name: 'accuQuoteCost') Decimal costQuoteAccumulate,
    @JsonKey(name: 'realizedPnl') Decimal pnlRealized,
    @JsonKey(name: 'unrealizedPnl') Decimal pnlUnrealized,
    @JsonKey(name: 'totalPnl') Decimal pnlTotal,
    @JsonKey(name: 'quoteTotalPnl') Decimal pnlQuoteTotal,
  });
}

/// @nodoc
class _$ApiServiceUserTokenCopyWithImpl<$Res>
    implements $ApiServiceUserTokenCopyWith<$Res> {
  _$ApiServiceUserTokenCopyWithImpl(this._self, this._then);

  final ApiServiceUserToken _self;
  final $Res Function(ApiServiceUserToken) _then;

  /// Create a copy of ApiServiceUserToken
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? address = null,
    Object? totalQuantity = null,
    Object? costAverage = null,
    Object? costTotal = null,
    Object? costAccumulate = null,
    Object? costQuoteAccumulate = null,
    Object? pnlRealized = null,
    Object? pnlUnrealized = null,
    Object? pnlTotal = null,
    Object? pnlQuoteTotal = null,
  }) {
    return _then(
      _self.copyWith(
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        totalQuantity: null == totalQuantity
            ? _self.totalQuantity
            : totalQuantity // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costAverage: null == costAverage
            ? _self.costAverage
            : costAverage // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costTotal: null == costTotal
            ? _self.costTotal
            : costTotal // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costAccumulate: null == costAccumulate
            ? _self.costAccumulate
            : costAccumulate // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costQuoteAccumulate: null == costQuoteAccumulate
            ? _self.costQuoteAccumulate
            : costQuoteAccumulate // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlRealized: null == pnlRealized
            ? _self.pnlRealized
            : pnlRealized // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlUnrealized: null == pnlUnrealized
            ? _self.pnlUnrealized
            : pnlUnrealized // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlTotal: null == pnlTotal
            ? _self.pnlTotal
            : pnlTotal // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlQuoteTotal: null == pnlQuoteTotal
            ? _self.pnlQuoteTotal
            : pnlQuoteTotal // ignore: cast_nullable_to_non_nullable
                  as Decimal,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _ApiServiceUserToken implements ApiServiceUserToken {
  const _ApiServiceUserToken({
    @JsonKey(name: 'tokenAddress') required this.address,
    @JsonKey(name: 'totalQuantity') required this.totalQuantity,
    @JsonKey(name: 'averageCost') required this.costAverage,
    @JsonKey(name: 'totalCost') required this.costTotal,
    @JsonKey(name: 'accuCost') required this.costAccumulate,
    @JsonKey(name: 'accuQuoteCost') required this.costQuoteAccumulate,
    @JsonKey(name: 'realizedPnl') required this.pnlRealized,
    @JsonKey(name: 'unrealizedPnl') required this.pnlUnrealized,
    @JsonKey(name: 'totalPnl') required this.pnlTotal,
    @JsonKey(name: 'quoteTotalPnl') required this.pnlQuoteTotal,
  });
  factory _ApiServiceUserToken.fromJson(Map<String, dynamic> json) =>
      _$ApiServiceUserTokenFromJson(json);

  @override
  @JsonKey(name: 'tokenAddress')
  final String address;
  @override
  @JsonKey(name: 'totalQuantity')
  final Decimal totalQuantity;
  @override
  @JsonKey(name: 'averageCost')
  final Decimal costAverage;
  @override
  @JsonKey(name: 'totalCost')
  final Decimal costTotal;
  @override
  @JsonKey(name: 'accuCost')
  final Decimal costAccumulate;
  @override
  @JsonKey(name: 'accuQuoteCost')
  final Decimal costQuoteAccumulate;
  @override
  @JsonKey(name: 'realizedPnl')
  final Decimal pnlRealized;
  @override
  @JsonKey(name: 'unrealizedPnl')
  final Decimal pnlUnrealized;
  @override
  @JsonKey(name: 'totalPnl')
  final Decimal pnlTotal;
  @override
  @JsonKey(name: 'quoteTotalPnl')
  final Decimal pnlQuoteTotal;

  /// Create a copy of ApiServiceUserToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ApiServiceUserTokenCopyWith<_ApiServiceUserToken> get copyWith =>
      __$ApiServiceUserTokenCopyWithImpl<_ApiServiceUserToken>(
        this,
        _$identity,
      );

  @override
  Map<String, dynamic> toJson() {
    return _$ApiServiceUserTokenToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ApiServiceUserToken &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.totalQuantity, totalQuantity) ||
                other.totalQuantity == totalQuantity) &&
            (identical(other.costAverage, costAverage) ||
                other.costAverage == costAverage) &&
            (identical(other.costTotal, costTotal) ||
                other.costTotal == costTotal) &&
            (identical(other.costAccumulate, costAccumulate) ||
                other.costAccumulate == costAccumulate) &&
            (identical(other.costQuoteAccumulate, costQuoteAccumulate) ||
                other.costQuoteAccumulate == costQuoteAccumulate) &&
            (identical(other.pnlRealized, pnlRealized) ||
                other.pnlRealized == pnlRealized) &&
            (identical(other.pnlUnrealized, pnlUnrealized) ||
                other.pnlUnrealized == pnlUnrealized) &&
            (identical(other.pnlTotal, pnlTotal) ||
                other.pnlTotal == pnlTotal) &&
            (identical(other.pnlQuoteTotal, pnlQuoteTotal) ||
                other.pnlQuoteTotal == pnlQuoteTotal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    address,
    totalQuantity,
    costAverage,
    costTotal,
    costAccumulate,
    costQuoteAccumulate,
    pnlRealized,
    pnlUnrealized,
    pnlTotal,
    pnlQuoteTotal,
  );

  @override
  String toString() {
    return 'ApiServiceUserToken(address: $address, totalQuantity: $totalQuantity, costAverage: $costAverage, costTotal: $costTotal, costAccumulate: $costAccumulate, costQuoteAccumulate: $costQuoteAccumulate, pnlRealized: $pnlRealized, pnlUnrealized: $pnlUnrealized, pnlTotal: $pnlTotal, pnlQuoteTotal: $pnlQuoteTotal)';
  }
}

/// @nodoc
abstract mixin class _$ApiServiceUserTokenCopyWith<$Res>
    implements $ApiServiceUserTokenCopyWith<$Res> {
  factory _$ApiServiceUserTokenCopyWith(
    _ApiServiceUserToken value,
    $Res Function(_ApiServiceUserToken) _then,
  ) = __$ApiServiceUserTokenCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'tokenAddress') String address,
    @JsonKey(name: 'totalQuantity') Decimal totalQuantity,
    @JsonKey(name: 'averageCost') Decimal costAverage,
    @JsonKey(name: 'totalCost') Decimal costTotal,
    @JsonKey(name: 'accuCost') Decimal costAccumulate,
    @JsonKey(name: 'accuQuoteCost') Decimal costQuoteAccumulate,
    @JsonKey(name: 'realizedPnl') Decimal pnlRealized,
    @JsonKey(name: 'unrealizedPnl') Decimal pnlUnrealized,
    @JsonKey(name: 'totalPnl') Decimal pnlTotal,
    @JsonKey(name: 'quoteTotalPnl') Decimal pnlQuoteTotal,
  });
}

/// @nodoc
class __$ApiServiceUserTokenCopyWithImpl<$Res>
    implements _$ApiServiceUserTokenCopyWith<$Res> {
  __$ApiServiceUserTokenCopyWithImpl(this._self, this._then);

  final _ApiServiceUserToken _self;
  final $Res Function(_ApiServiceUserToken) _then;

  /// Create a copy of ApiServiceUserToken
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? address = null,
    Object? totalQuantity = null,
    Object? costAverage = null,
    Object? costTotal = null,
    Object? costAccumulate = null,
    Object? costQuoteAccumulate = null,
    Object? pnlRealized = null,
    Object? pnlUnrealized = null,
    Object? pnlTotal = null,
    Object? pnlQuoteTotal = null,
  }) {
    return _then(
      _ApiServiceUserToken(
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        totalQuantity: null == totalQuantity
            ? _self.totalQuantity
            : totalQuantity // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costAverage: null == costAverage
            ? _self.costAverage
            : costAverage // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costTotal: null == costTotal
            ? _self.costTotal
            : costTotal // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costAccumulate: null == costAccumulate
            ? _self.costAccumulate
            : costAccumulate // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        costQuoteAccumulate: null == costQuoteAccumulate
            ? _self.costQuoteAccumulate
            : costQuoteAccumulate // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlRealized: null == pnlRealized
            ? _self.pnlRealized
            : pnlRealized // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlUnrealized: null == pnlUnrealized
            ? _self.pnlUnrealized
            : pnlUnrealized // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlTotal: null == pnlTotal
            ? _self.pnlTotal
            : pnlTotal // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        pnlQuoteTotal: null == pnlQuoteTotal
            ? _self.pnlQuoteTotal
            : pnlQuoteTotal // ignore: cast_nullable_to_non_nullable
                  as Decimal,
      ),
    );
  }
}
