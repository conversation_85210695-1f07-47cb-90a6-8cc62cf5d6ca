import 'dart:ui' as ui;

import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/business.dart';
import '/provider/card.dart';
import '/provider/user.dart';

class Fun extends StatelessWidget {
  const Fun({super.key});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: themeBy(
        meTheme: defaultMEThemeDark,
        locale: Localizations.localeOf(context),
      ),
      child: const _MainBody(),
    );
  }
}

class _MainBody extends ConsumerWidget {
  const _MainBody();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userInfo = ref.watch(fetchUserInfoProvider());
    final config = ref.watch(configProvider);
    final events = config.events;
    final cards = ref.watch(fetchMyCardsProvider);
    final cardEventIds = (cards.valueOrNull ?? []).map((c) => c.card3EventId).toSet();

    final filteredEvents = events.where((event) {
      final eventIds = event.eventIds;
      return eventIds.isNotEmpty ? eventIds.any((id) => cardEventIds.contains(id)) : true;
    }).toList();

    return BrightnessLayer(
      brightness: Brightness.dark,
      child: Scaffold(
        body: SingleChildScrollView(
          child: DecoratedBox(
            decoration: BoxDecoration(
              image: DecorationImage(
                alignment: const FractionalOffset(0.2, 0.4),
                image: AssetImage(Assets.icons.images.bgPoints.path),
                fit: BoxFit.fitWidth,
              ),
            ),
            child: Column(
              children: [
                const Stack(
                  children: [
                    Positioned(
                      child: _AnimatedAppLogo(),
                    ),
                  ],
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: context.theme.cardColor,
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: GestureDetector(
                              onTap: () {
                                // 导航到积分记录页面
                                meNavigator.pushNamed(
                                  Routes.funPointRecord.name,
                                );
                              },
                              child: const Icon(Icons.history, size: 30),
                            ),
                          ),
                        ],
                      ),
                      const Text(
                        'Points',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey,
                          fontSize: 24,
                        ),
                      ),
                      Text(
                        userInfo.valueOrNull?.integral.toString() ?? '0',
                        style: const TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      GestureDetector(
                        onTap: () {
                          // 导航到积分记录页面
                          meNavigator.pushNamed(Routes.funPointRecord.name);
                        },
                        child: const Text(
                          'Point Records',
                          style: TextStyle(
                            color: Color(0xFFFFD700),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: GridView.count(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    crossAxisCount: 2,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    childAspectRatio: 1.3,
                    children: [
                      ...filteredEvents.map(
                        (event) => _buildFeatureCard(
                          context,
                          title: event.title,
                          subtitle: event.desc,
                          onTap: () {
                            meNavigator.pushNamed(
                              Routes.webview.name,
                              arguments: Routes.webview.d(
                                url: '$envUrlCard3${event.link}',
                                title: event.title,
                              ),
                            );
                          },
                          icon: Container(
                            width: 56,
                            height: 56,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                            ),
                            child: MEImage(
                              event.image,
                              fit: BoxFit.contain,
                              height: 56,
                            ),
                          ),
                        ),
                      ),
                      _buildFeatureCard(
                        context,
                        title: 'Connections',
                        subtitle: 'Stay connected',
                        onTap: () {
                          meNavigator.pushNamed(Routes.funConnection.name);
                        },
                        icon: Container(
                          width: 56,
                          height: 56,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                          child: BackdropFilter(
                            filter: ui.ImageFilter.blur(sigmaX: 0, sigmaY: 0),
                            blendMode: BlendMode.lighten,
                            child: Image(
                              fit: BoxFit.cover,
                              image: AssetImage(Assets.icons.images.ad3.path),
                            ),
                          ),
                        ),
                      ),
                      _buildFeatureCard(
                        context,
                        title: 'Referral',
                        subtitle: 'Earn points',
                        onTap: () {
                          meNavigator.pushNamed(Routes.funReferal.name);
                        },
                        icon: Container(
                          width: 56,
                          height: 56,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                          child: Image(
                            fit: BoxFit.cover,
                            image: AssetImage(Assets.icons.images.ad1.path),
                            height: 56,
                            colorBlendMode: BlendMode.plus,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建功能卡片的辅助方法
  Widget _buildFeatureCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Widget icon,
    VoidCallback? onTap,
  }) {
    return Tapper(
      onTap: onTap,
      child: Container(
        constraints: const BoxConstraints(minHeight: 140),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: context.theme.cardColor,
        ),
        padding: const EdgeInsets.all(12),
        child: Column(
          spacing: 8.0,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              spacing: 4.0,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textTheme.headlineSmall,
                  ),
                ),
                Text(
                  subtitle,
                  style: const TextStyle(color: Colors.grey),
                ),
              ],
            ),
            // 使用Align替代Row，确保图标总是在右下角
            Expanded(
              child: Align(
                alignment: AlignmentDirectional.bottomEnd,
                child: icon,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 添加AnimatedAppLogo组件
class _AnimatedAppLogo extends StatefulWidget {
  const _AnimatedAppLogo();

  @override
  State<_AnimatedAppLogo> createState() => _AnimatedAppLogoState();
}

class _AnimatedAppLogoState extends State<_AnimatedAppLogo> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat(reverse: true); // 反向重复播放动画

    // 创建一个从0.5到1.0的透明度动画
    _opacityAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: child,
        );
      },
      child: Assets.icons.headerFun.svg(width: 413.0, fit: BoxFit.cover),
    );
  }
}
