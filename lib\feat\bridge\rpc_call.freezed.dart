// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rpc_call.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$JsonRPCRequest {
  @JsonKey(name: 'id', fromJson: parseJsonRpcId)
  String get id;
  @JsonKey(name: 'method')
  String get method;
  @JsonKey(name: 'params')
  List get params;
  String get jsonrpc;

  /// Create a copy of JsonRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JsonRPCRequestCopyWith<JsonRPCRequest> get copyWith =>
      _$JsonRPCRequestCopyWithImpl<JsonRPCRequest>(
        this as JsonRPCRequest,
        _$identity,
      );

  /// Serializes this JsonRPCRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JsonRPCRequest &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.method, method) || other.method == method) &&
            const DeepCollectionEquality().equals(other.params, params) &&
            (identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    method,
    const DeepCollectionEquality().hash(params),
    jsonrpc,
  );

  @override
  String toString() {
    return 'JsonRPCRequest(id: $id, method: $method, params: $params, jsonrpc: $jsonrpc)';
  }
}

/// @nodoc
abstract mixin class $JsonRPCRequestCopyWith<$Res> {
  factory $JsonRPCRequestCopyWith(
    JsonRPCRequest value,
    $Res Function(JsonRPCRequest) _then,
  ) = _$JsonRPCRequestCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) String id,
    @JsonKey(name: 'method') String method,
    @JsonKey(name: 'params') List params,
    String jsonrpc,
  });
}

/// @nodoc
class _$JsonRPCRequestCopyWithImpl<$Res>
    implements $JsonRPCRequestCopyWith<$Res> {
  _$JsonRPCRequestCopyWithImpl(this._self, this._then);

  final JsonRPCRequest _self;
  final $Res Function(JsonRPCRequest) _then;

  /// Create a copy of JsonRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? method = null,
    Object? params = null,
    Object? jsonrpc = null,
  }) {
    return _then(
      _self.copyWith(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        method: null == method
            ? _self.method
            : method // ignore: cast_nullable_to_non_nullable
                  as String,
        params: null == params
            ? _self.params
            : params // ignore: cast_nullable_to_non_nullable
                  as List,
        jsonrpc: null == jsonrpc
            ? _self.jsonrpc
            : jsonrpc // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _JsonRPCRequest extends JsonRPCRequest {
  const _JsonRPCRequest({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) required final String id,
    @JsonKey(name: 'method') required this.method,
    @JsonKey(name: 'params') required final List params,
    final String jsonrpc = JsonRPC.JSON_RPC_VERSION,
  }) : _params = params,
       super._(id: id, jsonrpc: jsonrpc);
  factory _JsonRPCRequest.fromJson(Map<String, dynamic> json) =>
      _$JsonRPCRequestFromJson(json);

  @override
  @JsonKey(name: 'method')
  final String method;
  final List _params;
  @override
  @JsonKey(name: 'params')
  List get params {
    if (_params is EqualUnmodifiableListView) return _params;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_params);
  }

  /// Create a copy of JsonRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JsonRPCRequestCopyWith<_JsonRPCRequest> get copyWith =>
      __$JsonRPCRequestCopyWithImpl<_JsonRPCRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JsonRPCRequestToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JsonRPCRequest &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.method, method) || other.method == method) &&
            const DeepCollectionEquality().equals(other._params, _params) &&
            (identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    method,
    const DeepCollectionEquality().hash(_params),
    jsonrpc,
  );

  @override
  String toString() {
    return 'JsonRPCRequest(id: $id, method: $method, params: $params, jsonrpc: $jsonrpc)';
  }
}

/// @nodoc
abstract mixin class _$JsonRPCRequestCopyWith<$Res>
    implements $JsonRPCRequestCopyWith<$Res> {
  factory _$JsonRPCRequestCopyWith(
    _JsonRPCRequest value,
    $Res Function(_JsonRPCRequest) _then,
  ) = __$JsonRPCRequestCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) String id,
    @JsonKey(name: 'method') String method,
    @JsonKey(name: 'params') List params,
    String jsonrpc,
  });
}

/// @nodoc
class __$JsonRPCRequestCopyWithImpl<$Res>
    implements _$JsonRPCRequestCopyWith<$Res> {
  __$JsonRPCRequestCopyWithImpl(this._self, this._then);

  final _JsonRPCRequest _self;
  final $Res Function(_JsonRPCRequest) _then;

  /// Create a copy of JsonRPCRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? method = null,
    Object? params = null,
    Object? jsonrpc = null,
  }) {
    return _then(
      _JsonRPCRequest(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        method: null == method
            ? _self.method
            : method // ignore: cast_nullable_to_non_nullable
                  as String,
        params: null == params
            ? _self._params
            : params // ignore: cast_nullable_to_non_nullable
                  as List,
        jsonrpc: null == jsonrpc
            ? _self.jsonrpc
            : jsonrpc // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$JsonRPCResponse {
  @JsonKey(name: 'id', fromJson: parseJsonRpcId)
  String get id;
  @JsonKey(name: 'jsonrpc')
  String get jsonrpc;
  @JsonKey(name: 'result')
  dynamic get result;
  @JsonKey(name: 'error')
  JsonRPCError? get error;

  /// Create a copy of JsonRPCResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JsonRPCResponseCopyWith<JsonRPCResponse> get copyWith =>
      _$JsonRPCResponseCopyWithImpl<JsonRPCResponse>(
        this as JsonRPCResponse,
        _$identity,
      );

  /// Serializes this JsonRPCResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JsonRPCResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc) &&
            const DeepCollectionEquality().equals(other.result, result) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    jsonrpc,
    const DeepCollectionEquality().hash(result),
    error,
  );

  @override
  String toString() {
    return 'JsonRPCResponse(id: $id, jsonrpc: $jsonrpc, result: $result, error: $error)';
  }
}

/// @nodoc
abstract mixin class $JsonRPCResponseCopyWith<$Res> {
  factory $JsonRPCResponseCopyWith(
    JsonRPCResponse value,
    $Res Function(JsonRPCResponse) _then,
  ) = _$JsonRPCResponseCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) String id,
    @JsonKey(name: 'jsonrpc') String jsonrpc,
    @JsonKey(name: 'result') dynamic result,
    @JsonKey(name: 'error') JsonRPCError? error,
  });

  $JsonRPCErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$JsonRPCResponseCopyWithImpl<$Res>
    implements $JsonRPCResponseCopyWith<$Res> {
  _$JsonRPCResponseCopyWithImpl(this._self, this._then);

  final JsonRPCResponse _self;
  final $Res Function(JsonRPCResponse) _then;

  /// Create a copy of JsonRPCResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? jsonrpc = null,
    Object? result = freezed,
    Object? error = freezed,
  }) {
    return _then(
      _self.copyWith(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        jsonrpc: null == jsonrpc
            ? _self.jsonrpc
            : jsonrpc // ignore: cast_nullable_to_non_nullable
                  as String,
        result: freezed == result
            ? _self.result
            : result // ignore: cast_nullable_to_non_nullable
                  as dynamic,
        error: freezed == error
            ? _self.error
            : error // ignore: cast_nullable_to_non_nullable
                  as JsonRPCError?,
      ),
    );
  }

  /// Create a copy of JsonRPCResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $JsonRPCErrorCopyWith<$Res>? get error {
    if (_self.error == null) {
      return null;
    }

    return $JsonRPCErrorCopyWith<$Res>(_self.error!, (value) {
      return _then(_self.copyWith(error: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _JsonRPCResponse extends JsonRPCResponse {
  const _JsonRPCResponse({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) required final String id,
    @JsonKey(name: 'jsonrpc') final String jsonrpc = JsonRPC.JSON_RPC_VERSION,
    @JsonKey(name: 'result') this.result,
    @JsonKey(name: 'error') this.error,
  }) : super._(id: id, jsonrpc: jsonrpc);
  factory _JsonRPCResponse.fromJson(Map<String, dynamic> json) =>
      _$JsonRPCResponseFromJson(json);

  @override
  @JsonKey(name: 'result')
  final dynamic result;
  @override
  @JsonKey(name: 'error')
  final JsonRPCError? error;

  /// Create a copy of JsonRPCResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JsonRPCResponseCopyWith<_JsonRPCResponse> get copyWith =>
      __$JsonRPCResponseCopyWithImpl<_JsonRPCResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JsonRPCResponseToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JsonRPCResponse &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.jsonrpc, jsonrpc) || other.jsonrpc == jsonrpc) &&
            const DeepCollectionEquality().equals(other.result, result) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    jsonrpc,
    const DeepCollectionEquality().hash(result),
    error,
  );

  @override
  String toString() {
    return 'JsonRPCResponse(id: $id, jsonrpc: $jsonrpc, result: $result, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$JsonRPCResponseCopyWith<$Res>
    implements $JsonRPCResponseCopyWith<$Res> {
  factory _$JsonRPCResponseCopyWith(
    _JsonRPCResponse value,
    $Res Function(_JsonRPCResponse) _then,
  ) = __$JsonRPCResponseCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'id', fromJson: parseJsonRpcId) String id,
    @JsonKey(name: 'jsonrpc') String jsonrpc,
    @JsonKey(name: 'result') dynamic result,
    @JsonKey(name: 'error') JsonRPCError? error,
  });

  @override
  $JsonRPCErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$JsonRPCResponseCopyWithImpl<$Res>
    implements _$JsonRPCResponseCopyWith<$Res> {
  __$JsonRPCResponseCopyWithImpl(this._self, this._then);

  final _JsonRPCResponse _self;
  final $Res Function(_JsonRPCResponse) _then;

  /// Create a copy of JsonRPCResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? jsonrpc = null,
    Object? result = freezed,
    Object? error = freezed,
  }) {
    return _then(
      _JsonRPCResponse(
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as String,
        jsonrpc: null == jsonrpc
            ? _self.jsonrpc
            : jsonrpc // ignore: cast_nullable_to_non_nullable
                  as String,
        result: freezed == result
            ? _self.result
            : result // ignore: cast_nullable_to_non_nullable
                  as dynamic,
        error: freezed == error
            ? _self.error
            : error // ignore: cast_nullable_to_non_nullable
                  as JsonRPCError?,
      ),
    );
  }

  /// Create a copy of JsonRPCResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $JsonRPCErrorCopyWith<$Res>? get error {
    if (_self.error == null) {
      return null;
    }

    return $JsonRPCErrorCopyWith<$Res>(_self.error!, (value) {
      return _then(_self.copyWith(error: value));
    });
  }
}

/// @nodoc
mixin _$JsonRPCError {
  @JsonKey(name: 'code')
  int get code;
  @JsonKey(name: 'message')
  String get message;

  /// Create a copy of JsonRPCError
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JsonRPCErrorCopyWith<JsonRPCError> get copyWith =>
      _$JsonRPCErrorCopyWithImpl<JsonRPCError>(
        this as JsonRPCError,
        _$identity,
      );

  /// Serializes this JsonRPCError to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JsonRPCError &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, message);

  @override
  String toString() {
    return 'JsonRPCError(code: $code, message: $message)';
  }
}

/// @nodoc
abstract mixin class $JsonRPCErrorCopyWith<$Res> {
  factory $JsonRPCErrorCopyWith(
    JsonRPCError value,
    $Res Function(JsonRPCError) _then,
  ) = _$JsonRPCErrorCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'code') int code,
    @JsonKey(name: 'message') String message,
  });
}

/// @nodoc
class _$JsonRPCErrorCopyWithImpl<$Res> implements $JsonRPCErrorCopyWith<$Res> {
  _$JsonRPCErrorCopyWithImpl(this._self, this._then);

  final JsonRPCError _self;
  final $Res Function(JsonRPCError) _then;

  /// Create a copy of JsonRPCError
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? code = null, Object? message = null}) {
    return _then(
      _self.copyWith(
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as int,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _JsonRPCError implements JsonRPCError {
  const _JsonRPCError({
    @JsonKey(name: 'code') required this.code,
    @JsonKey(name: 'message') required this.message,
  });
  factory _JsonRPCError.fromJson(Map<String, dynamic> json) =>
      _$JsonRPCErrorFromJson(json);

  @override
  @JsonKey(name: 'code')
  final int code;
  @override
  @JsonKey(name: 'message')
  final String message;

  /// Create a copy of JsonRPCError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JsonRPCErrorCopyWith<_JsonRPCError> get copyWith =>
      __$JsonRPCErrorCopyWithImpl<_JsonRPCError>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JsonRPCErrorToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JsonRPCError &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, message);

  @override
  String toString() {
    return 'JsonRPCError(code: $code, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$JsonRPCErrorCopyWith<$Res>
    implements $JsonRPCErrorCopyWith<$Res> {
  factory _$JsonRPCErrorCopyWith(
    _JsonRPCError value,
    $Res Function(_JsonRPCError) _then,
  ) = __$JsonRPCErrorCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'code') int code,
    @JsonKey(name: 'message') String message,
  });
}

/// @nodoc
class __$JsonRPCErrorCopyWithImpl<$Res>
    implements _$JsonRPCErrorCopyWith<$Res> {
  __$JsonRPCErrorCopyWithImpl(this._self, this._then);

  final _JsonRPCError _self;
  final $Res Function(_JsonRPCError) _then;

  /// Create a copy of JsonRPCError
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? code = null, Object? message = null}) {
    return _then(
      _JsonRPCError(
        code: null == code
            ? _self.code
            : code // ignore: cast_nullable_to_non_nullable
                  as int,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$JsonRPCResult {
  @JsonKey(name: 'result')
  dynamic get result;
  @JsonKey(name: 'error')
  JsonRPCError? get error;

  /// Create a copy of JsonRPCResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JsonRPCResultCopyWith<JsonRPCResult> get copyWith =>
      _$JsonRPCResultCopyWithImpl<JsonRPCResult>(
        this as JsonRPCResult,
        _$identity,
      );

  /// Serializes this JsonRPCResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JsonRPCResult &&
            const DeepCollectionEquality().equals(other.result, result) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(result),
    error,
  );

  @override
  String toString() {
    return 'JsonRPCResult(result: $result, error: $error)';
  }
}

/// @nodoc
abstract mixin class $JsonRPCResultCopyWith<$Res> {
  factory $JsonRPCResultCopyWith(
    JsonRPCResult value,
    $Res Function(JsonRPCResult) _then,
  ) = _$JsonRPCResultCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'result') dynamic result,
    @JsonKey(name: 'error') JsonRPCError? error,
  });

  $JsonRPCErrorCopyWith<$Res>? get error;
}

/// @nodoc
class _$JsonRPCResultCopyWithImpl<$Res>
    implements $JsonRPCResultCopyWith<$Res> {
  _$JsonRPCResultCopyWithImpl(this._self, this._then);

  final JsonRPCResult _self;
  final $Res Function(JsonRPCResult) _then;

  /// Create a copy of JsonRPCResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? result = freezed, Object? error = freezed}) {
    return _then(
      _self.copyWith(
        result: freezed == result
            ? _self.result
            : result // ignore: cast_nullable_to_non_nullable
                  as dynamic,
        error: freezed == error
            ? _self.error
            : error // ignore: cast_nullable_to_non_nullable
                  as JsonRPCError?,
      ),
    );
  }

  /// Create a copy of JsonRPCResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $JsonRPCErrorCopyWith<$Res>? get error {
    if (_self.error == null) {
      return null;
    }

    return $JsonRPCErrorCopyWith<$Res>(_self.error!, (value) {
      return _then(_self.copyWith(error: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _JsonRPCResult implements JsonRPCResult {
  const _JsonRPCResult({
    @JsonKey(name: 'result') this.result,
    @JsonKey(name: 'error') this.error,
  });
  factory _JsonRPCResult.fromJson(Map<String, dynamic> json) =>
      _$JsonRPCResultFromJson(json);

  @override
  @JsonKey(name: 'result')
  final dynamic result;
  @override
  @JsonKey(name: 'error')
  final JsonRPCError? error;

  /// Create a copy of JsonRPCResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JsonRPCResultCopyWith<_JsonRPCResult> get copyWith =>
      __$JsonRPCResultCopyWithImpl<_JsonRPCResult>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JsonRPCResultToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JsonRPCResult &&
            const DeepCollectionEquality().equals(other.result, result) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(result),
    error,
  );

  @override
  String toString() {
    return 'JsonRPCResult(result: $result, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$JsonRPCResultCopyWith<$Res>
    implements $JsonRPCResultCopyWith<$Res> {
  factory _$JsonRPCResultCopyWith(
    _JsonRPCResult value,
    $Res Function(_JsonRPCResult) _then,
  ) = __$JsonRPCResultCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'result') dynamic result,
    @JsonKey(name: 'error') JsonRPCError? error,
  });

  @override
  $JsonRPCErrorCopyWith<$Res>? get error;
}

/// @nodoc
class __$JsonRPCResultCopyWithImpl<$Res>
    implements _$JsonRPCResultCopyWith<$Res> {
  __$JsonRPCResultCopyWithImpl(this._self, this._then);

  final _JsonRPCResult _self;
  final $Res Function(_JsonRPCResult) _then;

  /// Create a copy of JsonRPCResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? result = freezed, Object? error = freezed}) {
    return _then(
      _JsonRPCResult(
        result: freezed == result
            ? _self.result
            : result // ignore: cast_nullable_to_non_nullable
                  as dynamic,
        error: freezed == error
            ? _self.error
            : error // ignore: cast_nullable_to_non_nullable
                  as JsonRPCError?,
      ),
    );
  }

  /// Create a copy of JsonRPCResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $JsonRPCErrorCopyWith<$Res>? get error {
    if (_self.error == null) {
      return null;
    }

    return $JsonRPCErrorCopyWith<$Res>(_self.error!, (value) {
      return _then(_self.copyWith(error: value));
    });
  }
}
