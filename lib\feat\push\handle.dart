import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' show Ref;
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:permission_handler/permission_handler.dart' as permission;

import '/internals/box.dart';
import '/internals/methods.dart' show handleExceptions;
import '/internals/riverpod.dart' show globalContainer;
import '/provider/api.dart';

/// 顶层函数处理后台消息
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  LogUtil.enable = true;
  LogUtil.d('📱 处理后台消息: ${message.messageId}');
  LogUtil.d('📱 消息数据: ${message.data}');
  if (message.notification case final notification?) {
    LogUtil.d('📱 通知标题: ${notification.title}');
    LogUtil.d('📱 通知内容: ${notification.body}');
  }
}

/// 推送消息处理回调
typedef PushMessageHandler = void Function(Map<String, dynamic> data);

/// Token缓存键
class _TokenCacheKeys {
  static const String fcmToken = 'fcm_token';
  static const String lastSentTokenAndroid = 'last_sent_fcm_android';
  static const String lastSentTokenIOS = 'last_sent_fcm_ios';
  static const String lastSentTokenWeb = 'last_sent_fcm_web';
}

/// 推送通知服务配置
class _NotificationConfig {
  // Android通知渠道配置
  static const androidChannelId = 'card3_notification_channel';
  static const androidChannelName = 'Card3 通知';
  static const androidChannelDescription = 'Card3 应用推送通知';

  // 通知配置
  static const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    androidChannelId,
    androidChannelName,
    channelDescription: androidChannelDescription,
    importance: Importance.high,
    priority: Priority.high,
    showWhen: true,
    enableVibration: true,
    playSound: true,
    ticker: 'Card3 Notification',
  );

  static const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    presentAlert: true,
    presentBadge: true,
    presentSound: true,
    badgeNumber: 1,
    subtitle: 'Card3',
    interruptionLevel: InterruptionLevel.active,
    sound: 'default',
  );

  static const NotificationDetails notificationDetails = NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );
}

/// 权限管理器
class _PermissionManager {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  /// 检查通知权限状态
  static Future<bool> isNotificationEnabled() async {
    try {
      if (Platform.isIOS) {
        final settings = await _firebaseMessaging.getNotificationSettings();
        final isEnabled =
            settings.authorizationStatus == AuthorizationStatus.authorized ||
            settings.authorizationStatus == AuthorizationStatus.provisional;
        LogUtil.d('📱 iOS通知权限状态: ${settings.authorizationStatus}, 已启用: $isEnabled');
        return isEnabled;
      } else {
        final status = await permission.Permission.notification.status;
        final isEnabled = status == permission.PermissionStatus.granted;
        LogUtil.d('📱 Android通知权限状态: $status, 已启用: $isEnabled');
        return isEnabled;
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return false;
    }
  }

  /// 请求通知权限
  static Future<bool> requestPermissions() async {
    try {
      if (Platform.isIOS) {
        return await _requestIOSPermissions();
      } else {
        return await _requestAndroidPermissions();
      }
    } catch (e, s) {
      handleExceptions(error: e, stackTrace: s);
      return false;
    }
  }

  static Future<bool> _requestIOSPermissions() async {
    // 先检查FCM权限
    final fcmSettings = await _firebaseMessaging.getNotificationSettings();
    LogUtil.d('📱 当前FCM权限状态: ${fcmSettings.authorizationStatus}');

    // 如果FCM权限未授权，先请求FCM权限
    if (fcmSettings.authorizationStatus == AuthorizationStatus.notDetermined ||
        fcmSettings.authorizationStatus == AuthorizationStatus.denied) {
      LogUtil.d('📱 请求FCM权限...');
      final newSettings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      LogUtil.d('📱 FCM权限请求结果: ${newSettings.authorizationStatus}');
    }

    // 然后请求本地通知权限
    LogUtil.d('📱 请求iOS本地通知权限...');
    final iosPlugin = _localNotifications.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>();

    if (iosPlugin != null) {
      final granted = await iosPlugin.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
      LogUtil.d('📱 iOS本地通知权限结果: $granted');

      if (granted == true) {
        LogUtil.d('✅ iOS通知权限授权成功');
        return true;
      }
    }

    // 最终检查
    final finalSettings = await _firebaseMessaging.getNotificationSettings();
    final isGranted =
        finalSettings.authorizationStatus == AuthorizationStatus.authorized ||
        finalSettings.authorizationStatus == AuthorizationStatus.provisional;
    LogUtil.d('📱 最终权限检查结果: $isGranted');
    return isGranted;
  }

  static Future<bool> _requestAndroidPermissions() async {
    var status = await permission.Permission.notification.status;
    LogUtil.d('📱 Android当前权限状态: $status');

    if (status.isDenied) {
      LogUtil.d('📱 请求Android通知权限...');
      status = await permission.Permission.notification.request();
      LogUtil.d('📱 Android权限请求结果: $status');
    }

    if (status.isPermanentlyDenied) {
      LogUtil.d('❌ Android通知权限被永久拒绝');
      return false;
    }

    return status.isGranted;
  }

  /// 打开应用设置
  static Future<void> openAppSettings() async {
    try {
      final opened = await permission.openAppSettings();
      if (!opened) {
        LogUtil.d('❌ 无法打开应用设置');
      }
    } catch (e) {
      LogUtil.d('❌ 打开应用设置失败: $e');
    }
  }
}

/// Token管理器
class _TokenManager {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  /// 获取当前平台的token缓存键
  static String _getLastSentTokenKey() {
    if (kIsWeb) {
      return _TokenCacheKeys.lastSentTokenWeb;
    } else if (Platform.isIOS) {
      return _TokenCacheKeys.lastSentTokenIOS;
    } else if (Platform.isAndroid) {
      return _TokenCacheKeys.lastSentTokenAndroid;
    }
    return _TokenCacheKeys.lastSentTokenAndroid; // 默认
  }

  /// 获取上次发送到服务器的token
  static String? getLastSentToken() {
    try {
      final key = _getLastSentTokenKey();
      return Boxes.settings.get(key);
    } catch (e) {
      LogUtil.d('❌ 获取上次发送的token失败: $e');
      return null;
    }
  }

  /// 保存已发送到服务器的token
  static Future<void> saveLastSentToken(String token) async {
    final key = _getLastSentTokenKey();
    await Boxes.settings.put(key, token);
    LogUtil.d('📱 已保存最后发送的token到缓存');
  }

  /// 检查token是否需要更新到服务器
  static bool shouldUpdateToken(String currentToken) {
    final lastSentToken = getLastSentToken();
    final shouldUpdate = lastSentToken != currentToken;
    LogUtil.d('📱 Token更新检查: 当前=$currentToken, 上次=${lastSentToken ?? "无"}, 需要更新=$shouldUpdate');
    return shouldUpdate;
  }

  /// 获取FCM Token
  static Future<String?> getFCMToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      LogUtil.d('📱 获取FCM Token: ${token?.substring(0, 20)}...');
      return token;
    } catch (e) {
      LogUtil.d('❌ 获取FCM Token失败: $e');
      return null;
    }
  }

  /// 保存Token到本地
  static Future<void> saveTokenToLocal(String token) async {
    final box = Boxes.settings;
    await box.put(_TokenCacheKeys.fcmToken, token);
    LogUtil.d('📱 Token已保存到本地');
  }

  /// 从本地获取Token
  static String? getTokenFromLocal() {
    return Boxes.settings.get(_TokenCacheKeys.fcmToken);
  }
}

class PushNotificationService {
  PushNotificationService(this.ref) {
    initialize();
  }

  final Ref ref;

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();

  String? _fcmToken;
  bool _isInitialized = false;

  // 消息处理回调
  PushMessageHandler? _onWalletMessage;
  PushMessageHandler? _onSocialMessage;
  PushMessageHandler? _onSystemMessage;

  /// 获取FCM Token
  String? get fcmToken => _fcmToken;

  /// 设置消息处理回调
  void setMessageHandlers({
    PushMessageHandler? onWalletMessage,
    PushMessageHandler? onSocialMessage,
    PushMessageHandler? onSystemMessage,
  }) {
    _onWalletMessage = onWalletMessage;
    _onSocialMessage = onSocialMessage;
    _onSystemMessage = onSystemMessage;
  }

  /// 初始化推送服务
  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    LogUtil.d('📱 开始初始化推送服务...');

    // 1. 初始化本地通知
    await _initializeLocalNotifications();

    // 2. 设置后台消息处理器
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // 3. 获取FCM Token
    await _initializeToken();

    // 4. 设置消息监听器
    _setupMessageListeners();

    // 5. 处理应用启动时的通知
    await _handleInitialMessage();

    _isInitialized = true;
    LogUtil.d('📱 推送服务初始化完成');
  }

  /// 初始化本地通知
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');

    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      defaultPresentAlert: true,
      defaultPresentBadge: true,
      defaultPresentSound: true,
    );

    const initializationSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    LogUtil.d('📱 本地通知插件初始化完成');

    // 创建Android通知渠道
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  /// 创建Android通知渠道
  Future<void> _createNotificationChannel() async {
    const androidChannel = AndroidNotificationChannel(
      _NotificationConfig.androidChannelId,
      _NotificationConfig.androidChannelName,
      description: _NotificationConfig.androidChannelDescription,
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
    );

    final androidPlugin = _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      await androidPlugin.createNotificationChannel(androidChannel);
      LogUtil.d('📱 Android通知渠道创建成功');
    }
  }

  /// 初始化Token
  Future<void> _initializeToken() async {
    // 先从本地获取
    _fcmToken = _TokenManager.getTokenFromLocal();

    // 获取最新token
    final newToken = await _TokenManager.getFCMToken();
    if (newToken != null) {
      _fcmToken = newToken;
      await _TokenManager.saveTokenToLocal(newToken);

      // 只有当token与上次发送的不同时，才发送到服务器
      if (_TokenManager.shouldUpdateToken(newToken)) {
        await _sendTokenToServer(newToken);
      }
    }
  }

  /// 设置消息监听器
  void _setupMessageListeners() {
    // 前台消息监听
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      LogUtil.d('📱 收到前台消息: ${message.messageId}');
      _handleForegroundMessage(message);
    });

    // 应用从后台打开时的消息监听
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      LogUtil.d('📱 从通知打开应用: ${message.messageId}');
      _handleMessageOpenedApp(message);
    });

    // Token刷新监听
    _firebaseMessaging.onTokenRefresh.listen((String token) {
      LogUtil.d('📱 Token刷新: ${token.substring(0, 20)}...');
      _fcmToken = token;
      _TokenManager.saveTokenToLocal(token);

      // 只有当token与上次发送的不同时，才发送到服务器
      if (_TokenManager.shouldUpdateToken(token)) {
        _sendTokenToServer(token);
      }
    });
  }

  /// 处理应用启动时的初始消息
  Future<void> _handleInitialMessage() async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      LogUtil.d('📱 应用启动时的初始消息: ${initialMessage.messageId}');
      _handleMessageOpenedApp(initialMessage);
    }
  }

  /// 处理前台消息
  void _handleForegroundMessage(RemoteMessage message) {
    if (message.notification != null) {
      _showLocalNotification(message);
    }
    _processMessageData(message);
  }

  /// 显示本地通知
  Future<void> _showLocalNotification(RemoteMessage message) async {
    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Card3',
      message.notification?.body ?? '',
      _NotificationConfig.notificationDetails,
      payload: message.data.toString(),
    );
  }

  /// 处理消息数据
  void _processMessageData(RemoteMessage message) {
    final data = message.data;
    if (data.isEmpty) {
      return;
    }

    final type = data['type'];
    LogUtil.d('📱 处理消息类型: $type');

    switch (type) {
      case 'wallet':
        _onWalletMessage?.call(data);
      case 'social':
        _onSocialMessage?.call(data);
      case 'system':
        _onSystemMessage?.call(data);
      default:
        LogUtil.d('📱 未知通知类型: $type');
    }
  }

  /// 通知被点击的处理
  void _onNotificationTapped(NotificationResponse response) {
    LogUtil.d('📱 通知被点击: ${response.payload}');
    // TODO: 根据payload导航到对应页面
  }

  /// 处理从通知打开应用
  void _handleMessageOpenedApp(RemoteMessage message) {
    LogUtil.d('📱 从通知打开应用: ${message.data}');
    _processMessageData(message);
  }

  /// 发送Token到服务器（优化版）
  Future<void> _sendTokenToServer(String token) async {
    LogUtil.d('📱 发送Token到服务器...');

    // 确定平台对应的字段
    final Map<String, String?> params = {};
    if (kIsWeb) {
      params['fcmWeb'] = token;
    } else if (Platform.isIOS) {
      params['fcmIos'] = token;
    } else if (Platform.isAndroid) {
      params['fcmAndroid'] = token;
    }

    await globalContainer
        .read(apiServiceProvider)
        .updateUserSettings(
          fcmAndroid: params['fcmAndroid'],
          fcmIos: params['fcmIos'],
          fcmWeb: params['fcmWeb'],
        );

    // 保存已发送的token
    await _TokenManager.saveLastSentToken(token);
    LogUtil.d('📱 Token发送成功并已缓存');
  }

  // 公开方法使用权限管理器
  Future<bool> isNotificationEnabled() => _PermissionManager.isNotificationEnabled();

  Future<bool> checkAndRequestPermission() => _PermissionManager.requestPermissions();

  Future<void> openAppSettings() => _PermissionManager.openAppSettings();

  /// 手动刷新Token
  Future<void> refreshToken() async {
    LogUtil.d('📱 手动刷新Token...');
    final token = await _TokenManager.getFCMToken();
    if (token != null && token != _fcmToken) {
      _fcmToken = token;
      await _TokenManager.saveTokenToLocal(token);

      // 只有当token与上次发送的不同时，才发送到服务器
      if (_TokenManager.shouldUpdateToken(token)) {
        await _sendTokenToServer(token);
      }
    }
  }

  /// 手动发送Token到服务器（强制发送）
  Future<void> sendTokenToServer(String token) async {
    await _sendTokenToServer(token);
  }

  /// 发送自定义本地通知
  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    int? id,
  }) async {
    LogUtil.d('📱 开始发送本地通知: $title');

    // 检查权限
    final hasPermission = await isNotificationEnabled();
    if (!hasPermission) {
      throw StateError('❌ 没有通知权限，无法发送本地通知');
    }

    // 确保已初始化
    if (!_isInitialized) {
      LogUtil.d('⚠️ 推送服务未初始化，正在初始化...');
      await initialize();
    }

    final notificationId = id ?? DateTime.now().millisecondsSinceEpoch ~/ 1000;
    LogUtil.d('📱 通知ID: $notificationId');
    LogUtil.d('📱 通知内容: $title - $body');

    await _localNotifications.show(
      notificationId,
      title,
      body,
      _NotificationConfig.notificationDetails,
      payload: payload,
    );

    LogUtil.d('📱 本地通知发送成功: $title (ID: $notificationId)');
  }
}
