import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:me_constants/me_constants.dart';
import 'package:me_extensions/me_extensions.dart';
import 'package:me_l10n/me_l10n.dart';
import 'package:me_ui/me_ui.dart';
import 'package:me_utils/me_utils.dart' show LogUtil;
import 'package:mobile_scanner/mobile_scanner.dart' show BarcodeCapture;

import '../../extensions/build_context_extension.dart';
import '../../res/assets.gen.dart';
import 'handler.dart' show ScanHandler;

void _showToast(String msg) {
  showToast(msg, duration: const Duration(seconds: 3));
}

@immutable
class QR<T> {
  const QR({this.handler, required this.value});

  final ScanHandler<T>? handler;
  final ScanDat<T> value;
}

mixin _Timeout on ScanManager {
  late Timer _timeoutTimer;
  Timer? _showTimer;
  bool _disposed = false;
  bool _active = true;

  @override
  void onInit() {
    super.onInit();
    _active = true;
    _timeoutTimer = _newTimeoutTimer();
  }

  void onScanSuccess() {
    if (_disposed) {
      return;
    }
    _timeoutTimer.cancel();
    _showTimer?.cancel();
    _timeoutTimer = _newTimeoutTimer();
  }

  Timer _newTimeoutTimer() {
    return Timer(const Duration(seconds: 10), () {
      if (!_active) {
        return;
      }
      _showTimer?.cancel();
      _showTimer = _newShowTimer();
      _showToast(globalL10n.textQrCodeErrorResolve);
      LogUtil.d(globalL10n.textQrCodeErrorResolve);
    });
  }

  Timer _newShowTimer() {
    return Timer.periodic(const Duration(seconds: 5), (Timer timer) {
      if (!_active) {
        return;
      }
      _showToast(globalL10n.textQrCodeErrorResolve);
      LogUtil.d(globalL10n.textQrCodeErrorResolve);
    });
  }

  @override
  void dispose() {
    _active = false;
    _disposed = true;
    _timeoutTimer.cancel();
    _showTimer?.cancel();
    super.dispose();
  }

  @override
  void onPause() {
    _active = false;
    super.onPause();
  }

  @override
  void onResume() {
    _active = true;
    super.onResume();
  }
}

mixin _NotSupported on ScanManager {
  DateTime? _last;

  void notSupported() {
    if (_last == null) {
      _showToast(globalL10n.textQrCodeErrorInvalid);
      LogUtil.d(globalL10n.textQrCodeErrorInvalid);
      _last = DateTime.now();
    } else {
      final DateTime expired = _last!.add(const Duration(seconds: 5));
      if (expired.isBefore(DateTime.now())) {
        _showToast(globalL10n.textQrCodeErrorInvalid);
        LogUtil.d(globalL10n.textQrCodeErrorInvalid);
        _last = DateTime.now();
      }
    }
  }
}

abstract class ScanManager implements ScanLifecycle {
  factory ScanManager(Set<ScanHandler> handlers) {
    return _ScanManagerImpl(handlers);
  }

  ScanManager._(this.handlers) {
    onInit();
  }

  final Set<ScanHandler> handlers;

  FutureOr<QR> accept(BarcodeCapture result);

  @override
  @mustCallSuper
  void dispose() {
    for (final handler in handlers) {
      handler.dispose();
    }
  }

  @override
  @mustCallSuper
  void onPause() {}

  @override
  @mustCallSuper
  void onResume() {}

  @override
  @mustCallSuper
  void onInit() {}
}

class _ScanManagerImpl extends ScanManager with _Timeout, _NotSupported {
  _ScanManagerImpl(super.handlers) : super._();

  @override
  Future<QR> accept(BarcodeCapture result) async {
    for (final handler in handlers) {
      final dat = await handler.accept(result);
      final st = dat.state;
      if (st == ScanState.mismatched) {
        continue;
      }
      onScanSuccess();
      return QR(handler: handler, value: dat);
    }
    notSupported();
    return QR(value: ScanDat.mismatched());
  }
}

abstract class ScanLifecycle {
  void onInit();

  void onPause();

  void onResume();

  void dispose();
}

enum ScanState {
  mismatched,
  processing,
  processed,
}

class ScanDat<T> {
  factory ScanDat.mismatched() => ScanDat._(ScanState.mismatched);

  factory ScanDat.processing([List<Widget>? children]) => ScanDat._(ScanState.processing, children: children);

  factory ScanDat.processed(T payload) => ScanDat._(ScanState.processed, payload: payload);

  ScanDat._(this.state, {this.payload, this.children});

  final T? payload;
  final List<Widget>? children;
  final ScanState state;
}

Future<dynamic> showInvalidQRCode(
  BuildContext context,
  RoutePredicate predicate, [
  String? qr,
]) {
  final noQR = qr == null || qr.isEmpty;
  return ScrollableBottomSheet.show(
    context: context,
    builder: (context) {
      return ScrollableBottomSheet(
        title: context.l10n.titleQrCodeInvalidPrompt,
        description: context.l10n.textQrCodeInvalidPrompt,
        iconBuilder: (_) => Assets.icons.scan.warningYellow.svg(width: 50.0),
        sliversBuilder: noQR
            ? null
            : (context) => [
                SliverToBoxAdapter(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(10.0),
                    decoration: BoxDecoration(
                      color: context.theme.cardColor,
                      borderRadius: RadiusConstants.r8,
                      border: Border.all(color: context.theme.dividerColor),
                    ),
                    child: Text(
                      qr,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      style: context.theme.textTheme.bodySmall?.copyWith(
                        fontSize: 12.0,
                      ),
                    ),
                  ),
                ),
              ],
        bottomBuilder: (context) => noQR
            ? ThemeTextButton.textOnly(
                onPressed: () {
                  Navigator.popUntil(context, predicate);
                },
                text: context.l10nME.okButton,
              )
            : ThemeTextButtonGroup(
                cancelText: context.l10nME.okButton,
                onCancel: () {
                  Navigator.popUntil(context, predicate);
                },
                confirmText: context.l10nME.copyButton,
                onConfirm: () {
                  copyAndToast(qr);
                },
              ),
      );
    },
  );
}
