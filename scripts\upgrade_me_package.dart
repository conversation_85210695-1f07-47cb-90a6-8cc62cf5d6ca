// ignore_for_file: avoid_print

import 'dart:io' show File, Process, ProcessException;

import 'package:path/path.dart' as p;
import 'package:yaml/yaml.dart' show loadYaml, YamlMap;

const _remote = 'https://github.com/AstroxNetwork/me_packages';

void main() async {
  final executable = 'git';
  final arguments = ['ls-remote', _remote, 'HEAD'];
  final result = await Process.run(executable, arguments);
  if (result.exitCode != 0) {
    throw ProcessException(
      executable,
      arguments,
      result.stderr.toString(),
      result.exitCode,
    );
  }

  final ref = result.stdout.toString().split('\t').first.substring(0, 8);
  final file = File(p.join(p.current, 'pubspec.yaml'));
  String fileContent = await file.readAsString();
  final map = loadYaml(fileContent) as YamlMap;
  final previous = (map['me_package'] as YamlMap)['ref'] as String;
  if (previous == ref) {
    print('Updated. $_remote/tree/$ref');
    return;
  }
  print(
    'Upgrading from $previous to $ref\n'
    '$_remote/tree/$ref',
  );
  fileContent = fileContent.replaceFirst(previous, ref);
  await file.writeAsString(fileContent);
}
