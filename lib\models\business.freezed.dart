// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'business.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

/// @nodoc
mixin _$BlockExplorer {
  @JsonKey(name: 'explorerName')
  String get explorerName;
  @JsonKey(name: 'url')
  String get url;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BlockExplorerCopyWith<BlockExplorer> get copyWith =>
      _$BlockExplorerCopyWithImpl<BlockExplorer>(
        this as BlockExplorer,
        _$identity,
      );

  /// Serializes this BlockExplorer to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BlockExplorer &&
            (identical(other.explorerName, explorerName) ||
                other.explorerName == explorerName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, explorerName, url);

  @override
  String toString() {
    return 'BlockExplorer(explorerName: $explorerName, url: $url)';
  }
}

/// @nodoc
abstract mixin class $BlockExplorerCopyWith<$Res> {
  factory $BlockExplorerCopyWith(
    BlockExplorer value,
    $Res Function(BlockExplorer) _then,
  ) = _$BlockExplorerCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'explorerName') String explorerName,
    @JsonKey(name: 'url') String url,
  });
}

/// @nodoc
class _$BlockExplorerCopyWithImpl<$Res>
    implements $BlockExplorerCopyWith<$Res> {
  _$BlockExplorerCopyWithImpl(this._self, this._then);

  final BlockExplorer _self;
  final $Res Function(BlockExplorer) _then;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? explorerName = null, Object? url = null}) {
    return _then(
      _self.copyWith(
        explorerName: null == explorerName
            ? _self.explorerName
            : explorerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _BlockExplorer implements BlockExplorer {
  const _BlockExplorer({
    @JsonKey(name: 'explorerName') required this.explorerName,
    @JsonKey(name: 'url') required this.url,
  });
  factory _BlockExplorer.fromJson(Map<String, dynamic> json) =>
      _$BlockExplorerFromJson(json);

  @override
  @JsonKey(name: 'explorerName')
  final String explorerName;
  @override
  @JsonKey(name: 'url')
  final String url;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BlockExplorerCopyWith<_BlockExplorer> get copyWith =>
      __$BlockExplorerCopyWithImpl<_BlockExplorer>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BlockExplorerToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BlockExplorer &&
            (identical(other.explorerName, explorerName) ||
                other.explorerName == explorerName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, explorerName, url);

  @override
  String toString() {
    return 'BlockExplorer(explorerName: $explorerName, url: $url)';
  }
}

/// @nodoc
abstract mixin class _$BlockExplorerCopyWith<$Res>
    implements $BlockExplorerCopyWith<$Res> {
  factory _$BlockExplorerCopyWith(
    _BlockExplorer value,
    $Res Function(_BlockExplorer) _then,
  ) = __$BlockExplorerCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'explorerName') String explorerName,
    @JsonKey(name: 'url') String url,
  });
}

/// @nodoc
class __$BlockExplorerCopyWithImpl<$Res>
    implements _$BlockExplorerCopyWith<$Res> {
  __$BlockExplorerCopyWithImpl(this._self, this._then);

  final _BlockExplorer _self;
  final $Res Function(_BlockExplorer) _then;

  /// Create a copy of BlockExplorer
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? explorerName = null, Object? url = null}) {
    return _then(
      _BlockExplorer(
        explorerName: null == explorerName
            ? _self.explorerName
            : explorerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$Ens {
  @JsonKey(name: 'address')
  String get address;
  @JsonKey(name: 'blockCreated')
  int get blockCreated;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EnsCopyWith<Ens> get copyWith =>
      _$EnsCopyWithImpl<Ens>(this as Ens, _$identity);

  /// Serializes this Ens to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Ens &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.blockCreated, blockCreated) ||
                other.blockCreated == blockCreated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, address, blockCreated);

  @override
  String toString() {
    return 'Ens(address: $address, blockCreated: $blockCreated)';
  }
}

/// @nodoc
abstract mixin class $EnsCopyWith<$Res> {
  factory $EnsCopyWith(Ens value, $Res Function(Ens) _then) = _$EnsCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'blockCreated') int blockCreated,
  });
}

/// @nodoc
class _$EnsCopyWithImpl<$Res> implements $EnsCopyWith<$Res> {
  _$EnsCopyWithImpl(this._self, this._then);

  final Ens _self;
  final $Res Function(Ens) _then;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? address = null, Object? blockCreated = null}) {
    return _then(
      _self.copyWith(
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        blockCreated: null == blockCreated
            ? _self.blockCreated
            : blockCreated // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Ens implements Ens {
  const _Ens({
    @JsonKey(name: 'address') required this.address,
    @JsonKey(name: 'blockCreated') required this.blockCreated,
  });
  factory _Ens.fromJson(Map<String, dynamic> json) => _$EnsFromJson(json);

  @override
  @JsonKey(name: 'address')
  final String address;
  @override
  @JsonKey(name: 'blockCreated')
  final int blockCreated;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EnsCopyWith<_Ens> get copyWith =>
      __$EnsCopyWithImpl<_Ens>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EnsToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Ens &&
            (identical(other.address, address) || other.address == address) &&
            (identical(other.blockCreated, blockCreated) ||
                other.blockCreated == blockCreated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, address, blockCreated);

  @override
  String toString() {
    return 'Ens(address: $address, blockCreated: $blockCreated)';
  }
}

/// @nodoc
abstract mixin class _$EnsCopyWith<$Res> implements $EnsCopyWith<$Res> {
  factory _$EnsCopyWith(_Ens value, $Res Function(_Ens) _then) =
      __$EnsCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'address') String address,
    @JsonKey(name: 'blockCreated') int blockCreated,
  });
}

/// @nodoc
class __$EnsCopyWithImpl<$Res> implements _$EnsCopyWith<$Res> {
  __$EnsCopyWithImpl(this._self, this._then);

  final _Ens _self;
  final $Res Function(_Ens) _then;

  /// Create a copy of Ens
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? address = null, Object? blockCreated = null}) {
    return _then(
      _Ens(
        address: null == address
            ? _self.address
            : address // ignore: cast_nullable_to_non_nullable
                  as String,
        blockCreated: null == blockCreated
            ? _self.blockCreated
            : blockCreated // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$NativeCurrency {
  @JsonKey(name: 'decimals')
  int get decimals;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'symbol')
  String get symbol;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NativeCurrencyCopyWith<NativeCurrency> get copyWith =>
      _$NativeCurrencyCopyWithImpl<NativeCurrency>(
        this as NativeCurrency,
        _$identity,
      );

  /// Serializes this NativeCurrency to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is NativeCurrency &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, decimals, name, symbol);

  @override
  String toString() {
    return 'NativeCurrency(decimals: $decimals, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class $NativeCurrencyCopyWith<$Res> {
  factory $NativeCurrencyCopyWith(
    NativeCurrency value,
    $Res Function(NativeCurrency) _then,
  ) = _$NativeCurrencyCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'decimals') int decimals,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'symbol') String symbol,
  });
}

/// @nodoc
class _$NativeCurrencyCopyWithImpl<$Res>
    implements $NativeCurrencyCopyWith<$Res> {
  _$NativeCurrencyCopyWithImpl(this._self, this._then);

  final NativeCurrency _self;
  final $Res Function(NativeCurrency) _then;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? decimals = null,
    Object? name = null,
    Object? symbol = null,
  }) {
    return _then(
      _self.copyWith(
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _NativeCurrency implements NativeCurrency {
  const _NativeCurrency({
    @JsonKey(name: 'decimals') required this.decimals,
    @JsonKey(name: 'name') required this.name,
    @JsonKey(name: 'symbol') required this.symbol,
  });
  factory _NativeCurrency.fromJson(Map<String, dynamic> json) =>
      _$NativeCurrencyFromJson(json);

  @override
  @JsonKey(name: 'decimals')
  final int decimals;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NativeCurrencyCopyWith<_NativeCurrency> get copyWith =>
      __$NativeCurrencyCopyWithImpl<_NativeCurrency>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$NativeCurrencyToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _NativeCurrency &&
            (identical(other.decimals, decimals) ||
                other.decimals == decimals) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.symbol, symbol) || other.symbol == symbol));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, decimals, name, symbol);

  @override
  String toString() {
    return 'NativeCurrency(decimals: $decimals, name: $name, symbol: $symbol)';
  }
}

/// @nodoc
abstract mixin class _$NativeCurrencyCopyWith<$Res>
    implements $NativeCurrencyCopyWith<$Res> {
  factory _$NativeCurrencyCopyWith(
    _NativeCurrency value,
    $Res Function(_NativeCurrency) _then,
  ) = __$NativeCurrencyCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'decimals') int decimals,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'symbol') String symbol,
  });
}

/// @nodoc
class __$NativeCurrencyCopyWithImpl<$Res>
    implements _$NativeCurrencyCopyWith<$Res> {
  __$NativeCurrencyCopyWithImpl(this._self, this._then);

  final _NativeCurrency _self;
  final $Res Function(_NativeCurrency) _then;

  /// Create a copy of NativeCurrency
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? decimals = null,
    Object? name = null,
    Object? symbol = null,
  }) {
    return _then(
      _NativeCurrency(
        decimals: null == decimals
            ? _self.decimals
            : decimals // ignore: cast_nullable_to_non_nullable
                  as int,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$RPCProvider {
  @JsonKey(name: 'providerName')
  String get providerName;
  @JsonKey(name: 'url')
  String get url;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RPCProviderCopyWith<RPCProvider> get copyWith =>
      _$RPCProviderCopyWithImpl<RPCProvider>(this as RPCProvider, _$identity);

  /// Serializes this RPCProvider to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RPCProvider &&
            (identical(other.providerName, providerName) ||
                other.providerName == providerName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, providerName, url);

  @override
  String toString() {
    return 'RPCProvider(providerName: $providerName, url: $url)';
  }
}

/// @nodoc
abstract mixin class $RPCProviderCopyWith<$Res> {
  factory $RPCProviderCopyWith(
    RPCProvider value,
    $Res Function(RPCProvider) _then,
  ) = _$RPCProviderCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'providerName') String providerName,
    @JsonKey(name: 'url') String url,
  });
}

/// @nodoc
class _$RPCProviderCopyWithImpl<$Res> implements $RPCProviderCopyWith<$Res> {
  _$RPCProviderCopyWithImpl(this._self, this._then);

  final RPCProvider _self;
  final $Res Function(RPCProvider) _then;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? providerName = null, Object? url = null}) {
    return _then(
      _self.copyWith(
        providerName: null == providerName
            ? _self.providerName
            : providerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _RPCProvider implements RPCProvider {
  const _RPCProvider({
    @JsonKey(name: 'providerName') this.providerName = '',
    @JsonKey(name: 'url') required this.url,
  });
  factory _RPCProvider.fromJson(Map<String, dynamic> json) =>
      _$RPCProviderFromJson(json);

  @override
  @JsonKey(name: 'providerName')
  final String providerName;
  @override
  @JsonKey(name: 'url')
  final String url;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RPCProviderCopyWith<_RPCProvider> get copyWith =>
      __$RPCProviderCopyWithImpl<_RPCProvider>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RPCProviderToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RPCProvider &&
            (identical(other.providerName, providerName) ||
                other.providerName == providerName) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, providerName, url);

  @override
  String toString() {
    return 'RPCProvider(providerName: $providerName, url: $url)';
  }
}

/// @nodoc
abstract mixin class _$RPCProviderCopyWith<$Res>
    implements $RPCProviderCopyWith<$Res> {
  factory _$RPCProviderCopyWith(
    _RPCProvider value,
    $Res Function(_RPCProvider) _then,
  ) = __$RPCProviderCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'providerName') String providerName,
    @JsonKey(name: 'url') String url,
  });
}

/// @nodoc
class __$RPCProviderCopyWithImpl<$Res> implements _$RPCProviderCopyWith<$Res> {
  __$RPCProviderCopyWithImpl(this._self, this._then);

  final _RPCProvider _self;
  final $Res Function(_RPCProvider) _then;

  /// Create a copy of RPCProvider
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({Object? providerName = null, Object? url = null}) {
    return _then(
      _RPCProvider(
        providerName: null == providerName
            ? _self.providerName
            : providerName // ignore: cast_nullable_to_non_nullable
                  as String,
        url: null == url
            ? _self.url
            : url // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$Network {
  @JsonKey(name: 'blockExplorers')
  List<BlockExplorer> get blockExplorers;
  @JsonKey(name: 'ens')
  Ens get ens;
  @JsonKey(name: 'iconUrl')
  String get iconUrl;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'isEIP1559')
  bool get isEIP1559;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'nativeCurrency')
  NativeCurrency get nativeCurrency;
  @JsonKey(name: 'network')
  String get network;
  @JsonKey(name: 'networkType')
  String get networkType;
  @JsonKey(name: 'nftEnable')
  bool get nftEnable;
  @JsonKey(name: 'rpcProviders')
  List<RPCProvider> get rpcProviders;
  @JsonKey(name: 'shortName')
  String get shortName;
  @JsonKey(name: 'weight')
  int get weight;
  @JsonKey(name: 'testnet')
  bool get testnet;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $NetworkCopyWith<Network> get copyWith =>
      _$NetworkCopyWithImpl<Network>(this as Network, _$identity);

  /// Serializes this Network to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Network &&
            const DeepCollectionEquality().equals(
              other.blockExplorers,
              blockExplorers,
            ) &&
            (identical(other.ens, ens) || other.ens == ens) &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isEIP1559, isEIP1559) ||
                other.isEIP1559 == isEIP1559) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.nativeCurrency, nativeCurrency) ||
                other.nativeCurrency == nativeCurrency) &&
            (identical(other.network, network) || other.network == network) &&
            (identical(other.networkType, networkType) ||
                other.networkType == networkType) &&
            (identical(other.nftEnable, nftEnable) ||
                other.nftEnable == nftEnable) &&
            const DeepCollectionEquality().equals(
              other.rpcProviders,
              rpcProviders,
            ) &&
            (identical(other.shortName, shortName) ||
                other.shortName == shortName) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.testnet, testnet) || other.testnet == testnet));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(blockExplorers),
    ens,
    iconUrl,
    id,
    isEIP1559,
    name,
    nativeCurrency,
    network,
    networkType,
    nftEnable,
    const DeepCollectionEquality().hash(rpcProviders),
    shortName,
    weight,
    testnet,
  );

  @override
  String toString() {
    return 'Network(blockExplorers: $blockExplorers, ens: $ens, iconUrl: $iconUrl, id: $id, isEIP1559: $isEIP1559, name: $name, nativeCurrency: $nativeCurrency, network: $network, networkType: $networkType, nftEnable: $nftEnable, rpcProviders: $rpcProviders, shortName: $shortName, weight: $weight, testnet: $testnet)';
  }
}

/// @nodoc
abstract mixin class $NetworkCopyWith<$Res> {
  factory $NetworkCopyWith(Network value, $Res Function(Network) _then) =
      _$NetworkCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'blockExplorers') List<BlockExplorer> blockExplorers,
    @JsonKey(name: 'ens') Ens ens,
    @JsonKey(name: 'iconUrl') String iconUrl,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isEIP1559') bool isEIP1559,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'nativeCurrency') NativeCurrency nativeCurrency,
    @JsonKey(name: 'network') String network,
    @JsonKey(name: 'networkType') String networkType,
    @JsonKey(name: 'nftEnable') bool nftEnable,
    @JsonKey(name: 'rpcProviders') List<RPCProvider> rpcProviders,
    @JsonKey(name: 'shortName') String shortName,
    @JsonKey(name: 'weight') int weight,
    @JsonKey(name: 'testnet') bool testnet,
  });

  $EnsCopyWith<$Res> get ens;
  $NativeCurrencyCopyWith<$Res> get nativeCurrency;
}

/// @nodoc
class _$NetworkCopyWithImpl<$Res> implements $NetworkCopyWith<$Res> {
  _$NetworkCopyWithImpl(this._self, this._then);

  final Network _self;
  final $Res Function(Network) _then;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blockExplorers = null,
    Object? ens = null,
    Object? iconUrl = null,
    Object? id = null,
    Object? isEIP1559 = null,
    Object? name = null,
    Object? nativeCurrency = null,
    Object? network = null,
    Object? networkType = null,
    Object? nftEnable = null,
    Object? rpcProviders = null,
    Object? shortName = null,
    Object? weight = null,
    Object? testnet = null,
  }) {
    return _then(
      _self.copyWith(
        blockExplorers: null == blockExplorers
            ? _self.blockExplorers
            : blockExplorers // ignore: cast_nullable_to_non_nullable
                  as List<BlockExplorer>,
        ens: null == ens
            ? _self.ens
            : ens // ignore: cast_nullable_to_non_nullable
                  as Ens,
        iconUrl: null == iconUrl
            ? _self.iconUrl
            : iconUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isEIP1559: null == isEIP1559
            ? _self.isEIP1559
            : isEIP1559 // ignore: cast_nullable_to_non_nullable
                  as bool,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        nativeCurrency: null == nativeCurrency
            ? _self.nativeCurrency
            : nativeCurrency // ignore: cast_nullable_to_non_nullable
                  as NativeCurrency,
        network: null == network
            ? _self.network
            : network // ignore: cast_nullable_to_non_nullable
                  as String,
        networkType: null == networkType
            ? _self.networkType
            : networkType // ignore: cast_nullable_to_non_nullable
                  as String,
        nftEnable: null == nftEnable
            ? _self.nftEnable
            : nftEnable // ignore: cast_nullable_to_non_nullable
                  as bool,
        rpcProviders: null == rpcProviders
            ? _self.rpcProviders
            : rpcProviders // ignore: cast_nullable_to_non_nullable
                  as List<RPCProvider>,
        shortName: null == shortName
            ? _self.shortName
            : shortName // ignore: cast_nullable_to_non_nullable
                  as String,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
        testnet: null == testnet
            ? _self.testnet
            : testnet // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EnsCopyWith<$Res> get ens {
    return $EnsCopyWith<$Res>(_self.ens, (value) {
      return _then(_self.copyWith(ens: value));
    });
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NativeCurrencyCopyWith<$Res> get nativeCurrency {
    return $NativeCurrencyCopyWith<$Res>(_self.nativeCurrency, (value) {
      return _then(_self.copyWith(nativeCurrency: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _Network implements Network {
  const _Network({
    @JsonKey(name: 'blockExplorers')
    final List<BlockExplorer> blockExplorers = const [],
    @JsonKey(name: 'ens') this.ens = const Ens(address: '', blockCreated: 0),
    @JsonKey(name: 'iconUrl') this.iconUrl = '',
    @JsonKey(name: 'id') this.id = 0,
    @JsonKey(name: 'isEIP1559') this.isEIP1559 = false,
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'nativeCurrency')
    this.nativeCurrency = const NativeCurrency(
      decimals: 0,
      name: '',
      symbol: '',
    ),
    @JsonKey(name: 'network') this.network = '',
    @JsonKey(name: 'networkType') this.networkType = '',
    @JsonKey(name: 'nftEnable') this.nftEnable = false,
    @JsonKey(name: 'rpcProviders')
    final List<RPCProvider> rpcProviders = const [],
    @JsonKey(name: 'shortName') this.shortName = '',
    @JsonKey(name: 'weight') this.weight = 0,
    @JsonKey(name: 'testnet') this.testnet = false,
  }) : _blockExplorers = blockExplorers,
       _rpcProviders = rpcProviders;
  factory _Network.fromJson(Map<String, dynamic> json) =>
      _$NetworkFromJson(json);

  final List<BlockExplorer> _blockExplorers;
  @override
  @JsonKey(name: 'blockExplorers')
  List<BlockExplorer> get blockExplorers {
    if (_blockExplorers is EqualUnmodifiableListView) return _blockExplorers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_blockExplorers);
  }

  @override
  @JsonKey(name: 'ens')
  final Ens ens;
  @override
  @JsonKey(name: 'iconUrl')
  final String iconUrl;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'isEIP1559')
  final bool isEIP1559;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'nativeCurrency')
  final NativeCurrency nativeCurrency;
  @override
  @JsonKey(name: 'network')
  final String network;
  @override
  @JsonKey(name: 'networkType')
  final String networkType;
  @override
  @JsonKey(name: 'nftEnable')
  final bool nftEnable;
  final List<RPCProvider> _rpcProviders;
  @override
  @JsonKey(name: 'rpcProviders')
  List<RPCProvider> get rpcProviders {
    if (_rpcProviders is EqualUnmodifiableListView) return _rpcProviders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_rpcProviders);
  }

  @override
  @JsonKey(name: 'shortName')
  final String shortName;
  @override
  @JsonKey(name: 'weight')
  final int weight;
  @override
  @JsonKey(name: 'testnet')
  final bool testnet;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$NetworkCopyWith<_Network> get copyWith =>
      __$NetworkCopyWithImpl<_Network>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$NetworkToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Network &&
            const DeepCollectionEquality().equals(
              other._blockExplorers,
              _blockExplorers,
            ) &&
            (identical(other.ens, ens) || other.ens == ens) &&
            (identical(other.iconUrl, iconUrl) || other.iconUrl == iconUrl) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.isEIP1559, isEIP1559) ||
                other.isEIP1559 == isEIP1559) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.nativeCurrency, nativeCurrency) ||
                other.nativeCurrency == nativeCurrency) &&
            (identical(other.network, network) || other.network == network) &&
            (identical(other.networkType, networkType) ||
                other.networkType == networkType) &&
            (identical(other.nftEnable, nftEnable) ||
                other.nftEnable == nftEnable) &&
            const DeepCollectionEquality().equals(
              other._rpcProviders,
              _rpcProviders,
            ) &&
            (identical(other.shortName, shortName) ||
                other.shortName == shortName) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.testnet, testnet) || other.testnet == testnet));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_blockExplorers),
    ens,
    iconUrl,
    id,
    isEIP1559,
    name,
    nativeCurrency,
    network,
    networkType,
    nftEnable,
    const DeepCollectionEquality().hash(_rpcProviders),
    shortName,
    weight,
    testnet,
  );

  @override
  String toString() {
    return 'Network(blockExplorers: $blockExplorers, ens: $ens, iconUrl: $iconUrl, id: $id, isEIP1559: $isEIP1559, name: $name, nativeCurrency: $nativeCurrency, network: $network, networkType: $networkType, nftEnable: $nftEnable, rpcProviders: $rpcProviders, shortName: $shortName, weight: $weight, testnet: $testnet)';
  }
}

/// @nodoc
abstract mixin class _$NetworkCopyWith<$Res> implements $NetworkCopyWith<$Res> {
  factory _$NetworkCopyWith(_Network value, $Res Function(_Network) _then) =
      __$NetworkCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'blockExplorers') List<BlockExplorer> blockExplorers,
    @JsonKey(name: 'ens') Ens ens,
    @JsonKey(name: 'iconUrl') String iconUrl,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'isEIP1559') bool isEIP1559,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'nativeCurrency') NativeCurrency nativeCurrency,
    @JsonKey(name: 'network') String network,
    @JsonKey(name: 'networkType') String networkType,
    @JsonKey(name: 'nftEnable') bool nftEnable,
    @JsonKey(name: 'rpcProviders') List<RPCProvider> rpcProviders,
    @JsonKey(name: 'shortName') String shortName,
    @JsonKey(name: 'weight') int weight,
    @JsonKey(name: 'testnet') bool testnet,
  });

  @override
  $EnsCopyWith<$Res> get ens;
  @override
  $NativeCurrencyCopyWith<$Res> get nativeCurrency;
}

/// @nodoc
class __$NetworkCopyWithImpl<$Res> implements _$NetworkCopyWith<$Res> {
  __$NetworkCopyWithImpl(this._self, this._then);

  final _Network _self;
  final $Res Function(_Network) _then;

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? blockExplorers = null,
    Object? ens = null,
    Object? iconUrl = null,
    Object? id = null,
    Object? isEIP1559 = null,
    Object? name = null,
    Object? nativeCurrency = null,
    Object? network = null,
    Object? networkType = null,
    Object? nftEnable = null,
    Object? rpcProviders = null,
    Object? shortName = null,
    Object? weight = null,
    Object? testnet = null,
  }) {
    return _then(
      _Network(
        blockExplorers: null == blockExplorers
            ? _self._blockExplorers
            : blockExplorers // ignore: cast_nullable_to_non_nullable
                  as List<BlockExplorer>,
        ens: null == ens
            ? _self.ens
            : ens // ignore: cast_nullable_to_non_nullable
                  as Ens,
        iconUrl: null == iconUrl
            ? _self.iconUrl
            : iconUrl // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        isEIP1559: null == isEIP1559
            ? _self.isEIP1559
            : isEIP1559 // ignore: cast_nullable_to_non_nullable
                  as bool,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        nativeCurrency: null == nativeCurrency
            ? _self.nativeCurrency
            : nativeCurrency // ignore: cast_nullable_to_non_nullable
                  as NativeCurrency,
        network: null == network
            ? _self.network
            : network // ignore: cast_nullable_to_non_nullable
                  as String,
        networkType: null == networkType
            ? _self.networkType
            : networkType // ignore: cast_nullable_to_non_nullable
                  as String,
        nftEnable: null == nftEnable
            ? _self.nftEnable
            : nftEnable // ignore: cast_nullable_to_non_nullable
                  as bool,
        rpcProviders: null == rpcProviders
            ? _self._rpcProviders
            : rpcProviders // ignore: cast_nullable_to_non_nullable
                  as List<RPCProvider>,
        shortName: null == shortName
            ? _self.shortName
            : shortName // ignore: cast_nullable_to_non_nullable
                  as String,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
        testnet: null == testnet
            ? _self.testnet
            : testnet // ignore: cast_nullable_to_non_nullable
                  as bool,
      ),
    );
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $EnsCopyWith<$Res> get ens {
    return $EnsCopyWith<$Res>(_self.ens, (value) {
      return _then(_self.copyWith(ens: value));
    });
  }

  /// Create a copy of Network
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $NativeCurrencyCopyWith<$Res> get nativeCurrency {
    return $NativeCurrencyCopyWith<$Res>(_self.nativeCurrency, (value) {
      return _then(_self.copyWith(nativeCurrency: value));
    });
  }
}

/// @nodoc
mixin _$Token {
  @JsonKey(name: 'blockChain')
  String get blockChain;
  @JsonKey(name: 'contractAddress')
  String get contractAddress;
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'desc')
  String get desc;
  @JsonKey(name: 'digits')
  int get digits;
  @JsonKey(name: 'icon')
  String get icon;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'serviceCharge')
  int get serviceCharge;
  @JsonKey(name: 'standard')
  String get standard;
  @JsonKey(name: 'symbol')
  String get symbol;
  @JsonKey(name: 'totalSupply')
  int get totalSupply;
  @JsonKey(name: 'weight')
  int get weight;

  /// Create a copy of Token
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TokenCopyWith<Token> get copyWith =>
      _$TokenCopyWithImpl<Token>(this as Token, _$identity);

  /// Serializes this Token to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Token &&
            (identical(other.blockChain, blockChain) ||
                other.blockChain == blockChain) &&
            (identical(other.contractAddress, contractAddress) ||
                other.contractAddress == contractAddress) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.digits, digits) || other.digits == digits) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.serviceCharge, serviceCharge) ||
                other.serviceCharge == serviceCharge) &&
            (identical(other.standard, standard) ||
                other.standard == standard) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.totalSupply, totalSupply) ||
                other.totalSupply == totalSupply) &&
            (identical(other.weight, weight) || other.weight == weight));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    blockChain,
    contractAddress,
    createTime,
    desc,
    digits,
    icon,
    name,
    serviceCharge,
    standard,
    symbol,
    totalSupply,
    weight,
  );

  @override
  String toString() {
    return 'Token(blockChain: $blockChain, contractAddress: $contractAddress, createTime: $createTime, desc: $desc, digits: $digits, icon: $icon, name: $name, serviceCharge: $serviceCharge, standard: $standard, symbol: $symbol, totalSupply: $totalSupply, weight: $weight)';
  }
}

/// @nodoc
abstract mixin class $TokenCopyWith<$Res> {
  factory $TokenCopyWith(Token value, $Res Function(Token) _then) =
      _$TokenCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'blockChain') String blockChain,
    @JsonKey(name: 'contractAddress') String contractAddress,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'digits') int digits,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'serviceCharge') int serviceCharge,
    @JsonKey(name: 'standard') String standard,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'totalSupply') int totalSupply,
    @JsonKey(name: 'weight') int weight,
  });
}

/// @nodoc
class _$TokenCopyWithImpl<$Res> implements $TokenCopyWith<$Res> {
  _$TokenCopyWithImpl(this._self, this._then);

  final Token _self;
  final $Res Function(Token) _then;

  /// Create a copy of Token
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blockChain = null,
    Object? contractAddress = null,
    Object? createTime = null,
    Object? desc = null,
    Object? digits = null,
    Object? icon = null,
    Object? name = null,
    Object? serviceCharge = null,
    Object? standard = null,
    Object? symbol = null,
    Object? totalSupply = null,
    Object? weight = null,
  }) {
    return _then(
      _self.copyWith(
        blockChain: null == blockChain
            ? _self.blockChain
            : blockChain // ignore: cast_nullable_to_non_nullable
                  as String,
        contractAddress: null == contractAddress
            ? _self.contractAddress
            : contractAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        digits: null == digits
            ? _self.digits
            : digits // ignore: cast_nullable_to_non_nullable
                  as int,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        serviceCharge: null == serviceCharge
            ? _self.serviceCharge
            : serviceCharge // ignore: cast_nullable_to_non_nullable
                  as int,
        standard: null == standard
            ? _self.standard
            : standard // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        totalSupply: null == totalSupply
            ? _self.totalSupply
            : totalSupply // ignore: cast_nullable_to_non_nullable
                  as int,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Token implements Token {
  const _Token({
    @JsonKey(name: 'blockChain') required this.blockChain,
    @JsonKey(name: 'contractAddress') required this.contractAddress,
    @JsonKey(name: 'createTime') required this.createTime,
    @JsonKey(name: 'desc') this.desc = '',
    @JsonKey(name: 'digits') this.digits = 0,
    @JsonKey(name: 'icon') this.icon = '',
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'serviceCharge') this.serviceCharge = 0,
    @JsonKey(name: 'standard') required this.standard,
    @JsonKey(name: 'symbol') required this.symbol,
    @JsonKey(name: 'totalSupply') this.totalSupply = 0,
    @JsonKey(name: 'weight') this.weight = 0,
  });
  factory _Token.fromJson(Map<String, dynamic> json) => _$TokenFromJson(json);

  @override
  @JsonKey(name: 'blockChain')
  final String blockChain;
  @override
  @JsonKey(name: 'contractAddress')
  final String contractAddress;
  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'desc')
  final String desc;
  @override
  @JsonKey(name: 'digits')
  final int digits;
  @override
  @JsonKey(name: 'icon')
  final String icon;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'serviceCharge')
  final int serviceCharge;
  @override
  @JsonKey(name: 'standard')
  final String standard;
  @override
  @JsonKey(name: 'symbol')
  final String symbol;
  @override
  @JsonKey(name: 'totalSupply')
  final int totalSupply;
  @override
  @JsonKey(name: 'weight')
  final int weight;

  /// Create a copy of Token
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TokenCopyWith<_Token> get copyWith =>
      __$TokenCopyWithImpl<_Token>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TokenToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Token &&
            (identical(other.blockChain, blockChain) ||
                other.blockChain == blockChain) &&
            (identical(other.contractAddress, contractAddress) ||
                other.contractAddress == contractAddress) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.digits, digits) || other.digits == digits) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.serviceCharge, serviceCharge) ||
                other.serviceCharge == serviceCharge) &&
            (identical(other.standard, standard) ||
                other.standard == standard) &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.totalSupply, totalSupply) ||
                other.totalSupply == totalSupply) &&
            (identical(other.weight, weight) || other.weight == weight));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    blockChain,
    contractAddress,
    createTime,
    desc,
    digits,
    icon,
    name,
    serviceCharge,
    standard,
    symbol,
    totalSupply,
    weight,
  );

  @override
  String toString() {
    return 'Token(blockChain: $blockChain, contractAddress: $contractAddress, createTime: $createTime, desc: $desc, digits: $digits, icon: $icon, name: $name, serviceCharge: $serviceCharge, standard: $standard, symbol: $symbol, totalSupply: $totalSupply, weight: $weight)';
  }
}

/// @nodoc
abstract mixin class _$TokenCopyWith<$Res> implements $TokenCopyWith<$Res> {
  factory _$TokenCopyWith(_Token value, $Res Function(_Token) _then) =
      __$TokenCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'blockChain') String blockChain,
    @JsonKey(name: 'contractAddress') String contractAddress,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'digits') int digits,
    @JsonKey(name: 'icon') String icon,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'serviceCharge') int serviceCharge,
    @JsonKey(name: 'standard') String standard,
    @JsonKey(name: 'symbol') String symbol,
    @JsonKey(name: 'totalSupply') int totalSupply,
    @JsonKey(name: 'weight') int weight,
  });
}

/// @nodoc
class __$TokenCopyWithImpl<$Res> implements _$TokenCopyWith<$Res> {
  __$TokenCopyWithImpl(this._self, this._then);

  final _Token _self;
  final $Res Function(_Token) _then;

  /// Create a copy of Token
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? blockChain = null,
    Object? contractAddress = null,
    Object? createTime = null,
    Object? desc = null,
    Object? digits = null,
    Object? icon = null,
    Object? name = null,
    Object? serviceCharge = null,
    Object? standard = null,
    Object? symbol = null,
    Object? totalSupply = null,
    Object? weight = null,
  }) {
    return _then(
      _Token(
        blockChain: null == blockChain
            ? _self.blockChain
            : blockChain // ignore: cast_nullable_to_non_nullable
                  as String,
        contractAddress: null == contractAddress
            ? _self.contractAddress
            : contractAddress // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        digits: null == digits
            ? _self.digits
            : digits // ignore: cast_nullable_to_non_nullable
                  as int,
        icon: null == icon
            ? _self.icon
            : icon // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        serviceCharge: null == serviceCharge
            ? _self.serviceCharge
            : serviceCharge // ignore: cast_nullable_to_non_nullable
                  as int,
        standard: null == standard
            ? _self.standard
            : standard // ignore: cast_nullable_to_non_nullable
                  as String,
        symbol: null == symbol
            ? _self.symbol
            : symbol // ignore: cast_nullable_to_non_nullable
                  as String,
        totalSupply: null == totalSupply
            ? _self.totalSupply
            : totalSupply // ignore: cast_nullable_to_non_nullable
                  as int,
        weight: null == weight
            ? _self.weight
            : weight // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$TokenBalance {
  @JsonKey(includeFromJson: false, includeToJson: false)
  Token get token;
  @JsonKey(includeFromJson: false, includeToJson: false)
  Decimal get balance;
  @JsonKey(includeFromJson: false, includeToJson: false)
  double get price;
  @JsonKey(includeFromJson: false, includeToJson: false)
  String get usdValue;

  /// Create a copy of TokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TokenBalanceCopyWith<TokenBalance> get copyWith =>
      _$TokenBalanceCopyWithImpl<TokenBalance>(
        this as TokenBalance,
        _$identity,
      );

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TokenBalance &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.usdValue, usdValue) ||
                other.usdValue == usdValue));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token, balance, price, usdValue);

  @override
  String toString() {
    return 'TokenBalance(token: $token, balance: $balance, price: $price, usdValue: $usdValue)';
  }
}

/// @nodoc
abstract mixin class $TokenBalanceCopyWith<$Res> {
  factory $TokenBalanceCopyWith(
    TokenBalance value,
    $Res Function(TokenBalance) _then,
  ) = _$TokenBalanceCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(includeFromJson: false, includeToJson: false) Token token,
    @JsonKey(includeFromJson: false, includeToJson: false) Decimal balance,
    @JsonKey(includeFromJson: false, includeToJson: false) double price,
    @JsonKey(includeFromJson: false, includeToJson: false) String usdValue,
  });

  $TokenCopyWith<$Res> get token;
}

/// @nodoc
class _$TokenBalanceCopyWithImpl<$Res> implements $TokenBalanceCopyWith<$Res> {
  _$TokenBalanceCopyWithImpl(this._self, this._then);

  final TokenBalance _self;
  final $Res Function(TokenBalance) _then;

  /// Create a copy of TokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? token = null,
    Object? balance = null,
    Object? price = null,
    Object? usdValue = null,
  }) {
    return _then(
      _self.copyWith(
        token: null == token
            ? _self.token
            : token // ignore: cast_nullable_to_non_nullable
                  as Token,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        usdValue: null == usdValue
            ? _self.usdValue
            : usdValue // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }

  /// Create a copy of TokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TokenCopyWith<$Res> get token {
    return $TokenCopyWith<$Res>(_self.token, (value) {
      return _then(_self.copyWith(token: value));
    });
  }
}

/// @nodoc

class _TokenBalance implements TokenBalance {
  const _TokenBalance({
    @JsonKey(includeFromJson: false, includeToJson: false) required this.token,
    @JsonKey(includeFromJson: false, includeToJson: false)
    required this.balance,
    @JsonKey(includeFromJson: false, includeToJson: false) this.price = 0,
    @JsonKey(includeFromJson: false, includeToJson: false) this.usdValue = '',
  });

  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Token token;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Decimal balance;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final double price;
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String usdValue;

  /// Create a copy of TokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TokenBalanceCopyWith<_TokenBalance> get copyWith =>
      __$TokenBalanceCopyWithImpl<_TokenBalance>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TokenBalance &&
            (identical(other.token, token) || other.token == token) &&
            (identical(other.balance, balance) || other.balance == balance) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.usdValue, usdValue) ||
                other.usdValue == usdValue));
  }

  @override
  int get hashCode => Object.hash(runtimeType, token, balance, price, usdValue);

  @override
  String toString() {
    return 'TokenBalance(token: $token, balance: $balance, price: $price, usdValue: $usdValue)';
  }
}

/// @nodoc
abstract mixin class _$TokenBalanceCopyWith<$Res>
    implements $TokenBalanceCopyWith<$Res> {
  factory _$TokenBalanceCopyWith(
    _TokenBalance value,
    $Res Function(_TokenBalance) _then,
  ) = __$TokenBalanceCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(includeFromJson: false, includeToJson: false) Token token,
    @JsonKey(includeFromJson: false, includeToJson: false) Decimal balance,
    @JsonKey(includeFromJson: false, includeToJson: false) double price,
    @JsonKey(includeFromJson: false, includeToJson: false) String usdValue,
  });

  @override
  $TokenCopyWith<$Res> get token;
}

/// @nodoc
class __$TokenBalanceCopyWithImpl<$Res>
    implements _$TokenBalanceCopyWith<$Res> {
  __$TokenBalanceCopyWithImpl(this._self, this._then);

  final _TokenBalance _self;
  final $Res Function(_TokenBalance) _then;

  /// Create a copy of TokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? token = null,
    Object? balance = null,
    Object? price = null,
    Object? usdValue = null,
  }) {
    return _then(
      _TokenBalance(
        token: null == token
            ? _self.token
            : token // ignore: cast_nullable_to_non_nullable
                  as Token,
        balance: null == balance
            ? _self.balance
            : balance // ignore: cast_nullable_to_non_nullable
                  as Decimal,
        price: null == price
            ? _self.price
            : price // ignore: cast_nullable_to_non_nullable
                  as double,
        usdValue: null == usdValue
            ? _self.usdValue
            : usdValue // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }

  /// Create a copy of TokenBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TokenCopyWith<$Res> get token {
    return $TokenCopyWith<$Res>(_self.token, (value) {
      return _then(_self.copyWith(token: value));
    });
  }
}

/// @nodoc
mixin _$Message {
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'message')
  String get message;
  @JsonKey(name: 'params')
  String get params;
  @JsonKey(name: 'messageType')
  String get messageType;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MessageCopyWith<Message> get copyWith =>
      _$MessageCopyWithImpl<Message>(this as Message, _$identity);

  /// Serializes this Message to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Message &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.params, params) || other.params == params) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, id, message, params, messageType);

  @override
  String toString() {
    return 'Message(createTime: $createTime, id: $id, message: $message, params: $params, messageType: $messageType)';
  }
}

/// @nodoc
abstract mixin class $MessageCopyWith<$Res> {
  factory $MessageCopyWith(Message value, $Res Function(Message) _then) =
      _$MessageCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'message') String message,
    @JsonKey(name: 'params') String params,
    @JsonKey(name: 'messageType') String messageType,
  });
}

/// @nodoc
class _$MessageCopyWithImpl<$Res> implements $MessageCopyWith<$Res> {
  _$MessageCopyWithImpl(this._self, this._then);

  final Message _self;
  final $Res Function(Message) _then;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? id = null,
    Object? message = null,
    Object? params = null,
    Object? messageType = null,
  }) {
    return _then(
      _self.copyWith(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        params: null == params
            ? _self.params
            : params // ignore: cast_nullable_to_non_nullable
                  as String,
        messageType: null == messageType
            ? _self.messageType
            : messageType // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Message implements Message {
  const _Message({
    @JsonKey(name: 'createTime') required this.createTime,
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'message') required this.message,
    @JsonKey(name: 'params') this.params = '',
    @JsonKey(name: 'messageType') required this.messageType,
  });
  factory _Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);

  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'message')
  final String message;
  @override
  @JsonKey(name: 'params')
  final String params;
  @override
  @JsonKey(name: 'messageType')
  final String messageType;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MessageCopyWith<_Message> get copyWith =>
      __$MessageCopyWithImpl<_Message>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MessageToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Message &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.params, params) || other.params == params) &&
            (identical(other.messageType, messageType) ||
                other.messageType == messageType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, id, message, params, messageType);

  @override
  String toString() {
    return 'Message(createTime: $createTime, id: $id, message: $message, params: $params, messageType: $messageType)';
  }
}

/// @nodoc
abstract mixin class _$MessageCopyWith<$Res> implements $MessageCopyWith<$Res> {
  factory _$MessageCopyWith(_Message value, $Res Function(_Message) _then) =
      __$MessageCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'message') String message,
    @JsonKey(name: 'params') String params,
    @JsonKey(name: 'messageType') String messageType,
  });
}

/// @nodoc
class __$MessageCopyWithImpl<$Res> implements _$MessageCopyWith<$Res> {
  __$MessageCopyWithImpl(this._self, this._then);

  final _Message _self;
  final $Res Function(_Message) _then;

  /// Create a copy of Message
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createTime = null,
    Object? id = null,
    Object? message = null,
    Object? params = null,
    Object? messageType = null,
  }) {
    return _then(
      _Message(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        message: null == message
            ? _self.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        params: null == params
            ? _self.params
            : params // ignore: cast_nullable_to_non_nullable
                  as String,
        messageType: null == messageType
            ? _self.messageType
            : messageType // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$Point {
  @JsonKey(name: 'action')
  String get action;
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'description')
  String get description;
  @JsonKey(name: 'integral')
  int get integral;
  @JsonKey(name: 'ip')
  String get ip;
  @JsonKey(name: 'id')
  int get id;
  @JsonKey(name: 'contentId')
  int get contentId;
  @JsonKey(name: 'contentType')
  String get contentType;
  @JsonKey(name: 'userId')
  int get userId;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PointCopyWith<Point> get copyWith =>
      _$PointCopyWithImpl<Point>(this as Point, _$identity);

  /// Serializes this Point to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Point &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.ip, ip) || other.ip == ip) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contentId, contentId) ||
                other.contentId == contentId) &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    action,
    createTime,
    description,
    integral,
    ip,
    id,
    contentId,
    contentType,
    userId,
  );

  @override
  String toString() {
    return 'Point(action: $action, createTime: $createTime, description: $description, integral: $integral, ip: $ip, id: $id, contentId: $contentId, contentType: $contentType, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class $PointCopyWith<$Res> {
  factory $PointCopyWith(Point value, $Res Function(Point) _then) =
      _$PointCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'action') String action,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'ip') String ip,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'contentId') int contentId,
    @JsonKey(name: 'contentType') String contentType,
    @JsonKey(name: 'userId') int userId,
  });
}

/// @nodoc
class _$PointCopyWithImpl<$Res> implements $PointCopyWith<$Res> {
  _$PointCopyWithImpl(this._self, this._then);

  final Point _self;
  final $Res Function(Point) _then;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? action = null,
    Object? createTime = null,
    Object? description = null,
    Object? integral = null,
    Object? ip = null,
    Object? id = null,
    Object? contentId = null,
    Object? contentType = null,
    Object? userId = null,
  }) {
    return _then(
      _self.copyWith(
        action: null == action
            ? _self.action
            : action // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        ip: null == ip
            ? _self.ip
            : ip // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        contentId: null == contentId
            ? _self.contentId
            : contentId // ignore: cast_nullable_to_non_nullable
                  as int,
        contentType: null == contentType
            ? _self.contentType
            : contentType // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Point implements Point {
  const _Point({
    @JsonKey(name: 'action') this.action = '',
    @JsonKey(name: 'createTime') required this.createTime,
    @JsonKey(name: 'description') required this.description,
    @JsonKey(name: 'integral') required this.integral,
    @JsonKey(name: 'ip') this.ip = '',
    @JsonKey(name: 'id') required this.id,
    @JsonKey(name: 'contentId') this.contentId = 0,
    @JsonKey(name: 'contentType') this.contentType = '',
    @JsonKey(name: 'userId') required this.userId,
  });
  factory _Point.fromJson(Map<String, dynamic> json) => _$PointFromJson(json);

  @override
  @JsonKey(name: 'action')
  final String action;
  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'description')
  final String description;
  @override
  @JsonKey(name: 'integral')
  final int integral;
  @override
  @JsonKey(name: 'ip')
  final String ip;
  @override
  @JsonKey(name: 'id')
  final int id;
  @override
  @JsonKey(name: 'contentId')
  final int contentId;
  @override
  @JsonKey(name: 'contentType')
  final String contentType;
  @override
  @JsonKey(name: 'userId')
  final int userId;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PointCopyWith<_Point> get copyWith =>
      __$PointCopyWithImpl<_Point>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PointToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Point &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.ip, ip) || other.ip == ip) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.contentId, contentId) ||
                other.contentId == contentId) &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.userId, userId) || other.userId == userId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    action,
    createTime,
    description,
    integral,
    ip,
    id,
    contentId,
    contentType,
    userId,
  );

  @override
  String toString() {
    return 'Point(action: $action, createTime: $createTime, description: $description, integral: $integral, ip: $ip, id: $id, contentId: $contentId, contentType: $contentType, userId: $userId)';
  }
}

/// @nodoc
abstract mixin class _$PointCopyWith<$Res> implements $PointCopyWith<$Res> {
  factory _$PointCopyWith(_Point value, $Res Function(_Point) _then) =
      __$PointCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'action') String action,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'description') String description,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'ip') String ip,
    @JsonKey(name: 'id') int id,
    @JsonKey(name: 'contentId') int contentId,
    @JsonKey(name: 'contentType') String contentType,
    @JsonKey(name: 'userId') int userId,
  });
}

/// @nodoc
class __$PointCopyWithImpl<$Res> implements _$PointCopyWith<$Res> {
  __$PointCopyWithImpl(this._self, this._then);

  final _Point _self;
  final $Res Function(_Point) _then;

  /// Create a copy of Point
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? action = null,
    Object? createTime = null,
    Object? description = null,
    Object? integral = null,
    Object? ip = null,
    Object? id = null,
    Object? contentId = null,
    Object? contentType = null,
    Object? userId = null,
  }) {
    return _then(
      _Point(
        action: null == action
            ? _self.action
            : action // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        description: null == description
            ? _self.description
            : description // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        ip: null == ip
            ? _self.ip
            : ip // ignore: cast_nullable_to_non_nullable
                  as String,
        id: null == id
            ? _self.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        contentId: null == contentId
            ? _self.contentId
            : contentId // ignore: cast_nullable_to_non_nullable
                  as int,
        contentType: null == contentType
            ? _self.contentType
            : contentType // ignore: cast_nullable_to_non_nullable
                  as String,
        userId: null == userId
            ? _self.userId
            : userId // ignore: cast_nullable_to_non_nullable
                  as int,
      ),
    );
  }
}

/// @nodoc
mixin _$Connection {
  @JsonKey(name: 'cardCode')
  String get cardCode;
  @JsonKey(name: 'city')
  String get city;
  @JsonKey(name: 'image')
  String get image;
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'name')
  String get name;
  @JsonKey(name: 'senderAvatar')
  String get senderAvatar;
  @JsonKey(name: 'twitter')
  String get twitter;
  @JsonKey(name: 'uniqId')
  String get uniqId;
  @JsonKey(name: 'verifyStatus')
  VerifyStatus get verifyStatus;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConnectionCopyWith<Connection> get copyWith =>
      _$ConnectionCopyWithImpl<Connection>(this as Connection, _$identity);

  /// Serializes this Connection to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Connection &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.senderAvatar, senderAvatar) ||
                other.senderAvatar == senderAvatar) &&
            (identical(other.twitter, twitter) || other.twitter == twitter) &&
            (identical(other.uniqId, uniqId) || other.uniqId == uniqId) &&
            (identical(other.verifyStatus, verifyStatus) ||
                other.verifyStatus == verifyStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    cardCode,
    city,
    image,
    createTime,
    name,
    senderAvatar,
    twitter,
    uniqId,
    verifyStatus,
  );

  @override
  String toString() {
    return 'Connection(cardCode: $cardCode, city: $city, image: $image, createTime: $createTime, name: $name, senderAvatar: $senderAvatar, twitter: $twitter, uniqId: $uniqId, verifyStatus: $verifyStatus)';
  }
}

/// @nodoc
abstract mixin class $ConnectionCopyWith<$Res> {
  factory $ConnectionCopyWith(
    Connection value,
    $Res Function(Connection) _then,
  ) = _$ConnectionCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'city') String city,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'senderAvatar') String senderAvatar,
    @JsonKey(name: 'twitter') String twitter,
    @JsonKey(name: 'uniqId') String uniqId,
    @JsonKey(name: 'verifyStatus') VerifyStatus verifyStatus,
  });
}

/// @nodoc
class _$ConnectionCopyWithImpl<$Res> implements $ConnectionCopyWith<$Res> {
  _$ConnectionCopyWithImpl(this._self, this._then);

  final Connection _self;
  final $Res Function(Connection) _then;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cardCode = null,
    Object? city = null,
    Object? image = null,
    Object? createTime = null,
    Object? name = null,
    Object? senderAvatar = null,
    Object? twitter = null,
    Object? uniqId = null,
    Object? verifyStatus = null,
  }) {
    return _then(
      _self.copyWith(
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        city: null == city
            ? _self.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        senderAvatar: null == senderAvatar
            ? _self.senderAvatar
            : senderAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        twitter: null == twitter
            ? _self.twitter
            : twitter // ignore: cast_nullable_to_non_nullable
                  as String,
        uniqId: null == uniqId
            ? _self.uniqId
            : uniqId // ignore: cast_nullable_to_non_nullable
                  as String,
        verifyStatus: null == verifyStatus
            ? _self.verifyStatus
            : verifyStatus // ignore: cast_nullable_to_non_nullable
                  as VerifyStatus,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Connection implements Connection {
  const _Connection({
    @JsonKey(name: 'cardCode') this.cardCode = '',
    @JsonKey(name: 'city') this.city = '',
    @JsonKey(name: 'image') this.image = '',
    @JsonKey(name: 'createTime') this.createTime = '',
    @JsonKey(name: 'name') this.name = '',
    @JsonKey(name: 'senderAvatar') this.senderAvatar = '',
    @JsonKey(name: 'twitter') this.twitter = '',
    @JsonKey(name: 'uniqId') this.uniqId = '',
    @JsonKey(name: 'verifyStatus') this.verifyStatus = VerifyStatus.NOT_SURE,
  });
  factory _Connection.fromJson(Map<String, dynamic> json) =>
      _$ConnectionFromJson(json);

  @override
  @JsonKey(name: 'cardCode')
  final String cardCode;
  @override
  @JsonKey(name: 'city')
  final String city;
  @override
  @JsonKey(name: 'image')
  final String image;
  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'name')
  final String name;
  @override
  @JsonKey(name: 'senderAvatar')
  final String senderAvatar;
  @override
  @JsonKey(name: 'twitter')
  final String twitter;
  @override
  @JsonKey(name: 'uniqId')
  final String uniqId;
  @override
  @JsonKey(name: 'verifyStatus')
  final VerifyStatus verifyStatus;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConnectionCopyWith<_Connection> get copyWith =>
      __$ConnectionCopyWithImpl<_Connection>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConnectionToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Connection &&
            (identical(other.cardCode, cardCode) ||
                other.cardCode == cardCode) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.senderAvatar, senderAvatar) ||
                other.senderAvatar == senderAvatar) &&
            (identical(other.twitter, twitter) || other.twitter == twitter) &&
            (identical(other.uniqId, uniqId) || other.uniqId == uniqId) &&
            (identical(other.verifyStatus, verifyStatus) ||
                other.verifyStatus == verifyStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    cardCode,
    city,
    image,
    createTime,
    name,
    senderAvatar,
    twitter,
    uniqId,
    verifyStatus,
  );

  @override
  String toString() {
    return 'Connection(cardCode: $cardCode, city: $city, image: $image, createTime: $createTime, name: $name, senderAvatar: $senderAvatar, twitter: $twitter, uniqId: $uniqId, verifyStatus: $verifyStatus)';
  }
}

/// @nodoc
abstract mixin class _$ConnectionCopyWith<$Res>
    implements $ConnectionCopyWith<$Res> {
  factory _$ConnectionCopyWith(
    _Connection value,
    $Res Function(_Connection) _then,
  ) = __$ConnectionCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'cardCode') String cardCode,
    @JsonKey(name: 'city') String city,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'name') String name,
    @JsonKey(name: 'senderAvatar') String senderAvatar,
    @JsonKey(name: 'twitter') String twitter,
    @JsonKey(name: 'uniqId') String uniqId,
    @JsonKey(name: 'verifyStatus') VerifyStatus verifyStatus,
  });
}

/// @nodoc
class __$ConnectionCopyWithImpl<$Res> implements _$ConnectionCopyWith<$Res> {
  __$ConnectionCopyWithImpl(this._self, this._then);

  final _Connection _self;
  final $Res Function(_Connection) _then;

  /// Create a copy of Connection
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? cardCode = null,
    Object? city = null,
    Object? image = null,
    Object? createTime = null,
    Object? name = null,
    Object? senderAvatar = null,
    Object? twitter = null,
    Object? uniqId = null,
    Object? verifyStatus = null,
  }) {
    return _then(
      _Connection(
        cardCode: null == cardCode
            ? _self.cardCode
            : cardCode // ignore: cast_nullable_to_non_nullable
                  as String,
        city: null == city
            ? _self.city
            : city // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        name: null == name
            ? _self.name
            : name // ignore: cast_nullable_to_non_nullable
                  as String,
        senderAvatar: null == senderAvatar
            ? _self.senderAvatar
            : senderAvatar // ignore: cast_nullable_to_non_nullable
                  as String,
        twitter: null == twitter
            ? _self.twitter
            : twitter // ignore: cast_nullable_to_non_nullable
                  as String,
        uniqId: null == uniqId
            ? _self.uniqId
            : uniqId // ignore: cast_nullable_to_non_nullable
                  as String,
        verifyStatus: null == verifyStatus
            ? _self.verifyStatus
            : verifyStatus // ignore: cast_nullable_to_non_nullable
                  as VerifyStatus,
      ),
    );
  }
}

/// @nodoc
mixin _$ReferralLog {
  @JsonKey(name: 'createTime')
  String get createTime;
  @JsonKey(name: 'integral')
  int get integral;
  @JsonKey(name: 'inviteeEmail')
  String get inviteeEmail;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ReferralLogCopyWith<ReferralLog> get copyWith =>
      _$ReferralLogCopyWithImpl<ReferralLog>(this as ReferralLog, _$identity);

  /// Serializes this ReferralLog to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ReferralLog &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.inviteeEmail, inviteeEmail) ||
                other.inviteeEmail == inviteeEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, integral, inviteeEmail);

  @override
  String toString() {
    return 'ReferralLog(createTime: $createTime, integral: $integral, inviteeEmail: $inviteeEmail)';
  }
}

/// @nodoc
abstract mixin class $ReferralLogCopyWith<$Res> {
  factory $ReferralLogCopyWith(
    ReferralLog value,
    $Res Function(ReferralLog) _then,
  ) = _$ReferralLogCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'inviteeEmail') String inviteeEmail,
  });
}

/// @nodoc
class _$ReferralLogCopyWithImpl<$Res> implements $ReferralLogCopyWith<$Res> {
  _$ReferralLogCopyWithImpl(this._self, this._then);

  final ReferralLog _self;
  final $Res Function(ReferralLog) _then;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? createTime = null,
    Object? integral = null,
    Object? inviteeEmail = null,
  }) {
    return _then(
      _self.copyWith(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        inviteeEmail: null == inviteeEmail
            ? _self.inviteeEmail
            : inviteeEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _ReferralLog implements ReferralLog {
  const _ReferralLog({
    @JsonKey(name: 'createTime') this.createTime = '',
    @JsonKey(name: 'integral') this.integral = 0,
    @JsonKey(name: 'inviteeEmail') this.inviteeEmail = '',
  });
  factory _ReferralLog.fromJson(Map<String, dynamic> json) =>
      _$ReferralLogFromJson(json);

  @override
  @JsonKey(name: 'createTime')
  final String createTime;
  @override
  @JsonKey(name: 'integral')
  final int integral;
  @override
  @JsonKey(name: 'inviteeEmail')
  final String inviteeEmail;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ReferralLogCopyWith<_ReferralLog> get copyWith =>
      __$ReferralLogCopyWithImpl<_ReferralLog>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ReferralLogToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ReferralLog &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.integral, integral) ||
                other.integral == integral) &&
            (identical(other.inviteeEmail, inviteeEmail) ||
                other.inviteeEmail == inviteeEmail));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, createTime, integral, inviteeEmail);

  @override
  String toString() {
    return 'ReferralLog(createTime: $createTime, integral: $integral, inviteeEmail: $inviteeEmail)';
  }
}

/// @nodoc
abstract mixin class _$ReferralLogCopyWith<$Res>
    implements $ReferralLogCopyWith<$Res> {
  factory _$ReferralLogCopyWith(
    _ReferralLog value,
    $Res Function(_ReferralLog) _then,
  ) = __$ReferralLogCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'createTime') String createTime,
    @JsonKey(name: 'integral') int integral,
    @JsonKey(name: 'inviteeEmail') String inviteeEmail,
  });
}

/// @nodoc
class __$ReferralLogCopyWithImpl<$Res> implements _$ReferralLogCopyWith<$Res> {
  __$ReferralLogCopyWithImpl(this._self, this._then);

  final _ReferralLog _self;
  final $Res Function(_ReferralLog) _then;

  /// Create a copy of ReferralLog
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? createTime = null,
    Object? integral = null,
    Object? inviteeEmail = null,
  }) {
    return _then(
      _ReferralLog(
        createTime: null == createTime
            ? _self.createTime
            : createTime // ignore: cast_nullable_to_non_nullable
                  as String,
        integral: null == integral
            ? _self.integral
            : integral // ignore: cast_nullable_to_non_nullable
                  as int,
        inviteeEmail: null == inviteeEmail
            ? _self.inviteeEmail
            : inviteeEmail // ignore: cast_nullable_to_non_nullable
                  as String,
      ),
    );
  }
}

/// @nodoc
mixin _$EventItem {
  @JsonKey(name: 'title')
  String get title;
  @JsonKey(name: 'desc')
  String get desc;
  @JsonKey(name: 'image')
  String get image;
  @JsonKey(name: 'imageWidth')
  String get imageWidth;
  @JsonKey(name: 'link')
  String get link;
  @JsonKey(name: 'span')
  int? get span;
  @JsonKey(name: 'sort')
  int? get sort;
  @JsonKey(name: 'eventIds')
  List<int> get eventIds;

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EventItemCopyWith<EventItem> get copyWith =>
      _$EventItemCopyWithImpl<EventItem>(this as EventItem, _$identity);

  /// Serializes this EventItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EventItem &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.imageWidth, imageWidth) ||
                other.imageWidth == imageWidth) &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.span, span) || other.span == span) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            const DeepCollectionEquality().equals(other.eventIds, eventIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    title,
    desc,
    image,
    imageWidth,
    link,
    span,
    sort,
    const DeepCollectionEquality().hash(eventIds),
  );

  @override
  String toString() {
    return 'EventItem(title: $title, desc: $desc, image: $image, imageWidth: $imageWidth, link: $link, span: $span, sort: $sort, eventIds: $eventIds)';
  }
}

/// @nodoc
abstract mixin class $EventItemCopyWith<$Res> {
  factory $EventItemCopyWith(EventItem value, $Res Function(EventItem) _then) =
      _$EventItemCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'imageWidth') String imageWidth,
    @JsonKey(name: 'link') String link,
    @JsonKey(name: 'span') int? span,
    @JsonKey(name: 'sort') int? sort,
    @JsonKey(name: 'eventIds') List<int> eventIds,
  });
}

/// @nodoc
class _$EventItemCopyWithImpl<$Res> implements $EventItemCopyWith<$Res> {
  _$EventItemCopyWithImpl(this._self, this._then);

  final EventItem _self;
  final $Res Function(EventItem) _then;

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? desc = null,
    Object? image = null,
    Object? imageWidth = null,
    Object? link = null,
    Object? span = freezed,
    Object? sort = freezed,
    Object? eventIds = null,
  }) {
    return _then(
      _self.copyWith(
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        imageWidth: null == imageWidth
            ? _self.imageWidth
            : imageWidth // ignore: cast_nullable_to_non_nullable
                  as String,
        link: null == link
            ? _self.link
            : link // ignore: cast_nullable_to_non_nullable
                  as String,
        span: freezed == span
            ? _self.span
            : span // ignore: cast_nullable_to_non_nullable
                  as int?,
        sort: freezed == sort
            ? _self.sort
            : sort // ignore: cast_nullable_to_non_nullable
                  as int?,
        eventIds: null == eventIds
            ? _self.eventIds
            : eventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _EventItem implements EventItem {
  const _EventItem({
    @JsonKey(name: 'title') required this.title,
    @JsonKey(name: 'desc') required this.desc,
    @JsonKey(name: 'image') required this.image,
    @JsonKey(name: 'imageWidth') this.imageWidth = '',
    @JsonKey(name: 'link') required this.link,
    @JsonKey(name: 'span') this.span = 1,
    @JsonKey(name: 'sort') this.sort = 0,
    @JsonKey(name: 'eventIds') final List<int> eventIds = const [],
  }) : _eventIds = eventIds;
  factory _EventItem.fromJson(Map<String, dynamic> json) =>
      _$EventItemFromJson(json);

  @override
  @JsonKey(name: 'title')
  final String title;
  @override
  @JsonKey(name: 'desc')
  final String desc;
  @override
  @JsonKey(name: 'image')
  final String image;
  @override
  @JsonKey(name: 'imageWidth')
  final String imageWidth;
  @override
  @JsonKey(name: 'link')
  final String link;
  @override
  @JsonKey(name: 'span')
  final int? span;
  @override
  @JsonKey(name: 'sort')
  final int? sort;
  final List<int> _eventIds;
  @override
  @JsonKey(name: 'eventIds')
  List<int> get eventIds {
    if (_eventIds is EqualUnmodifiableListView) return _eventIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_eventIds);
  }

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EventItemCopyWith<_EventItem> get copyWith =>
      __$EventItemCopyWithImpl<_EventItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EventItemToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EventItem &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.imageWidth, imageWidth) ||
                other.imageWidth == imageWidth) &&
            (identical(other.link, link) || other.link == link) &&
            (identical(other.span, span) || other.span == span) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            const DeepCollectionEquality().equals(other._eventIds, _eventIds));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    title,
    desc,
    image,
    imageWidth,
    link,
    span,
    sort,
    const DeepCollectionEquality().hash(_eventIds),
  );

  @override
  String toString() {
    return 'EventItem(title: $title, desc: $desc, image: $image, imageWidth: $imageWidth, link: $link, span: $span, sort: $sort, eventIds: $eventIds)';
  }
}

/// @nodoc
abstract mixin class _$EventItemCopyWith<$Res>
    implements $EventItemCopyWith<$Res> {
  factory _$EventItemCopyWith(
    _EventItem value,
    $Res Function(_EventItem) _then,
  ) = __$EventItemCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'title') String title,
    @JsonKey(name: 'desc') String desc,
    @JsonKey(name: 'image') String image,
    @JsonKey(name: 'imageWidth') String imageWidth,
    @JsonKey(name: 'link') String link,
    @JsonKey(name: 'span') int? span,
    @JsonKey(name: 'sort') int? sort,
    @JsonKey(name: 'eventIds') List<int> eventIds,
  });
}

/// @nodoc
class __$EventItemCopyWithImpl<$Res> implements _$EventItemCopyWith<$Res> {
  __$EventItemCopyWithImpl(this._self, this._then);

  final _EventItem _self;
  final $Res Function(_EventItem) _then;

  /// Create a copy of EventItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? title = null,
    Object? desc = null,
    Object? image = null,
    Object? imageWidth = null,
    Object? link = null,
    Object? span = freezed,
    Object? sort = freezed,
    Object? eventIds = null,
  }) {
    return _then(
      _EventItem(
        title: null == title
            ? _self.title
            : title // ignore: cast_nullable_to_non_nullable
                  as String,
        desc: null == desc
            ? _self.desc
            : desc // ignore: cast_nullable_to_non_nullable
                  as String,
        image: null == image
            ? _self.image
            : image // ignore: cast_nullable_to_non_nullable
                  as String,
        imageWidth: null == imageWidth
            ? _self.imageWidth
            : imageWidth // ignore: cast_nullable_to_non_nullable
                  as String,
        link: null == link
            ? _self.link
            : link // ignore: cast_nullable_to_non_nullable
                  as String,
        span: freezed == span
            ? _self.span
            : span // ignore: cast_nullable_to_non_nullable
                  as int?,
        sort: freezed == sort
            ? _self.sort
            : sort // ignore: cast_nullable_to_non_nullable
                  as int?,
        eventIds: null == eventIds
            ? _self._eventIds
            : eventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
      ),
    );
  }
}

/// @nodoc
mixin _$Config {
  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  @JsonKey(name: 'chainTokenFilters')
  Map<String, List<String>> get chainTokenFilters;

  /// 支持的链ID列表
  /// 例如: [10, 324, 59144]
  @JsonKey(name: 'chainFilters')
  List<int> get chainFilters;

  /// 以太坊社区活动ID列表
  /// 例如: [10, 324, 59144]
  @JsonKey(name: 'ethccEventIds')
  List<int> get ethccEventIds;

  /// 默认模式
  @JsonKey(name: 'defaultMode')
  ProfileMode get defaultMode;

  /// 是否支持NFC
  @JsonKey(name: 'nfc')
  bool get nfc;

  /// 事件列表
  ///
  @JsonKey(name: 'events')
  List<EventItem> get events;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ConfigCopyWith<Config> get copyWith =>
      _$ConfigCopyWithImpl<Config>(this as Config, _$identity);

  /// Serializes this Config to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is Config &&
            const DeepCollectionEquality().equals(
              other.chainTokenFilters,
              chainTokenFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other.chainFilters,
              chainFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other.ethccEventIds,
              ethccEventIds,
            ) &&
            (identical(other.defaultMode, defaultMode) ||
                other.defaultMode == defaultMode) &&
            (identical(other.nfc, nfc) || other.nfc == nfc) &&
            const DeepCollectionEquality().equals(other.events, events));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(chainTokenFilters),
    const DeepCollectionEquality().hash(chainFilters),
    const DeepCollectionEquality().hash(ethccEventIds),
    defaultMode,
    nfc,
    const DeepCollectionEquality().hash(events),
  );

  @override
  String toString() {
    return 'Config(chainTokenFilters: $chainTokenFilters, chainFilters: $chainFilters, ethccEventIds: $ethccEventIds, defaultMode: $defaultMode, nfc: $nfc, events: $events)';
  }
}

/// @nodoc
abstract mixin class $ConfigCopyWith<$Res> {
  factory $ConfigCopyWith(Config value, $Res Function(Config) _then) =
      _$ConfigCopyWithImpl;
  @useResult
  $Res call({
    @JsonKey(name: 'chainTokenFilters')
    Map<String, List<String>> chainTokenFilters,
    @JsonKey(name: 'chainFilters') List<int> chainFilters,
    @JsonKey(name: 'ethccEventIds') List<int> ethccEventIds,
    @JsonKey(name: 'defaultMode') ProfileMode defaultMode,
    @JsonKey(name: 'nfc') bool nfc,
    @JsonKey(name: 'events') List<EventItem> events,
  });
}

/// @nodoc
class _$ConfigCopyWithImpl<$Res> implements $ConfigCopyWith<$Res> {
  _$ConfigCopyWithImpl(this._self, this._then);

  final Config _self;
  final $Res Function(Config) _then;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chainTokenFilters = null,
    Object? chainFilters = null,
    Object? ethccEventIds = null,
    Object? defaultMode = null,
    Object? nfc = null,
    Object? events = null,
  }) {
    return _then(
      _self.copyWith(
        chainTokenFilters: null == chainTokenFilters
            ? _self.chainTokenFilters
            : chainTokenFilters // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        chainFilters: null == chainFilters
            ? _self.chainFilters
            : chainFilters // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        ethccEventIds: null == ethccEventIds
            ? _self.ethccEventIds
            : ethccEventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        defaultMode: null == defaultMode
            ? _self.defaultMode
            : defaultMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        nfc: null == nfc
            ? _self.nfc
            : nfc // ignore: cast_nullable_to_non_nullable
                  as bool,
        events: null == events
            ? _self.events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<EventItem>,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _Config implements Config {
  const _Config({
    @JsonKey(name: 'chainTokenFilters')
    required final Map<String, List<String>> chainTokenFilters,
    @JsonKey(name: 'chainFilters') required final List<int> chainFilters,
    @JsonKey(name: 'ethccEventIds') final List<int> ethccEventIds = const [],
    @JsonKey(name: 'defaultMode') this.defaultMode = ProfileMode.ETHCC,
    @JsonKey(name: 'nfc') this.nfc = false,
    @JsonKey(name: 'events') required final List<EventItem> events,
  }) : _chainTokenFilters = chainTokenFilters,
       _chainFilters = chainFilters,
       _ethccEventIds = ethccEventIds,
       _events = events;
  factory _Config.fromJson(Map<String, dynamic> json) => _$ConfigFromJson(json);

  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  final Map<String, List<String>> _chainTokenFilters;

  /// chainTokenFilters 示例:
  /// {
  ///   "1": ["ETH", "USDC", "USDT"],
  ///   "8453": ["ETH", "USDT", "USDC", "DEGEN"],
  ///   ...
  /// }
  @override
  @JsonKey(name: 'chainTokenFilters')
  Map<String, List<String>> get chainTokenFilters {
    if (_chainTokenFilters is EqualUnmodifiableMapView)
      return _chainTokenFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_chainTokenFilters);
  }

  /// 支持的链ID列表
  /// 例如: [10, 324, 59144]
  final List<int> _chainFilters;

  /// 支持的链ID列表
  /// 例如: [10, 324, 59144]
  @override
  @JsonKey(name: 'chainFilters')
  List<int> get chainFilters {
    if (_chainFilters is EqualUnmodifiableListView) return _chainFilters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_chainFilters);
  }

  /// 以太坊社区活动ID列表
  /// 例如: [10, 324, 59144]
  final List<int> _ethccEventIds;

  /// 以太坊社区活动ID列表
  /// 例如: [10, 324, 59144]
  @override
  @JsonKey(name: 'ethccEventIds')
  List<int> get ethccEventIds {
    if (_ethccEventIds is EqualUnmodifiableListView) return _ethccEventIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_ethccEventIds);
  }

  /// 默认模式
  @override
  @JsonKey(name: 'defaultMode')
  final ProfileMode defaultMode;

  /// 是否支持NFC
  @override
  @JsonKey(name: 'nfc')
  final bool nfc;

  /// 事件列表
  ///
  final List<EventItem> _events;

  /// 事件列表
  ///
  @override
  @JsonKey(name: 'events')
  List<EventItem> get events {
    if (_events is EqualUnmodifiableListView) return _events;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_events);
  }

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ConfigCopyWith<_Config> get copyWith =>
      __$ConfigCopyWithImpl<_Config>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ConfigToJson(this);
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _Config &&
            const DeepCollectionEquality().equals(
              other._chainTokenFilters,
              _chainTokenFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other._chainFilters,
              _chainFilters,
            ) &&
            const DeepCollectionEquality().equals(
              other._ethccEventIds,
              _ethccEventIds,
            ) &&
            (identical(other.defaultMode, defaultMode) ||
                other.defaultMode == defaultMode) &&
            (identical(other.nfc, nfc) || other.nfc == nfc) &&
            const DeepCollectionEquality().equals(other._events, _events));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    const DeepCollectionEquality().hash(_chainTokenFilters),
    const DeepCollectionEquality().hash(_chainFilters),
    const DeepCollectionEquality().hash(_ethccEventIds),
    defaultMode,
    nfc,
    const DeepCollectionEquality().hash(_events),
  );

  @override
  String toString() {
    return 'Config(chainTokenFilters: $chainTokenFilters, chainFilters: $chainFilters, ethccEventIds: $ethccEventIds, defaultMode: $defaultMode, nfc: $nfc, events: $events)';
  }
}

/// @nodoc
abstract mixin class _$ConfigCopyWith<$Res> implements $ConfigCopyWith<$Res> {
  factory _$ConfigCopyWith(_Config value, $Res Function(_Config) _then) =
      __$ConfigCopyWithImpl;
  @override
  @useResult
  $Res call({
    @JsonKey(name: 'chainTokenFilters')
    Map<String, List<String>> chainTokenFilters,
    @JsonKey(name: 'chainFilters') List<int> chainFilters,
    @JsonKey(name: 'ethccEventIds') List<int> ethccEventIds,
    @JsonKey(name: 'defaultMode') ProfileMode defaultMode,
    @JsonKey(name: 'nfc') bool nfc,
    @JsonKey(name: 'events') List<EventItem> events,
  });
}

/// @nodoc
class __$ConfigCopyWithImpl<$Res> implements _$ConfigCopyWith<$Res> {
  __$ConfigCopyWithImpl(this._self, this._then);

  final _Config _self;
  final $Res Function(_Config) _then;

  /// Create a copy of Config
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? chainTokenFilters = null,
    Object? chainFilters = null,
    Object? ethccEventIds = null,
    Object? defaultMode = null,
    Object? nfc = null,
    Object? events = null,
  }) {
    return _then(
      _Config(
        chainTokenFilters: null == chainTokenFilters
            ? _self._chainTokenFilters
            : chainTokenFilters // ignore: cast_nullable_to_non_nullable
                  as Map<String, List<String>>,
        chainFilters: null == chainFilters
            ? _self._chainFilters
            : chainFilters // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        ethccEventIds: null == ethccEventIds
            ? _self._ethccEventIds
            : ethccEventIds // ignore: cast_nullable_to_non_nullable
                  as List<int>,
        defaultMode: null == defaultMode
            ? _self.defaultMode
            : defaultMode // ignore: cast_nullable_to_non_nullable
                  as ProfileMode,
        nfc: null == nfc
            ? _self.nfc
            : nfc // ignore: cast_nullable_to_non_nullable
                  as bool,
        events: null == events
            ? _self._events
            : events // ignore: cast_nullable_to_non_nullable
                  as List<EventItem>,
      ),
    );
  }
}
