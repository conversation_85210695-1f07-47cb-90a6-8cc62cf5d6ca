import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/chain.dart' show walletAddressProvider;

@FFRoute(name: '/setting/wallets')
class WalletsPage extends ConsumerWidget {
  const WalletsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final walletAddress = ref.watch(walletAddressProvider);

    // 假设你有SVG或PNG资源，替换为你的实际资源路径
    final chainIcons = [
      'https://s3.ap-east-1.amazonaws.com/op.astrox.app/image/chain_icon/20230109/polygon.svg',
      'https://s3.ap-east-1.amazonaws.com/op.astrox.app/image/token_icon/20230517/arbitrum-arb-logo-5.svg',
      'https://s3.ap-east-1.amazonaws.com/op.astrox.app/image/token_icon/20230822/base.png',
    ];

    return AppScaffold(
      title: 'Wallets Management',
      bodyPadding: const EdgeInsets.all(20),
      body: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: context.theme.cardColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题和链图标
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'EVM Wallet',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: ColorName.cardColorDark,
                      ),
                    ),
                    Row(
                      children: chainIcons
                          .map(
                            (icon) => Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 4),
                              child: MEImage(
                                icon,
                                width: 36,
                                height: 36,
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // 钱包地址
                Text(
                  walletAddress ?? '--',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFFB0B0B8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
