// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'astrox_gw.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_AstroxTicker _$AstroxTickerFromJson(Map json) => _AstroxTicker(
  price: (json['price'] as num?)?.toDouble() ?? 0,
  symbol: json['symbol'] as String,
  timestamp: (json['timestamp'] as num).toInt(),
);

Map<String, dynamic> _$AstroxTickerToJson(_AstroxTicker instance) =>
    <String, dynamic>{
      'price': instance.price,
      'symbol': instance.symbol,
      'timestamp': instance.timestamp,
    };
