import 'dart:convert';
import 'dart:typed_data';
import 'package:card3/exports.dart';
import 'package:flutter/material.dart';
import '../../../models/card.dart';
import '../../widgets/social/profile/avatar_img.dart';

class NormalCustomizeView extends StatelessWidget {
  const NormalCustomizeView({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.titleController,
    required this.companyController,
    required this.selectedImageBase64,
    required this.coverInfo,
    required this.isLoading,
    required this.avatarPickerKey,
    required this.onImageSelected,
    required this.onRemoveImage,
    required this.onNext,
    required this.onConfirm,
    required this.onBack,
    required this.validateName,
    required this.validateTitle,
    required this.validateCompany,
    required this.calculateFontSize,
    this.isConfirmView = false,
  });

  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController titleController;
  final TextEditingController companyController;
  final String? selectedImageBase64;
  final CoverInfo? coverInfo;
  final bool isLoading;
  final GlobalKey<AvatarImgPickerState> avatarPickerKey;
  final Function(String, Uint8List) onImageSelected;
  final Function() onRemoveImage;
  final Function() onNext;
  final Function() onConfirm;
  final Function() onBack;
  final String? Function(String?) validateName;
  final String? Function(String?) validateTitle;
  final String? Function(String?) validateCompany;
  final double Function(String) calculateFontSize;
  final bool isConfirmView;

  @override
  Widget build(BuildContext context) {
    if (isConfirmView) {
      return _buildConfirmView(context);
    } else {
      return _buildMainView(context);
    }
  }

  Widget _buildMainView(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      appBar: AppBar(
        backgroundColor: const Color(0xFFf1f1f1),
        elevation: 0,
        leading: const AppBackButton(),
      ),
      body: Form(
        key: formKey,
        child: Column(
          children: [
            // 卡片预览区域
            _buildCardPreview(),

            // 表单输入区域
            Expanded(
              child: Container(
                color: Colors.white,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // 图片上传区域
                      _buildImageUploadSection(),
                      const SizedBox(height: 24),

                      // 表单字段
                      _buildFormFields(),
                      const SizedBox(height: 24),
                      // 底部按钮
                      _buildBottomButton(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfirmView(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFf1f1f1),
      appBar: AppBar(
        backgroundColor: const Color(0xFFf1f1f1),
        elevation: 0,
        leading: AppBackButton(
          onPressed: onBack,
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 100),
                  // Front/Back 标签
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 32),
                    child: Row(
                      children: [
                        Expanded(
                          child: Center(
                            child: Text(
                              'Front',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: Center(
                            child: Text(
                              'Back',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  // 卡片预览
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 正面卡片
                        Flexible(child: _buildFrontCard()),
                        const SizedBox(width: 20),
                        // 背面卡片
                        Flexible(child: _buildBackCard()),
                      ],
                    ),
                  ),
                  const SizedBox(height: 60),
                ],
              ),
            ),
          ),
          // 底部确认按钮
          Container(
            padding: const EdgeInsets.fromLTRB(24, 0, 24, 40),
            child: SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: isLoading ? null : onConfirm,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorName.themeColorDark,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'Confirm',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardPreview() {
    return Container(
      height: 280,
      color: const Color(0xFFf1f1f1),
      child: Center(
        child: Container(
          width: 140,
          height: 220,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Stack(
            children: [
              // 使用 normalBackcover 作为背景
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Assets.icons.images.normalFrontcover.image(
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Column(
                children: [
                  // 图片区域
                  GestureDetector(
                    onTap: () {
                      avatarPickerKey.currentState?.pickImage();
                    },
                    child: Container(
                      width: 140,
                      height: 140,
                      decoration: const BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                        child: selectedImageBase64 != null
                            ? (selectedImageBase64!.startsWith('data:image')
                                  ? _buildBase64Image(selectedImageBase64!)
                                  : MEImage(
                                      selectedImageBase64!,
                                      fit: BoxFit.cover,
                                      clipOval: false,
                                      alternativeSVG: true,
                                    ))
                            : const Icon(
                                Icons.add,
                                size: 56,
                                color: ColorName.themeColorDark,
                              ),
                      ),
                    ),
                  ),

                  // 文字信息区域 - 移除渐变背景，使用透明背景让底层的 normalBackcover 显示
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      decoration: const BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              nameController.text.isEmpty ? 'Your name' : nameController.text,
                              style: TextStyle(
                                fontSize: calculateFontSize(nameController.text),
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (titleController.text.isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                titleController.text,
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Colors.black45,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                            if (companyController.text.isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                companyController.text,
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Colors.black45,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageUploadSection() {
    return SizedBox(
      width: double.infinity,
      height: 100,
      child: CustomPaint(
        painter: DashedBorderPainter(),
        child: selectedImageBase64 != null
            ? Stack(
                children: [
                  Center(
                    child: SizedBox(
                      width: 80,
                      height: 80,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: selectedImageBase64!.startsWith('data:image')
                            ? _buildBase64Image(
                                selectedImageBase64!,
                                width: 80,
                                height: 80,
                              )
                            : MEImage(
                                selectedImageBase64!,
                                fit: BoxFit.cover,
                                clipOval: false,
                                alternativeSVG: true,
                              ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: onRemoveImage,
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.red.shade500,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.remove,
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            : GestureDetector(
                onTap: () {
                  avatarPickerKey.currentState?.pickImage();
                },
                child: Container(
                  width: double.infinity,
                  height: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.add,
                        size: 56,
                        color: ColorName.themeColorDark,
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Your Picture',
                              style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            Text(
                              'NFT, Selfie, Logo...',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // 隐藏的AvatarImgPicker组件
                      Opacity(
                        opacity: 0,
                        child: SizedBox(
                          width: 1,
                          height: 1,
                          child: AvatarImgPicker(
                            key: avatarPickerKey,
                            avatarUrl: selectedImageBase64,
                            onImageSelected: onImageSelected,
                            size: 1,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildBase64Image(
    String base64String, {
    double? width,
    double? height,
  }) {
    final base64Data = base64String.split(',').last;
    final imageBytes = base64Decode(base64Data);
    return Image.memory(
      imageBytes,
      fit: BoxFit.cover,
      width: width,
      height: height,
      errorBuilder: (context, error, stackTrace) {
        handleExceptions(error: error, stackTrace: stackTrace);
        return const Icon(
          Icons.broken_image,
          size: 50,
          color: Colors.grey,
        );
      },
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // 姓名输入框
        TextFormField(
          controller: nameController,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.grey[100],
            hintText: 'Your name',
            errorStyle: const TextStyle(
              color: Colors.red,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ColorName.themeColorDark, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateName,
          onChanged: (value) => {/* setState will be handled by parent */},
        ),
        const SizedBox(height: 16),

        // 职位输入框
        TextFormField(
          controller: titleController,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.grey[100],
            hintText: 'Title (Optional)',
            errorStyle: const TextStyle(
              color: Colors.red,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ColorName.themeColorDark, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateTitle,
          onChanged: (value) => {/* setState will be handled by parent */},
        ),
        const SizedBox(height: 16),

        // 公司输入框
        TextFormField(
          controller: companyController,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.grey[100],
            hintText: 'Company (Optional)',
            errorStyle: const TextStyle(
              color: Colors.red,
              fontSize: 14,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ColorName.themeColorDark, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
          validator: validateCompany,
          onChanged: (value) => {/* setState will be handled by parent */},
        ),
      ],
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: onNext,
          style: ElevatedButton.styleFrom(
            backgroundColor: ColorName.themeColorDark,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: const Text(
            'Next',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  // 构建正面卡片
  Widget _buildFrontCard() {
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 使用 normalBackcover 作为背景
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Assets.icons.images.normalFrontcover.image(
                fit: BoxFit.cover,
              ),
            ),
          ),
          Column(
            children: [
              // 图片区域
              Container(
                width: 140,
                height: 140,
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: selectedImageBase64 != null
                      ? (selectedImageBase64!.startsWith('data:image')
                            ? _buildBase64Image(selectedImageBase64!)
                            : MEImage(
                                selectedImageBase64!,
                                fit: BoxFit.cover,
                                clipOval: false,
                                alternativeSVG: true,
                              ))
                      : Container(
                          color: Colors.grey[100],
                          child: const Icon(
                            Icons.person,
                            size: 60,
                            color: Colors.grey,
                          ),
                        ),
                ),
              ),
              // 文字信息区域 - 移除渐变背景
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(16),
                      bottomRight: Radius.circular(16),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          nameController.text.isEmpty ? 'Your name' : nameController.text,
                          style: TextStyle(
                            fontSize: calculateFontSize(nameController.text),
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (titleController.text.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            titleController.text,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        if (companyController.text.isNotEmpty) ...[
                          const SizedBox(height: 2),
                          Text(
                            companyController.text,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建背面卡片
  Widget _buildBackCard() {
    return Container(
      width: 140,
      height: 220,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: coverInfo?.backCover.isNotEmpty == true
            ? MEImage(
                coverInfo!.backCover,
                fit: BoxFit.cover,
                width: 140,
                height: 220,
              )
            : Assets.icons.images.normalBackcover.image(
                fit: BoxFit.cover,
                width: 140,
                height: 220,
              ),
      ),
    );
  }
}

/// 虚线边框画笔
class DashedBorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.grey.shade400
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashWidth = 8.0;
    const dashSpace = 6.0;

    final path = Path()
      ..addRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(0, 0, size.width, size.height),
          const Radius.circular(16),
        ),
      );

    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final extractPath = pathMetric.extractPath(
          distance,
          distance + dashWidth,
        );
        canvas.drawPath(extractPath, paint);
        distance += dashWidth + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
