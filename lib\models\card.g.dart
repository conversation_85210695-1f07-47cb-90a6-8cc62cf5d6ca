// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_Social _$SocialFromJson(Map json) => _Social(
  handleName: json['handleName'] as String,
  id: (json['id'] as num).toInt(),
  platformName: json['platformName'] as String,
  platformUrl: json['platformUrl'] as String,
  isVerify: json['isVerify'] as bool? ?? false,
);

Map<String, dynamic> _$SocialTo<PERSON>son(_Social instance) => <String, dynamic>{
  'handleName': instance.handleName,
  'id': instance.id,
  'platformName': instance.platformName,
  'platformUrl': instance.platformUrl,
  'isVerify': instance.isVerify,
};

_CardInfo _$CardInfoFromJson(Map json) => _CardInfo(
  active: json['active'] as bool,
  activeTime: json['activeTime'] as String? ?? '',
  backCover: json['backCover'] as String? ?? '',
  card3EventId: (json['card3EventId'] as num?)?.toInt() ?? 0,
  cardCode: json['cardCode'] as String? ?? '',
  cardType:
      $enumDecodeNullable(_$CardTypeEnumMap, json['cardType']) ?? CardType.CARD,
  chainId: (json['chainId'] as num?)?.toInt() ?? 0,
  eventName: json['eventName'] as String? ?? '',
  id: (json['id'] as num?)?.toInt() ?? 0,
  isActive: json['isActive'] as bool? ?? false,
  referralCode: json['referralCode'] as String? ?? '',
  virtualCard: json['virtualCard'] as bool? ?? false,
  nfcType:
      $enumDecodeNullable(_$NfcTypeEnumMap, json['nfcType']) ?? NfcType.NFC215,
);

Map<String, dynamic> _$CardInfoToJson(_CardInfo instance) => <String, dynamic>{
  'active': instance.active,
  'activeTime': instance.activeTime,
  'backCover': instance.backCover,
  'card3EventId': instance.card3EventId,
  'cardCode': instance.cardCode,
  'cardType': _$CardTypeEnumMap[instance.cardType]!,
  'chainId': instance.chainId,
  'eventName': instance.eventName,
  'id': instance.id,
  'isActive': instance.isActive,
  'referralCode': instance.referralCode,
  'virtualCard': instance.virtualCard,
  'nfcType': _$NfcTypeEnumMap[instance.nfcType]!,
};

const _$CardTypeEnumMap = {
  CardType.STICKER: 'STICKER',
  CardType.CARD: 'CARD',
  CardType.WRISTBAND: 'WRISTBAND',
};

const _$NfcTypeEnumMap = {NfcType.NFC215: 'NFC215', NfcType.NFC424: 'NFC424'};

_CoverInfo _$CoverInfoFromJson(Map json) => _CoverInfo(
  activeMode: json['activeMode'] as String? ?? '',
  backCover: json['backCover'] as String? ?? '',
  eventId: json['eventId'] as String? ?? '',
  eventName: json['eventName'] as String? ?? '',
  paymentLink: json['paymentLink'] as String? ?? '',
  price: (json['price'] as num?)?.toInt() ?? 0,
  priceDescription: json['priceDescription'] as String? ?? '',
  printType:
      $enumDecodeNullable(_$PrintTypeEnumMap, json['printType']) ??
      PrintType.NORMAL,
  thirdPartyLink: json['thirdPartyLink'] as String? ?? '',
);

Map<String, dynamic> _$CoverInfoToJson(_CoverInfo instance) =>
    <String, dynamic>{
      'activeMode': instance.activeMode,
      'backCover': instance.backCover,
      'eventId': instance.eventId,
      'eventName': instance.eventName,
      'paymentLink': instance.paymentLink,
      'price': instance.price,
      'priceDescription': instance.priceDescription,
      'printType': _$PrintTypeEnumMap[instance.printType]!,
      'thirdPartyLink': instance.thirdPartyLink,
    };

const _$PrintTypeEnumMap = {
  PrintType.METAL: 'METAL',
  PrintType.NORMAL: 'NORMAL',
};

_CreateCardCoverResponse _$CreateCardCoverResponseFromJson(Map json) =>
    _CreateCardCoverResponse(
      code: json['code'] as String,
      paymentLink: json['paymentLink'] as String? ?? '',
    );

Map<String, dynamic> _$CreateCardCoverResponseToJson(
  _CreateCardCoverResponse instance,
) => <String, dynamic>{
  'code': instance.code,
  'paymentLink': instance.paymentLink,
};

_EthccProfile _$EthccProfileFromJson(Map json) => _EthccProfile(
  githubHandle: json['githubHandle'] as String? ?? '',
  topics: json['topics'] == null
      ? const []
      : EthccProfile.fromJoinedString(json['topics'] as String),
  roles: json['role'] == null
      ? const []
      : EthccProfile.fromJoinedString(json['role'] as String),
);

Map<String, dynamic> _$EthccProfileToJson(_EthccProfile instance) =>
    <String, dynamic>{
      'githubHandle': instance.githubHandle,
      'topics': EthccProfile.toJoinedString(instance.topics),
      'role': EthccProfile.toJoinedString(instance.roles),
    };

_UpdateEthccTopicsRequest _$UpdateEthccTopicsRequestFromJson(Map json) =>
    _UpdateEthccTopicsRequest(topics: json['topics'] as String);

Map<String, dynamic> _$UpdateEthccTopicsRequestToJson(
  _UpdateEthccTopicsRequest instance,
) => <String, dynamic>{'topics': instance.topics};

_UpdateEthccGithubRequest _$UpdateEthccGithubRequestFromJson(Map json) =>
    _UpdateEthccGithubRequest(githubHandle: json['githubHandle'] as String);

Map<String, dynamic> _$UpdateEthccGithubRequestToJson(
  _UpdateEthccGithubRequest instance,
) => <String, dynamic>{'githubHandle': instance.githubHandle};
