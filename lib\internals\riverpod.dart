import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:me_misc/me_misc.dart';
import 'package:me_utils/me_utils.dart';

import 'methods.dart' show produceReadableError;

ProviderContainer get globalContainer => ProviderScope.containerOf(meContext);

enum RiverpodLoggingWhen {
  loading,
  error,
  updated,
  sameValue,
  disposed,
}

class RiverpodLogger extends ProviderObserver {
  const RiverpodLogger({
    this.loggingWhen = const {
      RiverpodLoggingWhen.loading,
      RiverpodLoggingWhen.error,
      RiverpodLoggingWhen.updated,
    },
  });

  final Set<RiverpodLoggingWhen> loggingWhen;

  static const _tag = '☄️ RiverpodLogger';

  @override
  void didUpdateProvider(
    ProviderBase<Object?> provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    if (newValue == previousValue) {
      if (!loggingWhen.contains(RiverpodLoggingWhen.sameValue)) {
        return;
      }
      return;
    }
    if (newValue case final AsyncValue newValue when newValue.isLoading) {
      if (!loggingWhen.contains(RiverpodLoggingWhen.loading)) {
        return;
      }
      LogUtil.dd(
        () => '${provider.name ?? provider.runtimeType} <= Loading...',
        tag: _tag,
        tagWithTrace: false,
      );
      return;
    }
    if (newValue is AsyncError) {
      if (!loggingWhen.contains(RiverpodLoggingWhen.error)) {
        return;
      }
      LogUtil.e(
        produceReadableError(newValue.error),
        stackTrace: newValue.stackTrace,
        tag: _tag,
        tagWithTrace: false,
        report: false,
      );
    } else {
      if (!loggingWhen.contains(RiverpodLoggingWhen.updated)) {
        return;
      }
      LogUtil.dd(
        () => '${provider.name ?? provider.runtimeType} <= $newValue',
        tag: _tag,
        tagWithTrace: false,
      );
    }
  }

  @override
  void didDisposeProvider(
    ProviderBase<Object?> provider,
    ProviderContainer container,
  ) {
    if (!loggingWhen.contains(RiverpodLoggingWhen.disposed)) {
      return;
    }
    LogUtil.dd(
      () => '${provider.name ?? provider.runtimeType} <= disposed',
      tag: _tag,
    );
  }
}
