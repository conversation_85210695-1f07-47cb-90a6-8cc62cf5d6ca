import 'dart:async' show Completer;

import 'package:collection/collection.dart';
import 'package:dio/dio.dart' show Dio;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '/api/astrox_gw.dart' show AstroxTicker;
import '/constants/envs.dart';
import '/extensions/riverpod_extension.dart';
import '/internals/box.dart' show Boxes;
import '/internals/methods.dart' show handleExceptions;
import '/models/business.dart' show Config, Message, Network, Point, Token;
import '/provider/api.dart' show AstroxPaged, Paged, apiAstroxProvider, apiAstroxGwProvider, apiServiceProvider;

part 'business.g.dart';

// 配置缓存键
const String _configCacheKey = 'card3_config_cache';
// 配置缓存有效期 - 10分钟（毫秒）
const int _configCacheExpiration = 10 * 60 * 1000;

@Riverpod(keepAlive: true)
class ConfigState extends _$ConfigState {
  // 默认配置
  static final _defaultConfigData = Config.fromJson({
    'chainTokenFilters': {
      '1': ['ETH', 'USDC', 'USDT'],
      '8453': ['ETH', 'USDT', 'USDC', 'DEGEN'],
      '137': ['POL', 'USDC', 'USDT'],
      '56': ['BNB', 'BUSD', 'WBNB'],
      '42161': ['ETH', 'USDC', 'USDT'],
      '10': ['ETH', 'USDC', 'USDT'],
      '43114': ['ETH', 'USDC', 'USDT'],
      '59144': ['ETH', 'USDC', 'USDT'],
    },
    'chainFilters': [10, 324, 59144],
    'ethccEventIds': <int>[0],
    'defaultMode': 'ETHCC',
    'nfc': false,
    'events': [],
  });

  @override
  Config build() {
    _loadConfig();
    return _getConfigFromCache() ?? _defaultConfigData;
  }

  Completer<void>? _loadLock;

  Config? _getConfigFromCache() {
    final cachedData = Boxes.cache.get(_configCacheKey);
    if (cachedData case final Map cacheMap) {
      try {
        // 如果缓存未过期
        final timestamp = int.parse(cacheMap['timestamp'].toString());
        if (DateTime.now().millisecondsSinceEpoch - timestamp < _configCacheExpiration) {
          final configData = cacheMap['config'] as Map;
          return Config.fromJson(configData.cast());
        }
      } catch (e, s) {
        handleExceptions(error: e, stackTrace: s);
        return null;
      }
    }
    return null;
  }

  Future<void> _loadConfig() async {
    if (_loadLock case final lock?) {
      return lock.future;
    }
    final lock = _loadLock = Completer<void>();
    Future<void>(() async {
      final dio = Dio();
      dio.options.headers = {
        'User-Agent': 'Card3App/1.0',
        'Accept': 'application/json',
        'Referer': envUrlCard3,
      };

      final response = await dio.get(envUrlConfig);
      if (response.statusCode == 200) {
        final configData = response.data as Map<String, dynamic>;
        final config = Config.fromJson(configData);
        final cachedConfig = _getConfigFromCache();
        await Boxes.cache.put(
          _configCacheKey,
          {
            'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
            'config': configData, // 存储原始数据而不是对象
          },
        );
        if (config != cachedConfig) {
          state = config;
        }
      }
    }).then(lock.complete, onError: lock.completeError).whenComplete(() {
      _loadLock = null;
    });
  }

  Future<void> loadConfig() => _loadConfig();

  /// 获取指定链支持的代币列表
  List<String> getSupportedTokens(String chainId) {
    return state.chainTokenFilters[chainId] ?? [];
  }

  /// 检查链是否被支持
  bool isChainSupported(int chainId) {
    return state.chainFilters.contains(chainId);
  }
}

@Riverpod(keepAlive: true)
Future<List<Network>> fetchNetworks(Ref ref) async {
  final ct = ref.cancelToken();
  ref.onDispose(() {
    ct.cancel();
  });

  final Config config = ref.watch(configStateProvider);
  // ignore: avoid_manual_providers_as_generated_provider_dependency
  final result = await ref.read(apiAstroxProvider).getNetworks(cancelToken: ct);

  // 过滤掉 chainFilters 中的链
  final filteredNetworks = result
      .where((network) => !config.chainFilters.contains(network.id))
      .where((network) => network.network != 'IC')
      .sorted((a, b) => b.weight.compareTo(a.weight));
  return filteredNetworks;
}

@riverpod
Future<AstroxPaged<Token>> fetchTokens(
  Ref ref, {
  required int pageNum,
  required int pageSize,
  required String blockChain,
}) async {
  final result = await ref
      .read(apiAstroxProvider)
      .getTokens(pageNum: pageNum, pageSize: pageSize, blockChain: blockChain);
  return result;
}

@riverpod
Future<Paged<Message>> fetchMessages(
  Ref ref, {
  required int pageNum,
  required int pageSize,
}) async {
  final result = await ref.read(apiServiceProvider).listMessages(pageNum: pageNum, pageSize: pageSize);
  return result;
}

@riverpod
Future<Paged<Point>> fetchPoints(
  Ref ref, {
  required int pageNum,
  required int pageSize,
}) async {
  final result = await ref.read(apiServiceProvider).getPoints(pageNum: pageNum, pageSize: pageSize);
  return result;
}

@riverpod
Future<AstroxTicker> fetchTickersPrice(
  Ref ref, {
  required String symbol,
}) async {
  final result = await ref.read(apiAstroxGwProvider).getPrice(symbol: symbol);
  return result;
}

final configProvider = Provider<Config>((ref) => ref.watch(configStateProvider));
