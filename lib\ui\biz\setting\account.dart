import 'package:card3/exports.dart';
import 'package:flutter/material.dart';

import '/provider/api.dart' show apiServiceProvider;
import '/provider/settings.dart' show selectedIndexProvider;
import '/provider/user.dart' show fetchUserInfoProvider, userRepoProvider;

@FFRoute(name: '/setting/account')
class AccountSecurity extends ConsumerStatefulWidget {
  const AccountSecurity({super.key});

  @override
  ConsumerState<AccountSecurity> createState() => _AccountSecurityState();
}

class _AccountSecurityState extends ConsumerState<AccountSecurity> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final userInfo = ref.watch(fetchUserInfoProvider());
    final email = userInfo.valueOrNull?.userEmail ?? '<EMAIL>';

    return AppScaffold(
      title: 'Account Security',
      bodyPadding: const EdgeInsets.all(16.0),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 删除账户按钮
          GestureDetector(
            onTap: () => _showDeleteConfirmation(context, email),
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Delete Account',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Colors.red,
                      ),
                    ),
                    const Spacer(),
                    if (_isLoading)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmation(
    BuildContext context,
    String email,
  ) async {
    // 显示确认对话框
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'Delete Account?',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Account: $email',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'This action is permanent and cannot be undone. Proceed with caution.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          actions: [
            Row(
              children: [
                // 取消按钮
                Expanded(
                  child: InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Colors.grey[200]!),
                          right: BorderSide(color: Colors.grey[200]!),
                        ),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                // 删除按钮
                Expanded(
                  child: InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                      _deleteAccount();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Colors.grey[200]!),
                        ),
                      ),
                      child: const Text(
                        'Delete',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.red,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
          actionsPadding: EdgeInsets.zero,
          contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
          insetPadding: const EdgeInsets.symmetric(horizontal: 40),
        );
      },
    );
  }

  Future<void> _deleteAccount() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 调用删除账户的API
      await ref.read(apiServiceProvider).deleteUser();

      // 删除成功后，注销用户并跳转到登录页面
      await privyClient.logout();
      ref.read(userRepoProvider.notifier).reset();
      ref.read(selectedIndexProvider.notifier).state = 0;
      meNavigator.removeNamedAndPushAndRemoveUntil(Routes.login.name);

      Card3ToastUtil.showToast(message: ToastMessages.deleted);
    } catch (e) {
      Card3ToastUtil.showToast(message: ToastMessages.deleteAccountFailed);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
