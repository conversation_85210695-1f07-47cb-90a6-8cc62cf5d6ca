import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('en')];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Card3'**
  String get appTitle;

  /// No description provided for @textTakingLongerThanExpected.
  ///
  /// In en, this message translates to:
  /// **'This takes longer than expected... ({time}s)'**
  String textTakingLongerThanExpected(Object time);

  /// No description provided for @titleBioVerify.
  ///
  /// In en, this message translates to:
  /// **'Biometric Verification'**
  String get titleBioVerify;

  /// No description provided for @textBioVerify.
  ///
  /// In en, this message translates to:
  /// **'Set face/fingerprint/iris verification to unlock the app or send assets.'**
  String get textBioVerify;

  /// No description provided for @textPinProceed.
  ///
  /// In en, this message translates to:
  /// **'Verify to proceed.'**
  String get textPinProceed;

  /// No description provided for @textUnlockRestrictPrompt.
  ///
  /// In en, this message translates to:
  /// **'Too many attempts, please try again later'**
  String get textUnlockRestrictPrompt;

  /// No description provided for @labelForgotPinButton.
  ///
  /// In en, this message translates to:
  /// **'Forgot PIN code?'**
  String get labelForgotPinButton;

  /// No description provided for @textIncorrectPinError.
  ///
  /// In en, this message translates to:
  /// **'Incorrect PIN code.'**
  String get textIncorrectPinError;

  /// No description provided for @textWrongPinTimesLeft.
  ///
  /// In en, this message translates to:
  /// **'{time} attempts remaining.'**
  String textWrongPinTimesLeft(Object time);

  /// No description provided for @textWrongPinLastTimesLeft.
  ///
  /// In en, this message translates to:
  /// **'If incorrect, you can only try again after {timerCount}.'**
  String textWrongPinLastTimesLeft(Object timerCount);

  /// No description provided for @textPinUnlock.
  ///
  /// In en, this message translates to:
  /// **'Verify to unlock your wallet.'**
  String get textPinUnlock;

  /// No description provided for @labelNavDiscovery.
  ///
  /// In en, this message translates to:
  /// **'Discovery'**
  String get labelNavDiscovery;

  /// No description provided for @labelNavSocial.
  ///
  /// In en, this message translates to:
  /// **'Social'**
  String get labelNavSocial;

  /// No description provided for @labelNavWallet.
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get labelNavWallet;

  /// No description provided for @titleQrCodeInvalidPrompt.
  ///
  /// In en, this message translates to:
  /// **'Invalid QR Code'**
  String get titleQrCodeInvalidPrompt;

  /// No description provided for @textQrCodeInvalidPrompt.
  ///
  /// In en, this message translates to:
  /// **'Please try scanning another QR code'**
  String get textQrCodeInvalidPrompt;

  /// No description provided for @textQrCodeErrorResolve.
  ///
  /// In en, this message translates to:
  /// **'Aim at the QR code or adjust the distance.'**
  String get textQrCodeErrorResolve;

  /// No description provided for @textQrCodeErrorInvalid.
  ///
  /// In en, this message translates to:
  /// **'QR code is not supported.'**
  String get textQrCodeErrorInvalid;

  /// No description provided for @textQrCodeNotFound.
  ///
  /// In en, this message translates to:
  /// **'QR code not found'**
  String get textQrCodeNotFound;

  /// No description provided for @labelSearchHint.
  ///
  /// In en, this message translates to:
  /// **'Search CA/Token'**
  String get labelSearchHint;

  /// No description provided for @textLiveCACount.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =1{1 CA} other{{count} CAs}}'**
  String textLiveCACount(int count);

  /// No description provided for @textLiveCAMentioned.
  ///
  /// In en, this message translates to:
  /// **'mentioned'**
  String get textLiveCAMentioned;

  /// No description provided for @textChainNotYetSupported.
  ///
  /// In en, this message translates to:
  /// **'{chain} is not yet supported.'**
  String textChainNotYetSupported(Object chain);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
