import 'package:flutter/material.dart';
import 'package:me_constants/me_constants.dart' show METhemeBuildContextExtension;

class AdditionalCounter extends StatelessWidget {
  const AdditionalCounter({
    super.key,
    required this.count,
  });

  final int count;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 4.0,
        vertical: 2.0,
      ),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(999999)),
        color: Theme.of(context).canvasColor,
      ),
      child: Text(
        '+$count',
        style: TextStyle(
          color: context.themeColor,
          fontSize: 10.0,
          fontWeight: FontWeight.bold,
          height: 1.0,
        ),
      ),
    );
  }
}
