import 'package:flutter/material.dart';

import '../../res/assets.gen.dart' show Assets;

class AppBackButton extends StatelessWidget {
  const AppBackButton({super.key, this.onPressed});

  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () {
        if (onPressed != null) {
          onPressed!();
        } else {
          Navigator.of(context).maybePop();
        }
      },
      icon: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: const Color(0xff4a4a4f),
          borderRadius: BorderRadius.circular(8.0),
        ),
        child: Assets.icons.back.svg(width: 14.0, height: 14.0),
      ),
    );
  }
}
